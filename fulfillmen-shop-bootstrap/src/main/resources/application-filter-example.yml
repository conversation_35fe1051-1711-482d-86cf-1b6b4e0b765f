# 过滤器统一配置示例
# 将此配置复制到 application.yml 或对应环境的配置文件中

fulfillmen:
  filter:
    # 租户过滤器配置
    tenant:
      enabled: true                    # 是否启用租户过滤器
      order: -**********              # 过滤器优先级（HIGHEST_PRECEDENCE + 10）
      url-patterns:                   # URL 匹配模式
        - "/*"
      exclude-patterns:               # 忽略的 URL 模式
        - "/error"
        - "/error/**"
        - "/health"
        - "/health/**"
        - "/actuator/**"
        - "/swagger-ui/**"
        - "/v3/api-docs/**"
        - "/webjars/**"
        - "/favicon.ico"
        - "/static/**"
        - "/public/**"
        - "/assets/**"
        - "/*.js"
        - "/*.css"
        - "/*.png"
        - "/*.jpg"
        - "/*.jpeg"
        - "/*.gif"
        - "/*.ico"
        - "/*.svg"

    # MDC 过滤器配置
    mdc:
      enabled: true                   # 是否启用 MDC 过滤器
      order: -**********             # 过滤器优先级（HIGHEST_PRECEDENCE + 20）
      url-patterns:                  # URL 匹配模式
        - "/*"
      exclude-patterns:              # 忽略的 URL 模式（同租户过滤器）
        - "/error"
        - "/error/**"
        - "/health"
        - "/health/**"
        - "/actuator/**"
        - "/swagger-ui/**"
        - "/v3/api-docs/**"
        - "/webjars/**"
        - "/favicon.ico"
        - "/static/**"
        - "/public/**"
        - "/assets/**"
        - "/*.js"
        - "/*.css"
        - "/*.png"
        - "/*.jpg"
        - "/*.jpeg"
        - "/*.gif"
        - "/*.ico"
        - "/*.svg"
      normalize-ip-address: true     # 是否标准化 IP 地址（IPv6 -> IPv4）
      include-browser-info: true     # 是否包含浏览器信息
      include-os-info: true          # 是否包含操作系统信息

# 过滤器优先级说明：
# 1. TRACE_FILTER     (-**********) - 链路跟踪过滤器，生成 TraceId
# 2. TENANT_FILTER    (-**********) - 租户过滤器，设置租户上下文
# 3. MDC_FILTER       (-**********) - MDC 过滤器，设置日志上下文
# 4. XSS_FILTER       (-**********) - XSS 过滤器，安全防护
# 5. CORS_FILTER      (-**********) - CORS 过滤器，跨域处理
# 6. LOG_FILTER       (2147483637)  - 日志过滤器，记录请求响应

# 环境特定配置示例：
---
# 开发环境配置
spring:
  profiles: dev
fulfillmen:
  filter:
    mdc:
      include-browser-info: true     # 开发环境记录详细信息
      include-os-info: true

---
# 生产环境配置
spring:
  profiles: prod
fulfillmen:
  filter:
    mdc:
      include-browser-info: false    # 生产环境减少信息记录
      include-os-info: false
