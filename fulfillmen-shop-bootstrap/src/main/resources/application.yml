---
### 项目配置
project:
  # 名称
  name: Fulfillmen Shop
  # 应用名称
  app-name: fulfillmen-shop
  # 版本
  version: 1.0.0
  # 描述
  description: fulfillmen 采购系统
  # 基本包
  base-package: com.fulfillmen.shop
  url: https://www.fulfillmen.com
  copyright: Copyright © 2024-present ${project.name} All rights reserved
  ## 作者信息配置
  contact:
    name: <PERSON>
    email: <PERSON>@fulfillmen.com
    url: https://www.fulfillmen.com
--- ### 接口文档配置
springdoc:
  # 设置对象型参数的展示形式（设为 true 表示将对象型参数平展开，即对象内的属性直接作为参数展示而不是嵌套在对象内，默认 false）
  # 如果不添加该全局配置，可以在需要如此处理的对象参数类上使用 @ParameterObject
  default-flat-param-object: true
  # 分组配置
  group-configs:
    - group: all
      paths-to-match: /**
      paths-to-exclude:
        - /error
    - group: auth
      display-name: 系统认证
      packages-to-scan: ${project.base-package}.controller
    - group: ERP-API
      display-name: ERP
      packages-to-scan: ${project.base-package}.frontend
    - group: openapi
      display-name: 开放平台接口
      packages-to-scan: ${project.base-package}.openapi

  #        -   group: monitor
  #            display-name: 系统监控
  #            packages-to-scan: ${project.base-package}.controller.monitor
  #        -   group: open
  #            display-name: 能力开放
  #            packages-to-scan: ${project.base-package}.controller.open
  ## 组件配置
  components:
    # 鉴权配置
    security-schemes:
      Authorization:
        type: HTTP
        in: HEADER
        name: ${sa-token.token-name}
        scheme: ${sa-token.token-prefix}

## 接口文档增强配置
knife4j:
  enable: true
  # 开启密码验证
  basic:
    enable: true
    username: fulfillmen
    password: Jv3+tB(vs8Yx8e
  setting:
    language: zh_cn
    swagger-model-name: 实体类列表
    # 是否显示默认的 footer（默认 true，显示）
    enable-footer: true
    # 是否自定义 footer（默认 false，非自定义）
    enable-footer-custom: true
    # 自定义 footer 内容，支持 Markdown 语法
    footer-custom-content: "Copyright © 2024-present [Fulfillmen Team](www.fulfillmen.com)&nbsp;⋅&nbsp;[${project.name}](${project.url}) v${project.version}"
---
### Spring 配置
spring:
  application:
    name: ${project.app-name}
  profiles:
    active: dev
    group:
      dev: dev
      test: test
      prod: prod
  datasource:
    # 数据源 默认配置
    hikari:
      # 最大连接数量（默认 10，根据实际环境调整）
      # 注意：当连接达到上限，并且没有空闲连接可用时，获取连接将在超时前阻塞最多 connectionTimeout 毫秒
      maximum-pool-size: 30
      # 初始连接数量（默认 10，根据实际环境调整）
      # 获取连接超时时间（默认 2000 毫秒，3 秒）
      connection-timeout: 3000
      # 空闲连接最大存活时间（默认 30000 毫秒，30 秒）
      idle-timeout: 30000
      # 保持连接活动的频率，以防止它被数据库或网络基础设施超时。该值必须小于 maxLifetime（默认 0，禁用） 25 秒
      keepaliveTime: 25000
      # 连接最大生存时间（默认 1800000 毫秒，30 分钟）
      max-lifetime: 1080000
      # 最小空闲连接数（默认 10，根据实际环境调整）
      minimum-idle: 10
      # 验证超时时间（默认 5000 毫秒，5 秒）
  #      validation-timeout: 5000
  #      # 连接测试查询
  #      connection-test-query: SELECT 1
  # 参考代码国际化配置 LocaleConfig
  #  messages:
  #    basename: i18n/messages,i18n/openapi-message,i18n/ValidationMessages
  #    encoding: UTF-8
  #    fallback-to-system-locale: true
  #    use-code-as-default-message: true
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    locale: en_US
    # 序列化配置
    serialization:
      write-dates-as-timestamps: false
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  ## 线程池配置（默认启用扩展配置，如未指定 corePoolSize、maxPoolSize 则根据机器配置自动设置）
  task:
    # 异步任务
    execution:
      thread-name-prefix: fulfillmen-task-pool
      # 任务拒绝策略（默认 ABORT，不执行新任务，直接抛出 RejectedExecutionException 异常）
      # CALLER_RUNS：提交的任务在执行被拒绝时，会由提交任务的线程去执行
      rejected-policy: CALLER_RUNS
      pool:
        keep-alive: 60s
      shutdown:
        # 是否等待任务执行完成再关闭线程池（默认 false）
        await-termination: true
        # 等待时间
        await-termination-period: 30s
  web:
    resources:
      static-locations:
        - classpath:/static/
        - classpath:/public/
      chain:
        strategy:
          content:
            enabled: true
            paths: /**
  mvc:
    static-path-pattern: /**
    view:
      prefix: /
      suffix: .html
  servlet:
    multipart:
      enabled: true
      # 单个文件上传大小限制
      max-file-size: 10MB
      # 单次总上传文件大小限制
      max-request-size: 20MB
  ### 邮件配置 通用
  mail:
    # 根据需要更换
    host: smtppro.zoho.in
    port: 465
    username: <EMAIL>
    password: QvYagucqtd6r
    properties:
      mail:
        smtp:
          auth: true
          socketFactory:
            class: javax.net.ssl.SSLSocketFactory
            port: 465
--- ### 服务器配置
server:
  compression:
    enabled: true
    mime-types:
      - text/html
      - text/xml
      - text/plain
      - text/css
      - text/javascript
      - application/javascript
      - application/json
      - application/xml
    min-response-size: 1024
  port: 8080
  servlet:
    # 应用访问路径
    context-path: /
  # 服务器关闭方式
  shutdown: graceful
  ## Undertow 服务器配置
  undertow:
    # HTTP POST 请求内容的大小上限（默认 -1，不限制）
    max-http-post-size: -1
    # 以下的配置会影响 buffer，这些 buffer 会用于服务器连接的 IO 操作，有点类似 Netty 的池化内存管理
    # 每块 buffer的空间大小（越小的空间被利用越充分，不要设置太大，以免影响其他应用，合适即可）
    buffer-size: 512
    # 是否分配的直接内存（NIO 直接分配的堆外内存）
    direct-buffers: true
    threads:
      # 设置 IO 线程数，它主要执行非阻塞的任务，它们会负责多个连接（默认每个 CPU 核心一个线程）
      io: 8
      # 阻塞任务线程池，当执行类似 Servlet 请求阻塞操作，Undertow 会从这个线程池中取得线程（它的值设置取决于系统的负载）
      worker: 256
### 短信配置
sms:
  # 从 YAML 读取配置
  config-type: YAML
  http-log: true
  is-print: false
#    blends:
#        cloopen:
#            # 短信厂商
#            supplier: cloopen
#            base-url: https://app.cloopen.com:8883/2013-12-26
#            access-key-id: 你的Access Key
#            access-key-secret: 你的Access Key Secret
#            sdk-app-id: 你的应用ID

## 项目日志配置
logging:
  config: classpath:logback-spring.xml
  file:
    path: ./logs
--- ### 项目初始配置
fulfillmen:
  # 接口限流配置
  rate-limit:
    default-config:
      rate: 100
      window: 60
    # 全局拦截器是否启用
    global-interceptor-enabled: true
--- ### 数据源 通用配置
spring.datasource:
  # Hikari 连接池配置（完整配置请参阅：https://github.com/brettwooldridge/HikariCP）
  hikari:
    # 最小空闲连接数（默认 10，根据实际环境调整）
    minimum-idle: 15
    # 最大连接数量（默认 10，根据实际环境调整）
    # 注意：当连接达到上限，并且没有空闲连接可用时，获取连接将在超时前阻塞最多 connectionTimeout 毫秒
    maximum-pool-size: 50
    # 获取连接超时时间（默认 20 秒，单位毫秒）
    connection-timeout: 20000
    # 空闲连接最大存活时间（默认 300000 ms，5 分钟）
    idle-timeout: 300000
    # 保持连接活动的频率，以防止它被数据库或网络基础设施超时。该值必须小于 maxLifetime（默认 0，禁用）
    keepaliveTime: 30000
    # 连接最大生存时间（默认 600000 ms，10 分钟）
    max-lifetime: 600000
---
fulfillmen-starter:
  web:
    ### 链路跟踪配置
    trace:
      enabled: true
      trace-id-name: traceId
      ## TLog 配置
      tlog:
        enable-invoke-time-print: false
        pattern: "[$traceId]:[$spanId]"
        mdc-enable: false
    ### 全局响应配置
    response:
      # 是否开启国际化（默认：false）
      i18n: false
      # 响应类全名（配置后 response-style 将不再生效）
      response-class-full-name: "com.fulfillmen.starter.web.model.R"
      # 自定义失败 HTTP 状态码（默认：200，建议业务和通信状态码区分）
      default-http-status-code-on-error: 200
      # 自定义成功响应码（默认：0）
      default-success-code: "0"
      # 自定义成功提示（默认：ok）
      default-success-msg: "ok"
      # 自定义失败响应码（默认：1）
      default-error-code: "-1"
      # 自定义失败提示（默认：error）
      default-error-msg: error
      # 是否打印异常日志（默认：false）
      print-exception-in-global-advice: true
      # 是否将原生异常错误信息填充到状态信息中（默认：false）
      # origin-exception-using-detail-message: true
      # 例外包路径（支持数字, * 和 ** 通配符匹配），该包路径下的 Controller 将被忽略处理
      exclude-packages:
        - io.swagger.**
        - org.springdoc.**
        - org.springframework.boot.actuate.*
  ### 日志配置
  log:
    # 是否打印日志，开启后可打印访问日志（类似于 Nginx access log）
    is-print: true
    # 包含信息
    includes:
      - DESCRIPTION
      - MODULE
      - REQUEST_HEADERS
      - REQUEST_BODY
      - IP_ADDRESS
      - BROWSER
      - OS
      - RESPONSE_HEADERS
      - RESPONSE_BODY
    # 排除拦截日志信息
    exclude-patterns:
      - "*.js"
      - "*.css"
      - "*.png"
      - "*.jpg"
      - "*.html"
      - /**.css
      - /**.html
      - /**.js
  ### 验证码配置
  captcha:
    ## 行为验证码
    behavior:
      enabled: true
      cache-type: REDIS
      water-mark:
      # 一分钟内接口请求次数限制开关（默认：0，关闭，开启后下方失败锁定配置才会生效）
      req-frequency-limit-enable: 0
      # 一分钟内验证码最多失败次数限制（默认：5次）
      req-get-lock-limit: 5
      # 一分钟内验证码最多失败次数限制达标后锁定时间（默认：300秒）
      req-get-lock-seconds: 300
    ## 图形验证码
    graphic:
      # 类型
      type: SPEC
      # 内容长度
      length: 4
      # 过期时间 (单位：分钟)
      expirationInMinutes: 2
  ## 安全配置
  security:
    ## 字段加/解密配置
    crypto:
      enabled: true
      # 对称加密算法密钥
      password: fulfillmensecret20250423
      # 非对称加密算法密钥（在线生成 RSA 密钥对：http://web.chacuo.net/netrsakeypair）
      asymmetric-type: FILE
      public-key-file: ${SECURITY_CRYPTO_PUBLIC_KEY_FILE:classpath:public.key}
      private-key-file: ${SECURITY_CRYPTO_PRIVATE_KEY_FILE:classpath:private.key}
    ## 密码编码器配置
    password:
      enabled: true
      # BCryptPasswordEncoder
      encoding-id: bcrypt
--- ### Sa-Token 配置
sa-token:
  # 指定 token 提交时的前缀
  token-prefix: "Bearer"
  # token 名称（同时也是 cookie 名称）
  token-name: Authorization
  # token 有效期（单位：秒，默认 30 天，-1 代表永不过期） 24h
  timeout: 86400
  # token 最低活跃频率 12h（单位：秒，默认 -1，代表不限制，永不冻结。如果 token 超过此时间没有访问系统就会被冻结）
  active-timeout: 43200
  # 是否打开自动续签（如果此值为 true，框架会在每次直接或间接调用 getLoginId() 时进行一次过期检查与续签操作）
  auto-renew: true
  # 是否允许同一账号多地同时登录（为 true 时允许一起登录，为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token（为 true 时所有登录共用一个 token，为 false 时每次登录新建一个 token）
  is-share: false
  # 是否输出操作日志
  is-log: false
  # JWT 秘钥
  jwt-secret-key: fulfillmenShopSecretKey@asdadh123kljhgfdsa
  ## 扩展配置
  extension:
    enabled: true
    enableJwt: true
    # 持久层配置
    dao:
      type: REDIS
    # 安全配置
    # 安全配置：排除（放行）路径配置
    security.excludes:
      - /error
      # 静态资源
      - /*.html
      - /*.htm
      - /*.css
      - /**.css
      - /*.js
      - /**.js
      - /websocket/**
      - /*.svg
      - /**.svg
      # 接口文档相关资源
      - /favicon.ico
      - /doc.html
      - /index.html
      - /index.htm
      - /webjars/**
      - /assets/**
      - /static/**
      - /public/**
      - /swagger-ui/**
      - /swagger-resources/**
      - /*/api-docs/**
      # 监控端点
      - /actuator/**
      # 本地存储资源
      - /file/**
      # 鉴权
      - /auth/**
      - /oauth/**
      - /oauth2/**
      # 开放平台接口 使用 sign 签名
      - /openapi/**
      - /api/test/**
  sign-many:
    # 提供 shopper 接口秘钥
    shopper:
      # nayasource 密码 api
      secret-key: U2FsdGVkX18jq9VHhv8oZcFO88UHzxEacXxCK4YL0CIXnztVCi0GwuOA87M0dmH9
  sign:
    secret-key: U2FsdGVkX18jq9VHhv8oZcFO88UHzxEacXxCK4YL0CIXnztVCi0GwuOA87M0dmH9-123
---
## 其他验证码配置
captcha:
  expirationInMinutes: 2
  ## 邮箱验证码配置
  mail:
    # 内容长度
    length: 6
    # 过期时间
    expirationInMinutes: 5
    # 模板路径
    templatePath: mail/captcha.ftl
    activation-template-path: mail/activation.ftl
    activation-base-url: ${project.url}
    activation-expirationInHours: 24
  ## 短信验证码配置
  sms:
    # 内容长度
    length: 4
    # 过期时间
    expirationInMinutes: 5
    # 模板 ID
    templateId: 1
---
## JetCache 配置
jetcache:
  # 统计间隔（默认 0，表示不统计）
  statIntervalMinutes: 15
  hidePackage: com.fulfillmen.shop
  ## 本地/进程级/一级缓存配置
  local:
    default:
      # 缓存类型
      type: caffeine
      # key 转换器的全局配置
      keyConvertor: jackson
      valueConvertor: jackson
      # 以毫秒为单位指定超时时间的全局配置
      expireAfterWriteInMillis: 7200000
      # 每个缓存实例的最大元素的全局配置，仅 local 类型的缓存需要指定
      limit: 1000
  ## 远程/分布式/二级缓存配置
  remote:
    default:
      # 默认 key- 远程前缀
      keyPrefix: "shop:"
      # 缓存类型
      type: redisson
      # key 转换器的全局配置（用于将复杂的 KEY 类型转换为缓存实现可以接受的类型）
      keyConvertor: jackson
      valueConvertor: jackson
      # 以毫秒为单位指定超时时间的全局配置
      expireAfterWriteInMillis: 7200000
      # 2.7+ 支持两级缓存更新以后失效其他 JVM 中的 local cache，但多个服务共用 Redis 同一个 channel 可能会造成广播风暴，需要在这里指定channel。
      # 你可以决定多个不同的服务是否共用同一个 channel，如果没有指定则不开启。
      broadcastChannel: ${spring.application.name}
      # 序列化器的全局配置，仅 remote 类型的缓存需要指定
      valueEncoder: kryo5
      valueDecoder: kryo5
---
## CosId 配置
cosid:
  namespace: ${spring.application.name}
  machine:
    enabled: true
    # 机器号位数

    # 机器号分配器
    distributor:
      type: REDIS
    guarder:
      # 开启机器号守护
      enabled: true
  generator:
    enabled: true
#    sequence-bit: 8
    type: RADIX36
  snowflake:
    enabled: true
    zone-id: Asia/Shanghai
    machine:
      distributor:
        type: redis
    provider:
      safe-js:
        machine-bit: 3
        sequence-bit: 9
      snowflake_friendly_second:
        timestamp-unit: second
        epoch: **********
        timestamp-bit: 31
#        machine-bit: 10
#        sequence-bit: 12
        converter:
          type: snowflake_friendly
    share:
      converter:
        friendly:
          pad-start: true
  segment:
    enabled: true # 可选，当需要使用号段算法时，需要设置为 true
    share:
      enabled: false
    distributor:
      type: redis
    provider:
      user_no:
        offset: 1000000
        converter:
          type: radix36
          radix:
            char-size: 5
            pad-start: true
      # 订单编号
      # 创建日期编号+段号 - via: 25070316582391557
      order_no:
        converter:
          type: to_string
          date-prefix:
            enabled: true
            delimiter: ""
            pattern: yyMMddHHmmssSSS
---
### MyBatis Plus 配置
mybatis-plus:
  # Mapper XML 文件目录配置
  mapper-locations: classpath*:/mapper/**/*Mapper.xml
  # 类型别名扫描包配置
  type-aliases-package: ${project.base-package}.**.model
  ## MyBatis 配置
  configuration:
    # MyBatis 自动映射策略
    # NONE：不启用 PARTIAL：只对非嵌套 resultMap 自动映射 FULL：对所有 resultMap 自动映射
    auto-mapping-behavior: PARTIAL
  ## 全局配置
  global-config:
    banner: false
    db-config:
      # 主键类型（默认 assign_id，表示自行赋值）
      # auto 代表使用数据库自增策略（需要在表中设置好自增约束）
      id-type: ASSIGN_ID
      # 逻辑删除字段
      logic-delete-field: isDeleted
      # 逻辑删除全局值（默认 id，表示已删除）
      logic-delete-value: id
      # 逻辑未删除全局值（默认 0，表示未删除）
      logic-not-delete-value: 0
  ## 扩展配置
  extension:
    enabled: true
    # Mapper 接口扫描包配置
    mapper-package: ${project.base-package}.**.mapper
    # 开启乐观锁
    optimisticLockerEnabled: true
    # ID 生成器配置
    id-generator:
      type: COSID
    # 分页插件配置
    pagination:
      enabled: true
      db-type: MYSQL
justauth:
  enabled: false

---
## 阿里巴巴的相关配置
alibaba:
  # 支付宝相关配置
  alipay:
    # 支付宝应用ID,默认为沙箱环境的APPID
    app-id: ${ALIPAY_APP_ID:2016101300670001}
    # 支付宝网关地址,默认为正式环境地址
    gateway-url: ${ALIPAY_GATEWAY_URL:https://openapi.alipay.com/gateway.do}
  # 1688开放平台相关配置
  open1688:
    # 1688应用的AppKey
    app-key: ${ALIBABA_1688_APP_KEY:8390330}
    # 1688应用的密钥
    secret-key: ${ALIBABA_1688_SECRET_KEY:3h27HVrZKU}
    # 1688开放平台网关地址
    gateway-url: ${ALIBABA_1688_GATEWAY_URL:https://gw.open.1688.com/openapi}
    # 1688访问令牌,用于调用API时的身份认证
    access-token: ${ALIBABA_1688_ACCESS_TOKEN:b49dfbf3-c3d7-4dfa-9b50-2d3300bad6c2}
---
## 监控配置 actuator 端点配置
management:
  server:
    port: 8099
  ### 健康检查配置
  health:
    mail:
      # 关闭邮箱健康检查（邮箱配置错误或邮箱服务器不可用时，健康检查会报错）
      enabled: false
    db:
      enabled: true
    redis:
      enabled: true
    # 限流器健康检查
    ratelimiters:
      enabled: true
  metrics:
    tags:
      # 应用名称标签
      application: ${spring.application.name}
    distribution:
      # 直方图配置，用于统计请求分布情况
      percentiles-histogram:
        http.server.requests: true
        # API调用持续时间分布
        alibaba.api.duration: true
        # API响应时间分布
        alibaba.api.response.time: true
        # API并发请求数分布
        alibaba.api.concurrent.requests: true
      # SLA目标配置，定义关键性能指标的目标值
      sla:
        # API调用持续时间的SLA目标
        alibaba.api.duration: 100ms,500ms,1s,5s
        # API响应时间的SLA目标
        alibaba.api.response.time: 100ms,500ms,1s,5s
      # 分位数配置，用于统计性能分布
      percentiles:
        # API响应时间的分位数统计点
        # 0.5 - 中位数
        # 0.75 - 75%的请求性能
        # 0.95 - 95%的请求性能
        # 0.99 - 99%的请求性能
        alibaba.api.response.time: 0.5,0.75,0.95,0.99
  prometheus:
    metrics:
      export:
        enabled: true
  endpoint:
    health:
      show-details: always
    httpexchanges:
      cache:
        time-to-live: 1m
  endpoints:
    web:
      exposure:
        include: health, info, beans, env, conditions, configprops, loggers, metrics, shutdown, prometheus, heapdump, cosid, cosidGenerator, cosidStringGenerator

---
