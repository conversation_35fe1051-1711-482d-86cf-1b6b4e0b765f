/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.config.tenant;

import com.fulfillmen.shop.common.tenant.EnhancedTenantContext;
import com.fulfillmen.shop.common.tenant.EnhancedTenantContextHolder;
import com.fulfillmen.shop.common.tenant.TenantCacheService;
import com.fulfillmen.shop.manager.service.TenantDataLoader;
import com.fulfillmen.shop.manager.service.TenantResolverService;
import com.fulfillmen.starter.data.mp.tenant.DefaultTenantContext;
import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
// import jakarta.servlet.annotation.WebFilter; // 不使用注解注册，改为通过FilterRegistrationBean注册
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;

/**
 * 租户过滤器
 * 在Servlet容器级别处理租户上下文的设置和清理
 * 相比拦截器，过滤器执行更早，能处理所有HTTP请求包括静态资源
 *
 * <AUTHOR>
 * @date 2025/7/3 11:00
 * @description: 租户请求过滤器，负责租户上下文的生命周期管理
 * @since 1.0.0
 */
@Slf4j
@Component
// 通过FilterRegistrationBean注册，不使用@WebFilter注解
public class TenantFilter implements Filter {

    /**
     * 忽略租户处理的URL模式
     */
    private static final Set<String> IGNORED_URL_PATTERNS = new HashSet<>(Arrays.asList(
        "/error", "/error/**",
        "/health", "/health/**",
        "/actuator/**",
        "/swagger-ui/**",
        "/v3/api-docs/**",
        "/webjars/**",
        "/favicon.ico",
        "/static/**",
        "/public/**",
        "/assets/**",
        "/*.js", "/*.css", "/*.png", "/*.jpg", "/*.jpeg", "/*.gif", "/*.ico", "/*.svg"
    ));

    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    @Autowired
    private TenantResolverService tenantResolverService;

    @Autowired
    private TenantCacheService tenantCacheService;

    @Autowired
    private TenantDataLoader tenantDataLoader;

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {

        if (!(request instanceof HttpServletRequest httpRequest) ||
            !(response instanceof HttpServletResponse httpResponse)) {
            chain.doFilter(request, response);
            return;
        }

        // 检查是否需要忽略租户处理
        if (shouldIgnoreRequest(httpRequest)) {
            log.debug("忽略租户处理的请求: {}", httpRequest.getRequestURI());
            chain.doFilter(request, response);
            return;
        }

        try {
            // 解析并设置租户上下文
            setupTenantContext(httpRequest);

            // 继续过滤器链
            chain.doFilter(request, response);

        } catch (Exception e) {
            log.error("租户过滤器处理异常: {}", e.getMessage(), e);
            // 即使出现异常，也要继续过滤器链，避免影响正常请求
            chain.doFilter(request, response);
        } finally {
            // 清理租户上下文
            clearTenantContext();
        }
    }

    /**
     * 设置租户上下文
     *
     * @param request HTTP请求
     */
    private void setupTenantContext(HttpServletRequest request) {
        try {
            // 使用统一的租户解析服务获取租户ID
            String tenantId = tenantResolverService.resolveTenantId(request);

            if (tenantId != null) {
                // 设置基本的租户ID到 ThreadLocal
                DefaultTenantContext.setTenantId(tenantId);
                MDC.put("tenantId", tenantId);

                // 加载并缓存增强的租户上下文
                loadAndCacheEnhancedTenantContext(tenantId);

                log.debug("过滤器设置当前请求的租户ID: {} (URI: {})", tenantId, request.getRequestURI());
            } else {
                log.debug("过滤器未找到有效的租户ID，请求将不进行租户隔离 (URI: {})", request.getRequestURI());
            }
        } catch (Exception e) {
            log.error("设置租户上下文时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 加载并缓存增强的租户上下文
     *
     * @param tenantId 租户ID
     */
    private void loadAndCacheEnhancedTenantContext(String tenantId) {
        try {
            // 1. 尝试从缓存获取租户上下文
            Optional<EnhancedTenantContext> cachedContext = tenantCacheService.getTenantContext(tenantId);

            if (cachedContext.isPresent()) {
                EnhancedTenantContext context = cachedContext.get();

                // 检查是否需要刷新缓存（15分钟策略）
                if (shouldRefreshCache(context)) {
                    // 刷新缓存过期时间
                    tenantCacheService.refreshTenantContext(tenantId);

                    // 更新访问统计
                    updateAccessStatistics(context);

                    log.debug("缓存命中并刷新: 租户ID={}, 缓存创建时间={}", tenantId, context.getCacheMetadata().getCacheCreatedTime());
                } else {
                    // 只更新访问统计，不刷新缓存
                    updateAccessStatistics(context);
                    log.debug("缓存命中: 租户ID={}, 访问次数={}", tenantId, context.getCacheMetadata().getAccessCount());
                }

                // 将增强的租户上下文存储到 ThreadLocal 中
                EnhancedTenantContextHolder.setEnhancedTenantContext(context);

            } else {
                // 2. 缓存未命中，从数据库加载
                log.debug("缓存未命中，从数据库加载租户数据: {}", tenantId);
                EnhancedTenantContext context = tenantDataLoader.loadTenantContext(tenantId);

                if (context != null) {
                    // 存储到缓存
                    tenantCacheService.setTenantContext(tenantId, context);

                    // 将增强的租户上下文存储到 ThreadLocal 中
                    EnhancedTenantContextHolder.setEnhancedTenantContext(context);

                    log.debug("数据库加载成功并缓存: 租户ID={}, 租户名称={}", tenantId, context.getTenantName());
                } else {
                    log.warn("租户数据加载失败，租户ID可能无效: {}", tenantId);
                }
            }

        } catch (Exception e) {
            log.error("加载增强租户上下文时发生异常: tenantId={}, error={}", tenantId, e.getMessage(), e);
            // 即使加载失败，也不影响基本的租户ID功能
        }
    }

    /**
     * 检查是否需要刷新缓存
     *
     * @param context 租户上下文
     * @return 如果需要刷新返回true
     */
    private boolean shouldRefreshCache(EnhancedTenantContext context) {
        if (context.getCacheMetadata() == null || context.getCacheMetadata().getLastAccessTime() == null) {
            return true;
        }

        LocalDateTime lastAccessTime = context.getCacheMetadata().getLastAccessTime();
        LocalDateTime now = LocalDateTime.now();

        // 计算距离上次访问的时间
        Duration duration = Duration.between(lastAccessTime, now);

        // 如果超过15分钟，需要刷新缓存
        return duration.toMinutes() >= TenantCacheService.CACHE_REFRESH_THRESHOLD_MINUTES;
    }

    /**
     * 更新访问统计
     *
     * @param context 租户上下文
     */
    private void updateAccessStatistics(EnhancedTenantContext context) {
        if (context.getCacheMetadata() != null) {
            context.getCacheMetadata().setLastAccessTime(LocalDateTime.now());
            context.getCacheMetadata().setAccessCount(
                context.getCacheMetadata().getAccessCount() + 1
            );
        }
    }

    /**
     * 清理租户上下文
     */
    private void clearTenantContext() {
        try {
            // 清理基本租户上下文
            DefaultTenantContext.clear();

            // 清理增强租户上下文
            EnhancedTenantContextHolder.clear();

            // 清理 MDC
            MDC.remove("tenantId");

            log.debug("过滤器清理租户上下文完成");
        } catch (Exception e) {
            log.error("清理租户上下文时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 检查是否应该忽略租户处理
     *
     * @param request HTTP请求
     * @return 如果应该忽略返回true，否则返回false
     */
    private boolean shouldIgnoreRequest(HttpServletRequest request) {
        String requestURI = request.getRequestURI();

        // 检查是否匹配忽略的URL模式
        return IGNORED_URL_PATTERNS.stream().anyMatch(pattern -> pathMatcher.match(pattern, requestURI));
    }
}
