/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.config;

import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import com.fulfillmen.shop.config.satoken.SaExtensionInterceptor;
import com.fulfillmen.shop.openapi.intercept.OpenapiInterceptor;
import com.fulfillmen.shop.openapi.service.OpenapiAccountService;
import com.fulfillmen.starter.auth.satoken.autoconfigure.SaTokenExtensionProperties;
import com.fulfillmen.starter.core.constant.StringConstants;
import jakarta.annotation.PostConstruct;
import jakarta.validation.MessageInterpolator;
import jakarta.validation.Validator;
import java.util.List;
import java.util.Locale;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.messageinterpolation.ResourceBundleMessageInterpolator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.env.Environment;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Fulfillmen Web MVC 配置
 *
 * <AUTHOR>
 * @date 2025/2/27 19:11
 * @description: todo
 * @since 1.0.0
 */
@Slf4j
@Configuration
public class FulfillmenWebMvcConfig implements WebMvcConfigurer {

    /**
     * 排除 api 列表
     */
    private static final List<String> OPENAPI_PATH = List.of("/openapi/**");

    /**
     * 国际化消息源列表
     */
    private static final String[] I18N_MESSAGE_LIST = new String[]{"i18n/messages", "i18n/openapi-message",
        "i18n/ValidationMessages"};

    private final SaTokenExtensionProperties properties;

    @Autowired(required = false)
    private OpenapiAccountService openapiAccountService;

    // 租户处理已移至过滤器级别，不再需要拦截器
    // @Autowired
    // private TenantInterceptor tenantInterceptor;

    @Autowired
    private Environment environment;

    public FulfillmenWebMvcConfig(SaTokenExtensionProperties properties) {
        this.properties = properties;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 拦截器实现 openapi 鉴权和签名
        if (openapiAccountService != null) {
            registry.addInterceptor(new OpenapiInterceptor(openapiAccountService)).addPathPatterns(OPENAPI_PATH);
        } else {
            log.warn("OpenapiAccountService not found, OpenAPI interceptor will be disabled");
        }
        // 默认处理逻辑
        registry.addInterceptor(new SaExtensionInterceptor(handle -> SaRouter.match("/api/**")
            .notMatch(properties.getSecurity().getExcludes())
            .check(r -> {
                if (SaRouter.isMatchCurrURI(properties.getSecurity().getExcludes())) {
                    return;
                }
                log.debug("auth check result: {}", StpUtil.isLogin());
                StpUtil.checkLogin();
            })))
            .addPathPatterns(StringConstants.PATH_PATTERN)
            .excludePathPatterns(properties.getSecurity().getExcludes());
    }

    /**
     * 初始化默认语言环境 确保系统启动时使用正确的默认语言环境
     */
    @PostConstruct
    public void initDefaultLocale() {
        // 设置系统默认语言环境为美国英语
        Locale.setDefault(Locale.US);
        log.info("Default locale initialized to: {}", Locale.getDefault());
    }

    /**
     * 配置国际化消息源 支持参数校验消息的国际化
     *
     * @return 消息源配置
     */
    @Bean
    public MessageSource messageSource() {
        // 缓存1小时
        ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
        // 配置文件前缀名，设置为Messages,那你的配置文件必须以Messages.properties/Message_en.properties...
        messageSource.setBasenames(I18N_MESSAGE_LIST);
        messageSource.setDefaultEncoding("UTF-8");
        messageSource.setCacheSeconds(3600);
        messageSource.setFallbackToSystemLocale(false);
        messageSource.setDefaultLocale(Locale.US);

        log.info("MessageSource configured with basenames: [i18n/messages, i18n/openapi-message]");
        return messageSource;
    }

    /**
     * 配置参数校验器工厂 将国际化消息源集成到参数校验中，并使用自定义的消息插值器
     *
     * @return 本地校验器工厂
     */
    @Bean
    public Validator validator() {
        LocalValidatorFactoryBean factoryBean = new LocalValidatorFactoryBean();
        factoryBean.setMessageInterpolator(messageInterpolator());
        factoryBean.setValidationMessageSource(messageSource());
        return factoryBean;
    }

    /**
     * 配置自定义消息插值器 确保Bean Validation的参数插值能够正确工作
     *
     * @return 消息插值器
     */
    @Bean
    public MessageInterpolator messageInterpolator() {
        // 使用Hibernate Validator的默认实现，它能够正确处理参数插值
        return new ResourceBundleMessageInterpolator();
    }

}
