/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.config.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 统一过滤器配置类 集中管理所有过滤器的注册和配置
 *
 * <AUTHOR>
 * @date 2025/7/5 13:20
 * @description: 统一过滤器配置，提供清晰的过滤器管理和优先级控制
 * @since 1.0.0
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(FilterConfigurationProperties.class)
public class UnifiedFilterConfiguration {

    private final FilterConfigurationProperties filterProperties;

    public UnifiedFilterConfiguration(FilterConfigurationProperties filterProperties) {
        this.filterProperties = filterProperties;
        logFilterConfiguration();
    }

    /**
     * 租户过滤器注册
     *
     * @param tenantFilter 租户过滤器实例
     * @return 过滤器注册Bean
     */
    @Bean
    @ConditionalOnProperty(prefix = "fulfillmen.filter.tenant", name = "enabled", havingValue = "true", matchIfMissing = true)
    public FilterRegistrationBean<TenantFilter> tenantFilterRegistration(TenantFilter tenantFilter) {
        FilterConfigurationProperties.TenantFilterConfig config = filterProperties.getTenant();

        FilterRegistrationBean<TenantFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(tenantFilter);
        registration.setName("tenantFilter");
        registration.addUrlPatterns(config.getUrlPatterns().toArray(new String[0]));
        registration.setOrder(config.getOrder());
        registration.setEnabled(config.isEnabled());

        log.info("租户过滤器已注册 [enabled={}, order={}, urlPatterns={}, excludePatterns={}]",
            config.isEnabled(), config.getOrder(), config.getUrlPatterns(), config.getExcludePatterns().size());

        return registration;
    }

    /**
     * MDC 过滤器注册
     *
     * @return 过滤器注册Bean
     */
    @Bean
    @ConditionalOnProperty(prefix = "fulfillmen.filter.mdc", name = "enabled", havingValue = "true", matchIfMissing = true)
    public FilterRegistrationBean<GlobalMDCFilter> mdcFilterRegistration() {
        FilterConfigurationProperties.MdcFilterConfig config = filterProperties.getMdc();

        FilterRegistrationBean<GlobalMDCFilter> registration = new FilterRegistrationBean<>();
        // 传递配置给过滤器
        registration.setFilter(new GlobalMDCFilter(config));
        registration.setName("globalMDCFilter");
        registration.addUrlPatterns(config.getUrlPatterns().toArray(new String[0]));
        registration.setOrder(config.getOrder());
        registration.setEnabled(config.isEnabled());

        log.info("MDC 过滤器已注册 [enabled={}, order={}, urlPatterns={}, features={}]",
            config.isEnabled(), config.getOrder(), config.getUrlPatterns(),
            buildMdcFeatureDescription(config));

        return registration;
    }

    /**
     * 构建 MDC 功能描述
     */
    private String buildMdcFeatureDescription(FilterConfigurationProperties.MdcFilterConfig config) {
        StringBuilder features = new StringBuilder();

        if (config.isNormalizeIpAddress()) {
            features.append("IP标准化,");
        }
        if (config.isIncludeBrowserInfo()) {
            features.append("浏览器信息,");
        }
        if (config.isIncludeOsInfo()) {
            features.append("操作系统信息,");
        }

        String result = features.toString();
        return result.endsWith(",") ? result.substring(0, result.length() - 1) : result;
    }

    /**
     * 记录过滤器配置信息
     */
    private void logFilterConfiguration() {
        log.info("=== 过滤器配置信息 ===");
        log.info("租户过滤器: enabled={}, order={}",
            filterProperties.getTenant().isEnabled(),
            filterProperties.getTenant().getOrder());
        log.info("MDC 过滤器: enabled={}, order={}",
            filterProperties.getMdc().isEnabled(),
            filterProperties.getMdc().getOrder());
        log.info("过滤器优先级说明:\n{}", FilterOrderConstants.getOrderDescription());
        log.info("===================");
    }

    /**
     * 获取过滤器配置属性（用于其他组件访问）
     *
     * @return 过滤器配置属性
     */
    @Bean
    public FilterConfigurationProperties filterConfigurationProperties() {
        return filterProperties;
    }
}
