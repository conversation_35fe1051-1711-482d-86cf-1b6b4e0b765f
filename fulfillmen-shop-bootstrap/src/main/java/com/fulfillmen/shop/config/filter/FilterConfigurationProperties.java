/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.config.filter;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * 过滤器统一配置属性
 *
 * <AUTHOR>
 * @date 2025/7/5 14:30
 * @description: 统一管理所有过滤器的配置属性
 * @since 1.0.0
 */
@Data
@ConfigurationProperties(prefix = "fulfillmen.filter")
public class FilterConfigurationProperties {

    /**
     * 租户过滤器配置
     */
    @NestedConfigurationProperty
    private TenantFilterConfig tenant = new TenantFilterConfig();

    /**
     * MDC 过滤器配置
     */
    @NestedConfigurationProperty
    private MdcFilterConfig mdc = new MdcFilterConfig();

    /**
     * 通用过滤器配置基类
     */
    @Data
    public static class BaseFilterConfig {

        /**
         * 是否启用过滤器
         */
        private boolean enabled = true;

        /**
         * 过滤器优先级（数值越小优先级越高）
         */
        private int order;

        /**
         * URL 匹配模式
         */
        private List<String> urlPatterns = List.of("/*");

        /**
         * 忽略的 URL 模式
         */
        private List<String> excludePatterns = new ArrayList<>();
    }

    /**
     * 租户过滤器配置
     */
    @Data
    public static class TenantFilterConfig extends BaseFilterConfig {

        public TenantFilterConfig() {
            // 租户过滤器默认优先级：HIGHEST_PRECEDENCE + 10
            setOrder(-**********); // Ordered.HIGHEST_PRECEDENCE + 10
            setExcludePatterns(List.of(
                "/error", "/error/**",
                "/health", "/health/**",
                "/actuator/**",
                "/swagger-ui/**",
                "/v3/api-docs/**",
                "/webjars/**",
                "/favicon.ico",
                "/static/**",
                "/public/**",
                "/assets/**",
                "/*.js", "/*.css", "/*.png", "/*.jpg", "/*.jpeg", "/*.gif", "/*.ico", "/*.svg"
            ));
        }
    }

    /**
     * MDC 过滤器配置
     */
    @Data
    public static class MdcFilterConfig extends BaseFilterConfig {

        /**
         * 是否启用 IP 地址标准化
         */
        private boolean normalizeIpAddress = true;

        /**
         * 是否记录浏览器信息
         */
        private boolean includeBrowserInfo = true;

        /**
         * 是否记录操作系统信息
         */
        private boolean includeOsInfo = true;

        public MdcFilterConfig() {
            // MDC 过滤器默认优先级：HIGHEST_PRECEDENCE + 20
            setOrder(-**********); // Ordered.HIGHEST_PRECEDENCE + 20
            setExcludePatterns(List.of(
                "/error", "/error/**",
                "/health", "/health/**",
                "/actuator/**",
                "/swagger-ui/**",
                "/v3/api-docs/**",
                "/webjars/**",
                "/favicon.ico",
                "/static/**",
                "/public/**",
                "/assets/**",
                "/*.js", "/*.css", "/*.png", "/*.jpg", "/*.jpeg", "/*.gif", "/*.ico", "/*.svg"
            ));
        }
    }
}
