/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.config.filter;

import org.springframework.core.Ordered;

/**
 * 过滤器优先级常量
 * 
 * <AUTHOR>
 * @date 2025/7/5 14:35
 * @description: 统一管理过滤器的执行优先级，确保过滤器按正确顺序执行
 * @since 1.0.0
 */
public final class FilterOrderConstants {

    private FilterOrderConstants() {
        // 工具类，禁止实例化
    }

    /**
     * 基础优先级 - 最高优先级
     */
    public static final int BASE_HIGHEST = Ordered.HIGHEST_PRECEDENCE;

    /**
     * 链路跟踪过滤器优先级
     * 最高优先级，确保 TraceId 在所有过滤器中都可用
     */
    public static final int TRACE_FILTER = BASE_HIGHEST;

    /**
     * 租户过滤器优先级
     * 在链路跟踪之后，为后续过滤器提供租户上下文
     */
    public static final int TENANT_FILTER = BASE_HIGHEST + 10;

    /**
     * MDC 过滤器优先级
     * 在租户过滤器之后，设置日志上下文信息
     */
    public static final int MDC_FILTER = BASE_HIGHEST + 20;

    /**
     * XSS 过滤器优先级
     * 在业务过滤器之前，进行安全过滤
     */
    public static final int XSS_FILTER = BASE_HIGHEST + 100;

    /**
     * CORS 过滤器优先级
     * 在安全过滤器之后
     */
    public static final int CORS_FILTER = BASE_HIGHEST + 110;

    /**
     * 日志过滤器优先级
     * 较低优先级，记录完整的请求响应信息
     */
    public static final int LOG_FILTER = Ordered.LOWEST_PRECEDENCE - 10;

    /**
     * 获取过滤器优先级说明
     * 
     * @return 优先级说明文档
     */
    public static String getOrderDescription() {
        return """
            过滤器执行顺序（数值越小优先级越高）：

            1. TRACE_FILTER     (%d) - 链路跟踪过滤器，生成 TraceId
            2. TENANT_FILTER    (%d) - 租户过滤器，设置租户上下文
            3. MDC_FILTER       (%d) - MDC 过滤器，设置日志上下文
            4. XSS_FILTER       (%d) - XSS 过滤器，安全防护
            5. CORS_FILTER      (%d) - CORS 过滤器，跨域处理
            6. LOG_FILTER       (%d) - 日志过滤器，记录请求响应

            注意：
            - 链路跟踪过滤器优先级最高，确保 TraceId 在整个请求生命周期中可用
            - 租户过滤器在链路跟踪之后，为后续过滤器提供租户上下文
            - MDC 过滤器在租户过滤器之后，可以获取到租户信息并设置到日志上下文
            - 安全相关过滤器（XSS、CORS）在业务过滤器之前执行
            - 日志过滤器优先级最低，记录完整的请求响应信息
            """.formatted(
            TRACE_FILTER, TENANT_FILTER, MDC_FILTER,
            XSS_FILTER, CORS_FILTER, LOG_FILTER
        );
    }
}
