/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.secheduler;

import com.fulfillmen.shop.domain.util.CurrencyConversionUtils;
import com.fulfillmen.shop.manager.support.service.ICurrencyExchangeService;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 汇率缓存同步服务
 *
 * <pre>
 * 1. 此类负责初始化和定时更新 {@link CurrencyConversionUtils} 的内部汇率缓存
 * 2. 通过调用 {@link ICurrencyExchangeService} 获取实时汇率数据
 * 3. 开发环境不进行定时刷新，保留 API 接口的请求次数,防止过度刷新调用。
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/6/6
 * @since 1.0.0
 */
@Slf4j
@Profile({"!dev", "!local"})
@Component
@RequiredArgsConstructor
public class CurrencyRateCacheScheduledTask implements CommandLineRunner {

    /**
     * 常用的货币列表
     */
    private static final String[] SUPPORTED_CURRENCIES = {"CNY", "USD", "EUR", "JPY", "KRW", "INR"};
    private final ICurrencyExchangeService currencyExchangeService;

    /**
     * 应用启动时初始化汇率缓存
     */
    @Override
    public void run(String... args) {
        log.info("开始初始化汇率缓存...");
        try {
            refreshRateCache();
            log.info("汇率缓存初始化完成，缓存条目数: {}", CurrencyConversionUtils.getCacheSize());
        } catch (Exception e) {
            log.error("汇率缓存初始化失败", e);
        }
    }

    /**
     * 定时刷新汇率缓存（7Days）
     */
    @Scheduled(fixedRate = 7, timeUnit = TimeUnit.DAYS) // 2小时
    public void scheduledRefreshRateCache() {
        log.info("开始定时刷新汇率缓存...");
        try {
            refreshRateCache();
            log.info("定时刷新汇率缓存完成，缓存条目数: {}", CurrencyConversionUtils.getCacheSize());
        } catch (Exception e) {
            log.error("定时刷新汇率缓存失败", e);
        }
    }

    /**
     * 手动刷新汇率缓存（可通过JMX或其他方式调用）
     */
    public void manualRefreshRateCache() {
        log.info("开始手动刷新汇率缓存...");
        try {
            refreshRateCache();
            log.info("手动刷新汇率缓存完成，缓存条目数: {}", CurrencyConversionUtils.getCacheSize());
        } catch (Exception e) {
            log.error("手动刷新汇率缓存失败", e);
        }
    }

    /**
     * 刷新汇率缓存的核心逻辑 - 仅获取以CNY为基础货币的汇率
     */
    private void refreshRateCache() {
        Map<String, BigDecimal> rates = new HashMap<>();
        int successCount = 0;
        int totalCount = 0;
        // 增加同步锁

        // 仅获取CNY作为基础货币的汇率，其他货币对通过CNY中转计算
        for (String targetCurrency : SUPPORTED_CURRENCIES) {
            if (!"CNY".equals(targetCurrency)) {
                totalCount++;
                try {
                    BigDecimal rate = currencyExchangeService.getExchangeRate("CNY", targetCurrency);
                    if (rate != null && rate.compareTo(BigDecimal.ZERO) > 0) {
                        String cacheKey = "CNY-" + targetCurrency;
                        rates.put(cacheKey, rate);
                        successCount++;
                        log.debug("获取CNY基础汇率成功: {} = {}", cacheKey, rate);
                    } else {
                        log.warn("获取CNY基础汇率失败或无效: CNY -> {}", targetCurrency);
                    }
                } catch (Exception e) {
                    log.warn("获取CNY基础汇率异常: CNY -> {}, 错误: {}", targetCurrency, e.getMessage());
                }

                // 避免API调用过于频繁
                try {
                    Thread.sleep(200); // 200ms间隔，因为调用次数少了可以稍微慢一点确保稳定
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        // 批量更新缓存
        if (!rates.isEmpty()) {
            CurrencyConversionUtils.updateExchangeRates(rates);
            log.info("CNY基础汇率缓存更新完成: 成功 {}/{} 条记录", successCount, totalCount);
        } else {
            log.warn("未获取到任何有效的CNY基础汇率数据");
        }
    }

    /**
     * 获取缓存状态信息
     */
    public Map<String, Object> getCacheStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("cacheSize", CurrencyConversionUtils.getCacheSize());
        status.put("lastUpdateTime", CurrencyConversionUtils.getLastUpdateTime());
        status.put("supportedCurrencies", SUPPORTED_CURRENCIES);
        return status;
    }

    /**
     * 清空并重新加载缓存
     */
    public void clearAndReloadCache() {
        log.info("清空并重新加载汇率缓存...");
        CurrencyConversionUtils.clearCache();
        refreshRateCache();
        log.info("汇率缓存重新加载完成");
    }
}
