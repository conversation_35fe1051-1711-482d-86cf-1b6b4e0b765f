/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.controller;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试 MDC 过滤器的控制器
 * 用于验证 MDC 信息是否正确设置
 *
 * <AUTHOR>
 * @date 2025/7/5 13:45
 * @description: 测试 MDC 过滤器功能的示例控制器
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/test/mdc")
public class TestMDCController {

    /**
     * 测试 MDC 信息
     *
     * @return MDC 信息
     */
    @GetMapping("/info")
    public Map<String, Object> getMDCInfo() {
        log.info("测试 MDC 过滤器 - 请求处理中");

        Map<String, Object> result = new HashMap<>();

        // 获取 MDC 中的信息
        String tenantId = MDC.get("tenantId");
        String userId = MDC.get("userId");
        String ip = MDC.get("ip");
        String os = MDC.get("os");
        String browser = MDC.get("browser");

        result.put("tenantId", tenantId);
        result.put("userId", userId);
        result.put("ip", ip);
        result.put("os", os);
        result.put("browser", browser);
        result.put("message", "MDC 信息获取成功");

        log.info("MDC 信息: tenantId={}, userId={}, ip={}, os={}, browser={}",
            tenantId, userId, ip, os, browser);

        return result;
    }

    /**
     * 测试日志输出
     *
     * @return 响应信息
     */
    @GetMapping("/log")
    public Map<String, String> testLog() {
        log.debug("这是一条 DEBUG 日志");
        log.info("这是一条 INFO 日志");
        log.warn("这是一条 WARN 日志");

        Map<String, String> result = new HashMap<>();
        result.put("message", "日志测试完成，请查看控制台输出");
        return result;
    }

    /**
     * 测试 IP 地址显示
     *
     * @return IP 地址信息
     */
    @GetMapping("/ip")
    public Map<String, Object> testIpAddress() {
        String ip = MDC.get("ip");

        Map<String, Object> result = new HashMap<>();
        result.put("ip", ip);
        result.put("message", "当前请求的 IP 地址");

        // 判断是否为本地地址
        boolean isLocal = "127.0.0.1".equals(ip) || "localhost".equals(ip);
        result.put("isLocal", isLocal);

        if (isLocal) {
            result.put("note", "检测到本地访问，IPv6 地址已自动转换为 127.0.0.1");
        }

        log.info("IP 地址测试: ip={}, isLocal={}", ip, isLocal);

        return result;
    }
}
