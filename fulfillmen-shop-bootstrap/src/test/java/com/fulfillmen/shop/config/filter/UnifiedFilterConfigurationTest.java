/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.config.filter;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 统一过滤器配置测试
 *
 * <AUTHOR>
 * @date 2025/7/5 15:00
 * @description: 测试统一过滤器配置功能
 * @since 1.0.0
 */
class UnifiedFilterConfigurationTest {

    @Test
    void testFilterOrderConstants() {
        // 验证过滤器优先级常量
        assertEquals(-2147483648, FilterOrderConstants.TRACE_FILTER);
        assertEquals(-2147483638, FilterOrderConstants.TENANT_FILTER);
        assertEquals(-2147483628, FilterOrderConstants.MDC_FILTER);
        assertEquals(-2147483548, FilterOrderConstants.XSS_FILTER);
        assertEquals(-2147483538, FilterOrderConstants.CORS_FILTER);
        assertEquals(2147483637, FilterOrderConstants.LOG_FILTER);

        // 验证优先级顺序正确
        assertTrue(FilterOrderConstants.TRACE_FILTER < FilterOrderConstants.TENANT_FILTER);
        assertTrue(FilterOrderConstants.TENANT_FILTER < FilterOrderConstants.MDC_FILTER);
        assertTrue(FilterOrderConstants.MDC_FILTER < FilterOrderConstants.XSS_FILTER);
        assertTrue(FilterOrderConstants.XSS_FILTER < FilterOrderConstants.CORS_FILTER);
        assertTrue(FilterOrderConstants.CORS_FILTER < FilterOrderConstants.LOG_FILTER);
    }

    @Test
    void testFilterOrderDescription() {
        String description = FilterOrderConstants.getOrderDescription();

        assertNotNull(description);
        assertTrue(description.contains("TRACE_FILTER"));
        assertTrue(description.contains("TENANT_FILTER"));
        assertTrue(description.contains("MDC_FILTER"));
        assertTrue(description.contains("链路跟踪过滤器"));
        assertTrue(description.contains("租户过滤器"));
        assertTrue(description.contains("MDC 过滤器"));
    }

    @Test
    void testFilterConfigurationProperties() {
        FilterConfigurationProperties properties = new FilterConfigurationProperties();

        // 测试默认配置
        assertNotNull(properties.getTenant());
        assertNotNull(properties.getMdc());

        // 测试租户过滤器默认配置
        FilterConfigurationProperties.TenantFilterConfig tenantConfig = properties.getTenant();
        assertTrue(tenantConfig.isEnabled());
        assertEquals(FilterOrderConstants.TENANT_FILTER, tenantConfig.getOrder());
        assertFalse(tenantConfig.getExcludePatterns().isEmpty());

        // 测试 MDC 过滤器默认配置
        FilterConfigurationProperties.MdcFilterConfig mdcConfig = properties.getMdc();
        assertTrue(mdcConfig.isEnabled());
        assertEquals(FilterOrderConstants.MDC_FILTER, mdcConfig.getOrder());
        assertTrue(mdcConfig.isNormalizeIpAddress());
        assertTrue(mdcConfig.isIncludeBrowserInfo());
        assertTrue(mdcConfig.isIncludeOsInfo());
        assertFalse(mdcConfig.getExcludePatterns().isEmpty());
    }

    @Test
    void testBaseFilterConfig() {
        FilterConfigurationProperties.BaseFilterConfig config = new FilterConfigurationProperties.BaseFilterConfig();

        // 测试默认值
        assertTrue(config.isEnabled());
        assertEquals(0, config.getOrder());
        assertEquals(1, config.getUrlPatterns().size());
        assertEquals("/*", config.getUrlPatterns().get(0));
        assertTrue(config.getExcludePatterns().isEmpty());
    }
}
