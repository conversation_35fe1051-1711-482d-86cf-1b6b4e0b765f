/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.config;

import com.fulfillmen.shop.config.filter.GlobalMDCFilter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.MDC;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GlobalMDCFilter 测试类
 *
 * <AUTHOR>
 * @date 2025/7/5 13:30
 * @description: 测试全局 MDC 过滤器功能
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class GlobalMDCFilterTest {

    private GlobalMDCFilter filter;

    @Mock
    private FilterChain filterChain;

    @BeforeEach
    void setUp() {
        filter = new GlobalMDCFilter();
        // 清理 MDC
        MDC.clear();
    }

    @AfterEach
    void tearDown() {
        // 清理 MDC
        MDC.clear();
    }

    @Test
    void testDoFilter_WithNormalRequest() throws IOException, ServletException {
        // 准备测试数据
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRequestURI("/api/test");
        request.setRemoteAddr("*************");
        request.addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");

        MockHttpServletResponse response = new MockHttpServletResponse();

        // 执行过滤器
        filter.doFilter(request, response, filterChain);

        // 验证过滤器链被调用
        verify(filterChain).doFilter(request, response);

        // 验证 MDC 被清理（因为在 finally 块中清理）
        assertNull(MDC.get(GlobalMDCFilter.MDC_KEYS.IP));
        assertNull(MDC.get(GlobalMDCFilter.MDC_KEYS.BROWSER));
        assertNull(MDC.get(GlobalMDCFilter.MDC_KEYS.OS));
    }

    @Test
    void testDoFilter_WithIgnoredRequest() throws IOException, ServletException {
        // 准备测试数据 - 静态资源请求
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRequestURI("/static/css/style.css");

        MockHttpServletResponse response = new MockHttpServletResponse();

        // 执行过滤器
        filter.doFilter(request, response, filterChain);

        // 验证过滤器链被调用
        verify(filterChain).doFilter(request, response);

        // 验证 MDC 没有被设置
        assertNull(MDC.get(GlobalMDCFilter.MDC_KEYS.IP));
        assertNull(MDC.get(GlobalMDCFilter.MDC_KEYS.BROWSER));
        assertNull(MDC.get(GlobalMDCFilter.MDC_KEYS.OS));
    }

    @Test
    void testDoFilter_WithHealthCheckRequest() throws IOException, ServletException {
        // 准备测试数据 - 健康检查请求
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRequestURI("/health");

        MockHttpServletResponse response = new MockHttpServletResponse();

        // 执行过滤器
        filter.doFilter(request, response, filterChain);

        // 验证过滤器链被调用
        verify(filterChain).doFilter(request, response);

        // 验证 MDC 没有被设置
        assertNull(MDC.get(GlobalMDCFilter.MDC_KEYS.IP));
        assertNull(MDC.get(GlobalMDCFilter.MDC_KEYS.BROWSER));
        assertNull(MDC.get(GlobalMDCFilter.MDC_KEYS.OS));
    }

    @Test
    void testDoFilter_WithActuatorRequest() throws IOException, ServletException {
        // 准备测试数据 - Actuator 请求
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRequestURI("/actuator/health");

        MockHttpServletResponse response = new MockHttpServletResponse();

        // 执行过滤器
        filter.doFilter(request, response, filterChain);

        // 验证过滤器链被调用
        verify(filterChain).doFilter(request, response);

        // 验证 MDC 没有被设置
        assertNull(MDC.get(GlobalMDCFilter.MDC_KEYS.IP));
        assertNull(MDC.get(GlobalMDCFilter.MDC_KEYS.BROWSER));
        assertNull(MDC.get(GlobalMDCFilter.MDC_KEYS.OS));
    }

    @Test
    void testDoFilter_WithIPv6LocalhostRequest() throws IOException, ServletException {
        // 准备测试数据 - IPv6 本地回环地址
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRequestURI("/api/test");
        request.setRemoteAddr("0:0:0:0:0:0:0:1");
        request.addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36");

        MockHttpServletResponse response = new MockHttpServletResponse();

        // 执行过滤器
        filter.doFilter(request, response, filterChain);

        // 验证过滤器链被调用
        verify(filterChain).doFilter(request, response);

        // 验证 MDC 被清理（因为在 finally 块中清理）
        assertNull(MDC.get(GlobalMDCFilter.MDC_KEYS.IP));
        assertNull(MDC.get(GlobalMDCFilter.MDC_KEYS.BROWSER));
        assertNull(MDC.get(GlobalMDCFilter.MDC_KEYS.OS));
    }

    @Test
    void testDoFilter_WithSaTokenContextException() throws IOException, ServletException {
        // 准备测试数据 - 模拟 SaToken 上下文未初始化的情况
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRequestURI("/api/test");
        request.setRemoteAddr("*************");
        request.addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");

        MockHttpServletResponse response = new MockHttpServletResponse();

        // 执行过滤器 - 应该正常处理，不抛出异常
        assertDoesNotThrow(() -> {
            filter.doFilter(request, response, filterChain);
        });

        // 验证过滤器链被调用
        verify(filterChain).doFilter(request, response);

        // 验证 MDC 被清理（因为在 finally 块中清理）
        assertNull(MDC.get(GlobalMDCFilter.MDC_KEYS.IP));
        assertNull(MDC.get(GlobalMDCFilter.MDC_KEYS.BROWSER));
        assertNull(MDC.get(GlobalMDCFilter.MDC_KEYS.OS));
    }

    @Test
    void testMDCKeysConstants() {
        // 验证 MDC 键名常量
        assertEquals("ip", GlobalMDCFilter.MDC_KEYS.IP);
        assertEquals("os", GlobalMDCFilter.MDC_KEYS.OS);
        assertEquals("browser", GlobalMDCFilter.MDC_KEYS.BROWSER);
    }
}
