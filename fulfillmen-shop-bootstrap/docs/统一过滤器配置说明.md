# 统一过滤器配置说明

## 概述

为了提高过滤器配置的可维护性和一致性，我们将所有过滤器的配置统一管理在 `UnifiedFilterConfiguration` 类中。这种方式提供了：

- **集中管理**：所有过滤器配置在一个地方
- **清晰的优先级**：统一的优先级常量和说明
- **灵活配置**：支持通过配置文件控制过滤器行为
- **易于维护**：减少配置分散带来的维护问题

## 架构设计

### 核心组件

1. **FilterConfigurationProperties** - 配置属性类
2. **FilterOrderConstants** - 优先级常量类
3. **UnifiedFilterConfiguration** - 统一配置类

### 过滤器执行顺序

```
请求 → TRACE_FILTER → TENANT_FILTER → MDC_FILTER → XSS_FILTER → CORS_FILTER → 业务逻辑 → LOG_FILTER → 响应
```

| 过滤器 | 优先级 | 作用 |
|--------|--------|------|
| TRACE_FILTER | -2147483648 | 链路跟踪，生成 TraceId |
| TENANT_FILTER | -********** | 租户上下文设置 |
| MDC_FILTER | -********** | 日志上下文设置 |
| XSS_FILTER | -********** | XSS 安全防护 |
| CORS_FILTER | -********** | 跨域处理 |
| LOG_FILTER | ********** | 请求响应日志记录 |

## 配置说明

### 基础配置

```yaml
fulfillmen:
  filter:
    # 租户过滤器配置
    tenant:
      enabled: true                    # 是否启用
      order: -**********              # 优先级
      url-patterns: ["/*"]            # URL 匹配模式
      exclude-patterns:               # 忽略的 URL 模式
        - "/error"
        - "/health"
        - "/actuator/**"
        # ... 更多模式

    # MDC 过滤器配置
    mdc:
      enabled: true                   # 是否启用
      order: -**********             # 优先级
      url-patterns: ["/*"]           # URL 匹配模式
      exclude-patterns: [...]        # 忽略的 URL 模式
      normalize-ip-address: true     # IP 地址标准化
      include-user-info: true        # 包含用户信息
      include-browser-info: true     # 包含浏览器信息
      include-os-info: true          # 包含操作系统信息
```

### 高级配置

#### 环境特定配置

```yaml
# 开发环境 - 记录详细信息
spring:
  profiles: dev
fulfillmen:
  filter:
    mdc:
      include-browser-info: true
      include-os-info: true

# 生产环境 - 减少信息记录
spring:
  profiles: prod
fulfillmen:
  filter:
    mdc:
      include-browser-info: false
      include-os-info: false
```

#### 禁用特定过滤器

```yaml
fulfillmen:
  filter:
    tenant:
      enabled: false    # 禁用租户过滤器
    mdc:
      enabled: true     # 保持 MDC 过滤器启用
```

## 迁移指南

### 从旧配置迁移

1. **删除旧的配置类**：
   - `GlobalMDCFilterConfig` ✅ 已删除
   - `TenantFilterConfig` ⚠️ 已标记为废弃

2. **更新配置文件**：
   ```yaml
   # 旧配置（不再需要）
   # 过滤器通过单独的配置类注册

   # 新配置
   fulfillmen:
     filter:
       tenant:
         enabled: true
       mdc:
         enabled: true
   ```

3. **验证迁移**：
   - 启动应用，查看日志确认过滤器正确注册
   - 检查过滤器执行顺序是否正确

### 兼容性

- **向后兼容**：旧的 `TenantFilterConfig` 仍然可用（已标记废弃）
- **默认行为**：如果没有配置，使用默认值
- **渐进迁移**：可以逐步迁移到新配置

## 使用示例

### 基本使用

```java
@RestController
public class TestController {
    
    @Autowired
    private FilterConfigurationProperties filterProperties;
    
    @GetMapping("/filter-config")
    public Map<String, Object> getFilterConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("tenant", filterProperties.getTenant());
        config.put("mdc", filterProperties.getMdc());
        return config;
    }
}
```

### 动态配置

```java
@Component
public class FilterConfigManager {
    
    @Autowired
    private FilterConfigurationProperties filterProperties;
    
    public void updateMdcConfig(boolean includeBrowser) {
        filterProperties.getMdc().setIncludeBrowserInfo(includeBrowser);
        // 注意：运行时修改配置不会影响已注册的过滤器
        // 需要重启应用或实现动态重新注册机制
    }
}
```

## 监控和调试

### 启动日志

应用启动时会输出过滤器配置信息：

```
=== 过滤器配置信息 ===
租户过滤器: enabled=true, order=-**********
MDC 过滤器: enabled=true, order=-**********
过滤器优先级说明:
...
===================
```

### 测试端点

- `/test/mdc/info` - 查看 MDC 信息
- `/test/mdc/ip` - 测试 IP 地址处理
- `/test/mdc/log` - 测试日志输出

## 最佳实践

1. **配置管理**：
   - 使用环境特定的配置文件
   - 生产环境减少不必要的信息记录
   - 定期审查过滤器配置

2. **性能优化**：
   - 合理设置 `exclude-patterns` 忽略静态资源
   - 根据需要禁用不必要的功能
   - 监控过滤器性能影响

3. **安全考虑**：
   - 不要在日志中记录敏感信息
   - 合理设置过滤器优先级
   - 定期更新安全相关过滤器

## 故障排除

### 常见问题

1. **过滤器未生效**：
   - 检查 `enabled` 配置
   - 确认 URL 模式匹配
   - 查看启动日志确认注册成功

2. **优先级问题**：
   - 检查 `order` 配置
   - 参考 `FilterOrderConstants` 中的说明
   - 避免使用相同的优先级值

3. **配置不生效**：
   - 确认配置文件路径正确
   - 检查配置属性名称拼写
   - 验证 YAML 格式正确

### 调试技巧

1. **启用调试日志**：
   ```yaml
   logging:
     level:
       com.fulfillmen.shop.config.filter: DEBUG
   ```

2. **查看过滤器链**：
   ```java
   @Component
   public class FilterChainDebugger implements Filter {
       @Override
       public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) {
           log.debug("Filter chain: {}", chain.getClass().getName());
           chain.doFilter(request, response);
       }
   }
   ```

## 扩展指南

### 添加新过滤器

1. **定义配置属性**：
   ```java
   @Data
   public static class NewFilterConfig extends BaseFilterConfig {
       private String customProperty = "default";
   }
   ```

2. **更新配置类**：
   ```java
   @NestedConfigurationProperty
   private NewFilterConfig newFilter = new NewFilterConfig();
   ```

3. **注册过滤器**：
   ```java
   @Bean
   public FilterRegistrationBean<NewFilter> newFilterRegistration() {
       // 实现注册逻辑
   }
   ```

### 自定义优先级

在 `FilterOrderConstants` 中添加新的优先级常量：

```java
public static final int NEW_FILTER = BASE_HIGHEST + 30;
```

这样可以确保过滤器配置的统一性和可维护性。
