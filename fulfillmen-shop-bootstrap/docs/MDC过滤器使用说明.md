# 全局 MDC 过滤器使用说明

## 概述

全局 MDC（Mapped Diagnostic Context）过滤器是一个用于自动设置日志上下文信息的过滤器，它会为每个 HTTP 请求自动提取并设置相关的上下文信息到 MDC 中，使得日志输出能够包含更丰富的请求信息。

## 功能特性

### 自动提取的信息

**基本请求信息**（所有请求）：
- `ip`: 客户端 IP 地址（自动处理 IPv6 本地回环地址）
- `browser`: 浏览器信息（从 User-Agent 解析）
- `os`: 操作系统信息（从 User-Agent 解析）

> **注意**：用户登录信息（如 `userId`、`tenantId`）由 SaToken 原有机制处理，不在此过滤器中设置。

### IP 地址处理

过滤器会自动处理以下 IP 地址格式：

- **IPv6 本地回环地址**：`0:0:0:0:0:0:0:1` 和 `::1` 会自动转换为 `127.0.0.1`
- **IPv4 零地址**：`0.0.0.0` 会自动转换为 `127.0.0.1`
- **其他地址**：保持原样显示

这样可以确保日志中的 IP 地址显示更加友好和一致。

### 性能优化

- **智能忽略**：自动忽略静态资源、健康检查、监控端点等请求，避免不必要的处理
- **异常处理**：所有操作都有异常处理，确保不会影响正常的请求处理
- **资源清理**：请求结束后自动清理 MDC 信息，避免内存泄漏

## 配置说明

### 过滤器注册

过滤器通过 `GlobalMDCFilterConfig` 自动注册，优先级设置为 `HIGHEST_PRECEDENCE + 20`，确保在租户过滤器之后执行。

### 忽略的 URL 模式

以下 URL 模式会被自动忽略，不会设置 MDC 信息：

```
/error, /error/**
/health, /health/**
/actuator/**
/swagger-ui/**
/v3/api-docs/**
/webjars/**
/favicon.ico
/static/**
/public/**
/assets/**
/*.js, /*.css, /*.png, /*.jpg, /*.jpeg, /*.gif, /*.ico, /*.svg
```

## 日志配置

### logback-spring.xml 配置

MDC 信息会自动添加到日志输出中，当前的日志格式包含以下 MDC 键：

```xml
<!-- 控制台输出格式 -->
<property name="CONSOLE_LOG_PATTERN" value="%red(%d{yyyy-MM-dd HH:mm:ss}) %highlight(%-5level) %green([%thread]) %boldMagenta(%logger{50}) [tid:%X{tenantId}:uId:%X{userId}:ip:%X{ip}:os:%X{os}:browser:%X{browser}] - %msg%n"/>

<!-- 文件输出格式 -->
<property name="FILE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss} %-5level [%thread] [tid:%X{tenantId}:uId:%X{userId}:ip:%X{ip}:os:%X{os}:browser:%X{browser}] %logger{50} - %msg%n"/>
```

### MDC 键名常量

可以通过 `GlobalMDCFilter.MDC_KEYS` 类访问此过滤器设置的 MDC 键名常量：

```java
public static final class MDC_KEYS {
    public static final String IP = "ip";
    public static final String OS = "os";
    public static final String BROWSER = "browser";
}
```

> **注意**：`tenantId` 和 `userId` 由 SaToken 机制管理，不在此常量类中定义。

## 使用示例

### 在代码中获取 MDC 信息

```java
@RestController
public class ExampleController {

    private static final Logger log = LoggerFactory.getLogger(ExampleController.class);

    @GetMapping("/example")
    public String example() {
        // MDC 信息会自动包含在日志输出中
        log.info("处理示例请求");

        // 也可以手动获取 MDC 信息（此过滤器设置的）
        String userIp = MDC.get(GlobalMDCFilter.MDC_KEYS.IP);
        String browser = MDC.get(GlobalMDCFilter.MDC_KEYS.BROWSER);
        String os = MDC.get(GlobalMDCFilter.MDC_KEYS.OS);

        // 用户信息由 SaToken 机制管理
        String tenantId = MDC.get("tenantId");
        String userId = MDC.get("userId");

        return "success";
    }
}
```

### 日志输出示例

**生产环境（真实 IP）：**
```
2025-07-05 13:45:30 INFO [http-nio-8080-exec-1] [tid::uId::ip:*************:os:Windows 10:browser:Chrome 91.0.4472.124] com.example.ExampleController - 处理示例请求
```

**本地开发环境（IPv6 地址已转换）：**
```
2025-07-05 13:45:30 INFO [http-nio-8080-exec-1] [tid::uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] com.example.ExampleController - 处理示例请求
```

> **说明**：`tid` 和 `uId` 字段由 SaToken 机制填充，此过滤器只负责 `ip`、`os`、`browser` 字段。

## 测试

### 运行测试

```bash
cd fulfillmen-shop-bootstrap
mvn test -Ptest -Dtest=GlobalMDCFilterTest -Dspotless.skip=true
```

### 测试端点

启动应用后，可以访问以下测试端点：

- `GET /test/mdc/info` - 获取当前请求的 MDC 信息
- `GET /test/mdc/log` - 测试日志输出
- `GET /test/mdc/ip` - 测试 IP 地址处理和显示

## 注意事项

1. **线程安全**：MDC 是基于 ThreadLocal 实现的，每个线程都有独立的 MDC 上下文
2. **异步处理**：如果使用异步处理，需要手动传递 MDC 信息到子线程
3. **性能影响**：过滤器会对每个请求进行处理，但已经优化了性能，忽略了不必要的请求
4. **内存管理**：过滤器会在请求结束后自动清理 MDC 信息，无需手动清理

## 扩展

如果需要添加更多的 MDC 信息，可以：

1. 在 `GlobalMDCFilter.MDC_KEYS` 中添加新的键名常量
2. 在 `setBasicRequestInfoToMDC` 或 `setUserInfoToMDC` 方法中添加设置逻辑
3. 在 `clearMDC` 方法中添加清理逻辑
4. 更新 `logback-spring.xml` 中的日志格式以包含新的 MDC 信息
