# SaToken 上下文问题解决方案

## 问题描述

在 GlobalMDCFilter 中调用 `StpUtil.isLogin()` 时抛出异常：
```
cn.dev33.satoken.exception.SaTokenContextException: SaTokenContext 上下文尚未初始化
```

## 问题分析

### 根本原因

**执行顺序问题**：SaToken 使用拦截器（Interceptor）而不是过滤器（Filter）来初始化上下文。

```
请求流程：
请求 → 过滤器链 → DispatcherServlet → 拦截器链 → Controller
       ↑ MDC过滤器在这里执行    ↑ SaToken拦截器在这里初始化上下文
```

### 技术细节

1. **SaToken 架构**：
   - 使用 `SaInterceptor` 拦截器处理认证
   - 拦截器在 `preHandle` 方法中初始化上下文
   - 拦截器执行顺序在过滤器之后

2. **MDC 过滤器**：
   - 在过滤器链中执行
   - 尝试获取用户信息设置到 MDC
   - 此时 SaToken 上下文尚未初始化

## 解决方案

### 方案一：健壮的异常处理（推荐）

#### 1. 添加 SaToken 上下文检查

在 `GlobalMDCFilter` 中添加安全的上下文检查：

```java
/**
 * 检查 SaToken 上下文是否可用
 */
private boolean isSaTokenContextAvailable() {
    try {
        StpUtil.getTokenName();
        return true;
    } catch (SaTokenContextException e) {
        log.debug("SaToken 上下文未初始化: {}", e.getMessage());
        return false;
    } catch (Exception e) {
        log.debug("检查 SaToken 上下文时发生异常: {}", e.getMessage());
        return false;
    }
}
```

#### 2. 安全的用户信息设置

```java
private void setUserInfoToMDC() {
    try {
        if (!shouldIncludeUserInfo()) {
            return;
        }

        // 检查 SaToken 上下文是否可用
        if (!isSaTokenContextAvailable()) {
            log.debug("SaToken 上下文尚未初始化，跳过用户信息设置到 MDC");
            return;
        }

        // 安全地获取用户信息
        if (StpUtil.isLogin()) {
            // 设置用户信息到 MDC
        }
    } catch (Exception e) {
        log.warn("设置用户信息到 MDC 失败", e);
    }
}
```

### 方案二：工具类封装（增强方案）

创建 `SaTokenContextUtils` 工具类：

```java
public final class SaTokenContextUtils {
    
    public static boolean isContextAvailable() {
        try {
            StpUtil.getTokenName();
            return true;
        } catch (SaTokenContextException e) {
            return false;
        }
    }
    
    public static void executeIfLoggedIn(Consumer<Object> action) {
        Object loginId = getLoginIdSafely();
        if (loginId != null) {
            action.accept(loginId);
        }
    }
}
```

使用工具类：

```java
// 在 GlobalMDCFilter 中使用
SaTokenContextUtils.executeIfLoggedIn(loginId -> {
    MDC.put(MDC_KEYS.USER_ID, loginId.toString());
    // 设置其他用户信息
});
```

## 实施步骤

### 1. 更新 GlobalMDCFilter

- ✅ 添加 `SaTokenContextException` 导入
- ✅ 实现 `isSaTokenContextAvailable()` 方法
- ✅ 更新 `setUserInfoToMDC()` 方法
- ✅ 使用 `SaTokenContextUtils` 工具类

### 2. 创建工具类

- ✅ 创建 `SaTokenContextUtils` 类
- ✅ 实现安全的上下文检查方法
- ✅ 提供便捷的操作方法

### 3. 更新测试

- ✅ 添加 SaToken 上下文异常测试
- ✅ 验证异常处理的健壮性

## 验证结果

### 测试通过

```bash
mvn test -Ptest -Dtest=GlobalMDCFilterTest -Dspotless.skip=true
# Tests run: 7, Failures: 0, Errors: 0, Skipped: 0
```

### 日志输出

**修复前**：
```
SaTokenContextException: SaTokenContext 上下文尚未初始化
```

**修复后**：
```
DEBUG - SaToken 上下文尚未初始化，跳过用户信息设置到 MDC
```

## 优势

### 1. **健壮性**
- 不会因为 SaToken 上下文问题导致请求失败
- 优雅地处理异常情况

### 2. **兼容性**
- 不影响现有的 SaToken 架构
- 保持过滤器和拦截器的正常执行顺序

### 3. **可维护性**
- 提供了工具类封装常用操作
- 清晰的日志说明问题原因

### 4. **性能**
- 轻量级的上下文检查
- 避免不必要的异常抛出

## 最佳实践

### 1. 在过滤器中使用 SaToken

```java
// ❌ 错误做法
if (StpUtil.isLogin()) {
    // 可能抛出 SaTokenContextException
}

// ✅ 正确做法
SaTokenContextUtils.executeIfLoggedIn(loginId -> {
    // 安全的操作
});
```

### 2. 日志级别

```java
// 使用 DEBUG 级别记录上下文状态
log.debug("SaToken 上下文未初始化，跳过用户信息设置");

// 使用 WARN 级别记录真正的异常
log.warn("设置用户信息到 MDC 失败", e);
```

### 3. 配置控制

```yaml
fulfillmen:
  filter:
    mdc:
      include-user-info: false  # 如果不需要用户信息可以禁用
```

## 总结

通过实施健壮的异常处理和工具类封装，成功解决了 SaToken 上下文未初始化的问题。这个解决方案：

- ✅ 解决了异常问题
- ✅ 保持了架构的清晰性
- ✅ 提供了可重用的工具类
- ✅ 通过了所有测试验证

现在 MDC 过滤器可以安全地在 SaToken 上下文初始化之前执行，不会影响应用的正常运行。
