# MDC 过滤器重构说明

## 重构背景

根据用户需求，删除了 MDC 过滤器中关于用户登录信息的处理逻辑，让登录相关的 MDC 信息完全由 SaToken 原有机制处理。

## 重构内容

### 删除的功能

1. **用户信息设置**：
   - 删除了 `setUserInfoToMDC()` 方法
   - 删除了用户 ID 和租户 ID 的 MDC 设置逻辑

2. **SaToken 相关代码**：
   - 删除了 `SaTokenContextUtils` 工具类
   - 删除了 `isSaTokenContextAvailable()` 方法
   - 删除了 SaToken 上下文检查逻辑

3. **配置项**：
   - 删除了 `includeUserInfo` 配置项
   - 删除了相关的配置描述和示例

4. **MDC 常量**：
   - 删除了 `TENANT_ID` 和 `USER_ID` 常量
   - 只保留基本请求信息的常量

### 保留的功能

1. **基本请求信息**：
   - ✅ IP 地址获取和标准化
   - ✅ 浏览器信息解析
   - ✅ 操作系统信息解析

2. **配置管理**：
   - ✅ 统一过滤器配置
   - ✅ 灵活的开关控制
   - ✅ 环境特定配置

3. **性能优化**：
   - ✅ URL 忽略机制
   - ✅ 异常处理
   - ✅ 资源清理

## 当前 MDC 字段

### 此过滤器负责的字段

| 字段 | 说明 | 示例值 |
|------|------|--------|
| `ip` | 客户端 IP 地址 | `127.0.0.1` |
| `os` | 操作系统信息 | `OSX` |
| `browser` | 浏览器信息 | `Chrome *********` |

### SaToken 负责的字段

| 字段 | 说明 | 示例值 |
|------|------|--------|
| `tenantId` | 租户 ID | `10000` |
| `userId` | 用户 ID | `12345` |

## 日志格式示例

```
2025-07-05 14:00:00 INFO [http-nio-8080-exec-1] [tid::uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] com.example.Controller - 处理请求
```

> **说明**：`tid` 和 `uId` 字段为空是正常的，它们由 SaToken 在适当的时机填充。

## 配置示例

```yaml
fulfillmen:
  filter:
    mdc:
      enabled: true                   # 启用 MDC 过滤器
      normalize-ip-address: true      # IP 地址标准化
      include-browser-info: true      # 包含浏览器信息
      include-os-info: true           # 包含操作系统信息
```

## 使用方式

### 获取基本请求信息

```java
@RestController
public class ExampleController {
    
    @GetMapping("/example")
    public String example() {
        // 获取此过滤器设置的信息
        String ip = MDC.get(GlobalMDCFilter.MDC_KEYS.IP);
        String browser = MDC.get(GlobalMDCFilter.MDC_KEYS.BROWSER);
        String os = MDC.get(GlobalMDCFilter.MDC_KEYS.OS);
        
        // 获取 SaToken 设置的信息
        String tenantId = MDC.get("tenantId");
        String userId = MDC.get("userId");
        
        return "success";
    }
}
```

## 优势

1. **职责清晰**：
   - MDC 过滤器只负责基本请求信息
   - SaToken 负责用户认证相关信息

2. **避免冲突**：
   - 不再有 SaToken 上下文未初始化的问题
   - 不会干扰 SaToken 的正常工作流程

3. **简化维护**：
   - 减少了复杂的异常处理逻辑
   - 降低了代码复杂度

4. **性能提升**：
   - 减少了不必要的 SaToken 调用
   - 避免了异常处理的开销

## 测试验证

所有测试通过：
```bash
mvn test -Ptest -Dtest=GlobalMDCFilterTest -Dspotless.skip=true
# Tests run: 7, Failures: 0, Errors: 0, Skipped: 0
```

## 总结

通过这次重构：

- ✅ 删除了用户登录信息的处理逻辑
- ✅ 保持了基本请求信息的功能
- ✅ 简化了代码结构
- ✅ 避免了 SaToken 上下文问题
- ✅ 保持了配置的灵活性

现在 MDC 过滤器专注于基本请求信息的处理，用户登录信息完全由 SaToken 原有机制管理，职责更加清晰，维护更加简单。
