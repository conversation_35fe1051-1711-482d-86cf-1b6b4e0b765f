# 产品同步责任链配置示例
product:
  sync:
    chain:
      # 启用责任链模式
      enabled: true
      
      # 调试模式
      debug: false
      
      # 性能监控
      performance-monitoring: true
      
      # 降级处理
      fallback-enabled: true
      
      # 超时配置
      handler-timeout-ms: 5000
      chain-timeout-ms: 30000
      
      # 验证配置
      validation:
        strict-mode: false
        max-product-id-length: 20
        allow-empty-title: false
        allow-empty-images: false
      
      # API配置
      api:
        timeout-ms: 10000
        cache-enabled: true
        cache-expiry-seconds: 300
        fallback-on-failure: true
        max-retries: 2
        retry-interval-ms: 1000
      
      # 缓存配置
      cache:
        multi-level-enabled: true
        l1-cache-size: 1000
        l1-expiry-seconds: 300
        l2-expiry-seconds: 3600
        preload-enabled: false
      
      # 重试配置
      retry:
        enabled: true
        max-attempts: 3
        interval-ms: 1000
        backoff-multiplier: 2.0
        max-interval-ms: 10000
        retryable-exceptions:
          - "java.net.SocketTimeoutException"
          - "java.net.ConnectException"
          - "org.springframework.dao.DataAccessException"
      
      # 处理器配置
      handlers:
        data-validation:
          enabled: true
          timeout-ms: 1000
          continue-on-failure: false
          priority: 10
        
        existing-data-check:
          enabled: true
          timeout-ms: 2000
          continue-on-failure: true
          priority: 20
        
        api-data-fetch:
          enabled: true
          timeout-ms: 10000
          fallback-supported: true
          priority: 30
        
        spu-process:
          enabled: true
          timeout-ms: 3000
          continue-on-failure: false
          priority: 40
        
        sku-process:
          enabled: true
          timeout-ms: 5000
          continue-on-failure: false
          priority: 50
        
        data-integrity:
          enabled: true
          timeout-ms: 2000
          continue-on-failure: false
          priority: 60
        
        cache-update:
          enabled: true
          timeout-ms: 1000
          continue-on-failure: true
          priority: 70
        
        fallback:
          enabled: true
          timeout-ms: 3000
          continue-on-failure: false
          priority: 999
      
      # 监控配置
      monitoring:
        enabled: true
        record-execution-time: true
        record-success-rate: true
        slow-query-monitoring: true
        slow-query-threshold-ms: 3000
        error-rate-monitoring: true
        error-rate-threshold: 5.0
        data-retention-days: 7

# 日志配置
logging:
  level:
    com.fulfillmen.shop.manager.service.sync.chain: DEBUG
    
# 缓存配置
spring:
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=1000,expireAfterWrite=300s
