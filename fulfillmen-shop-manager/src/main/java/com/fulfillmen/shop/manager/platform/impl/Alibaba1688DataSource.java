/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.platform.impl;

import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.domain.entity.PdcProductMapping;
import com.fulfillmen.shop.domain.entity.enums.PlatformCodeEnum;
import com.fulfillmen.shop.manager.core.repository.PdcProductMappingRepository;
import com.fulfillmen.shop.manager.platform.IPlatformDataSource;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 1688平台数据源实现
 *
 * <pre>
 * 作为现有 {@link PdcProductMappingRepository} 的适配器，为多平台架构提供统一接口
 * 提供产品详情、批量获取产品详情、同步产品数据到映射表、批量同步产品数据到映射表、检查数据源是否可用、获取数据源优先级等方法
 * TODO： 暂时未启用
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/06/19
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class Alibaba1688DataSource implements IPlatformDataSource {

    private final PdcProductMappingRepository pdcProductMappingRepository;

    @Override
    public PlatformCodeEnum getSupportedPlatform() {
        return PlatformCodeEnum.PLATFORM_CODE_1688;
    }

    @Override
    public AlibabaProductDetailDTO getProductDetail(String platformProductId, boolean forceRefresh) {
        log.debug("1688平台获取产品详情: {}, forceRefresh: {}", platformProductId, forceRefresh);

        try {
            Long productId = Long.valueOf(platformProductId);
            return pdcProductMappingRepository.getProductDetailWithCache(productId, forceRefresh);
        } catch (Exception e) {
            log.error("1688平台获取产品详情失败: {}", platformProductId, e);
            return null;
        }
    }

    @Override
    public List<AlibabaProductDetailDTO> batchGetProductDetails(List<String> platformProductIds, boolean forceRefresh) {
        log.debug("1688平台批量获取产品详情: 数量={}, forceRefresh: {}", platformProductIds.size(), forceRefresh);

        return platformProductIds.stream().map(id -> getProductDetail(id, forceRefresh)).filter(detail -> detail != null).collect(Collectors.toList());
    }

    @Override
    public PdcProductMapping syncToMapping(String platformProductId) {
        log.debug("1688平台同步产品到映射表: {}", platformProductId);

        try {
            // 通过获取产品详情触发同步逻辑
            AlibabaProductDetailDTO productDetail = getProductDetail(platformProductId, true);

            if (productDetail != null) {
                // 从Repository查询同步后的PdcProductMapping
                return findPdcMapping(platformProductId);
            }

            return null;
        } catch (Exception e) {
            log.error("1688平台同步产品失败: {}", platformProductId, e);
            return null;
        }
    }

    @Override
    public List<PdcProductMapping> batchSyncToMapping(List<String> platformProductIds) {
        log.info("1688平台批量同步产品: 数量={}", platformProductIds.size());

        return platformProductIds.stream().map(this::syncToMapping).filter(mapping -> mapping != null).collect(Collectors.toList());
    }

    @Override
    public boolean isAvailable() {
        try {
            // 简单检查Repository是否可用
            return pdcProductMappingRepository != null;
        } catch (Exception e) {
            log.warn("1688平台数据源不可用", e);
            return false;
        }
    }

    @Override
    public int getPriority() {
        // 1688平台为主要数据源，优先级最高
        return 1;
    }

    /**
     * 查找PdcProductMapping
     */
    private PdcProductMapping findPdcMapping(String platformProductId) {
        try {
            // 这里需要通过Repository查询，但当前Repository接口没有直接查询方法
            // 暂时返回null，后续需要扩展Repository接口
            log.debug("查找PdcProductMapping: {}", platformProductId);
            return null;
        } catch (Exception e) {
            log.warn("查找PdcProductMapping失败: {}", platformProductId, e);
            return null;
        }
    }
}
