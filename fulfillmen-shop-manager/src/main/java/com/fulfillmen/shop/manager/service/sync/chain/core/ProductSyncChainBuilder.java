/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.service.sync.chain.core;

import com.fulfillmen.shop.manager.service.sync.chain.enums.SyncType;
import com.fulfillmen.shop.manager.service.sync.chain.handlers.ApiDataFetchHandler;
import com.fulfillmen.shop.manager.service.sync.chain.handlers.CacheUpdateHandler;
import com.fulfillmen.shop.manager.service.sync.chain.handlers.DataIntegrityHandler;
import com.fulfillmen.shop.manager.service.sync.chain.handlers.DataValidationHandler;
import com.fulfillmen.shop.manager.service.sync.chain.handlers.ExistingDataCheckHandler;
import com.fulfillmen.shop.manager.service.sync.chain.handlers.FallbackHandler;
import com.fulfillmen.shop.manager.service.sync.chain.handlers.SkuProcessHandler;
import com.fulfillmen.shop.manager.service.sync.chain.handlers.SpuProcessHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

/**
 * 产品同步处理器链构建器
 *
 * <pre>
 * 构建器职责：
 * 1. 根据同步类型构建不同的处理器链
 * 2. 管理处理器的顺序和组合
 * 3. 提供灵活的链条配置
 * 4. 支持动态处理器注册
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/07/05
 * @since 2.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ProductSyncChainBuilder {

    // 注入所有处理器
    private final DataValidationHandler dataValidationHandler;
    private final ExistingDataCheckHandler existingDataCheckHandler;
    private final ApiDataFetchHandler apiDataFetchHandler;
    private final SpuProcessHandler spuProcessHandler;
    private final SkuProcessHandler skuProcessHandler;
    private final DataIntegrityHandler dataIntegrityHandler;
    private final CacheUpdateHandler cacheUpdateHandler;
    private final FallbackHandler fallbackHandler;

    /**
     * 构建标准同步处理器链
     */
    public ProductSyncHandler buildStandardChain() {
        log.debug("构建标准同步处理器链");

        return dataValidationHandler
            .setNext(existingDataCheckHandler)
            .setNext(apiDataFetchHandler)
            .setNext(spuProcessHandler)
            .setNext(skuProcessHandler)
            .setNext(dataIntegrityHandler)
            .setNext(cacheUpdateHandler);
    }

    /**
     * 构建强制重新同步处理器链
     */
    public ProductSyncHandler buildForceResyncChain() {
        log.debug("构建强制重新同步处理器链");

        return dataValidationHandler
            .setNext(existingDataCheckHandler)
            .setNext(apiDataFetchHandler)
            .setNext(spuProcessHandler)
            .setNext(skuProcessHandler)
            .setNext(dataIntegrityHandler)
            .setNext(cacheUpdateHandler);
    }

    /**
     * 构建修复同步处理器链
     */
    public ProductSyncHandler buildRepairChain() {
        log.debug("构建修复同步处理器链");

        // 修复同步需要额外的数据完整性检查
        return dataValidationHandler
            .setNext(existingDataCheckHandler)
            .setNext(apiDataFetchHandler);
        // 后续添加数据完整性处理器
        // .setNext(dataIntegrityHandler)
        // .setNext(spuProcessHandler)
        // .setNext(skuProcessHandler)
        // .setNext(cacheUpdateHandler);
    }

    /**
     * 构建批量同步处理器链
     */
    public ProductSyncHandler buildBatchChain() {
        log.debug("构建批量同步处理器链");

        // 批量同步可能需要不同的缓存策略
        return dataValidationHandler
            .setNext(existingDataCheckHandler)
            .setNext(apiDataFetchHandler);
        // 批量处理优化的处理器
    }

    /**
     * 构建降级处理器链
     */
    public ProductSyncHandler buildFallbackChain() {
        log.debug("构建降级处理器链");

        // 降级处理链只包含必要的处理器
        return dataValidationHandler
            .setNext(apiDataFetchHandler)
            .setNext(fallbackHandler);
    }

    /**
     * 构建验证同步处理器链
     */
    public ProductSyncHandler buildValidationChain() {
        log.debug("构建验证同步处理器链");

        // 验证同步只检查数据，不进行修改
        return dataValidationHandler
            .setNext(existingDataCheckHandler);
        // .setNext(dataIntegrityHandler);
    }

    /**
     * 根据同步类型构建处理器链
     */
    public ProductSyncHandler buildChain(SyncType syncType) {
        log.debug("根据同步类型构建处理器链: {}", syncType);

        switch (syncType) {
            case STANDARD:
                return buildStandardChain();
            case FORCE_RESYNC:
                return buildForceResyncChain();
            case REPAIR:
                return buildRepairChain();
            case BATCH:
                return buildBatchChain();
            case FALLBACK:
                return buildFallbackChain();
            case VALIDATION:
                return buildValidationChain();
            case INCREMENTAL:
                return buildIncrementalChain();
            default:
                log.warn("未知的同步类型，使用标准链: {}", syncType);
                return buildStandardChain();
        }
    }

    /**
     * 构建增量同步处理器链
     */
    private ProductSyncHandler buildIncrementalChain() {
        log.debug("构建增量同步处理器链");

        // 增量同步需要特殊的变更检测逻辑
        return dataValidationHandler
            .setNext(existingDataCheckHandler)
            .setNext(apiDataFetchHandler);
        // 后续添加增量处理器
    }

    /**
     * 构建自定义处理器链
     */
    public ProductSyncHandler buildCustomChain(List<ProductSyncHandler> handlers) {
        if (handlers == null || handlers.isEmpty()) {
            log.warn("自定义处理器列表为空，返回标准链");
            return buildStandardChain();
        }

        log.debug("构建自定义处理器链，处理器数量: {}", handlers.size());

        // 按优先级排序
        handlers.sort(Comparator.comparingInt(ProductSyncHandler::getPriority));

        // 构建链条
        ProductSyncHandler head = handlers.get(0);
        ProductSyncHandler current = head;

        for (int i = 1; i < handlers.size(); i++) {
            current = current.setNext(handlers.get(i));
        }

        return head;
    }

    /**
     * 获取所有可用的处理器
     */
    public List<ProductSyncHandler> getAllHandlers() {
        return Arrays.asList(
            dataValidationHandler,
            existingDataCheckHandler,
            apiDataFetchHandler,
            spuProcessHandler,
            skuProcessHandler,
            dataIntegrityHandler,
            cacheUpdateHandler,
            fallbackHandler
        );
    }

    /**
     * 验证处理器链的完整性
     */
    public boolean validateChain(ProductSyncHandler head) {
        if (head == null) {
            log.error("处理器链头部为空");
            return false;
        }

        ProductSyncHandler current = head;
        int count = 0;
        final int MAX_CHAIN_LENGTH = 20; // 防止无限循环

        while (current != null && count < MAX_CHAIN_LENGTH) {
            log.debug("验证处理器: {}", current.getHandlerName());
            current = current.getNext();
            count++;
        }

        if (count >= MAX_CHAIN_LENGTH) {
            log.error("处理器链可能存在循环引用");
            return false;
        }

        log.debug("处理器链验证通过，长度: {}", count);
        return true;
    }

    /**
     * 打印处理器链信息
     */
    public void printChainInfo(ProductSyncHandler head) {
        if (head == null) {
            log.info("处理器链为空");
            return;
        }

        StringBuilder chainInfo = new StringBuilder("处理器链: ");
        ProductSyncHandler current = head;
        int index = 0;

        while (current != null) {
            if (index > 0) {
                chainInfo.append(" -> ");
            }
            chainInfo.append(current.getHandlerName());
            current = current.getNext();
            index++;
        }

        log.info(chainInfo.toString());
    }
}
