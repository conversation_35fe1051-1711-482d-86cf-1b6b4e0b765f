/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.common.tenant.EnhancedTenantContext;
import com.fulfillmen.shop.dao.mapper.TenantDomainsMapper;
import com.fulfillmen.shop.dao.mapper.TenantPlanRelationMapper;
import com.fulfillmen.shop.dao.mapper.TenantPlansMapper;
import com.fulfillmen.shop.dao.mapper.TenantWarehouseMapper;
import com.fulfillmen.shop.dao.mapper.TenantsInfoMapper;
import com.fulfillmen.shop.dao.mapper.TenantsMapper;
import com.fulfillmen.shop.domain.entity.TenantDomains;
import com.fulfillmen.shop.domain.entity.TenantPlanRelation;
import com.fulfillmen.shop.domain.entity.TenantPlans;
import com.fulfillmen.shop.domain.entity.TenantWarehouse;
import com.fulfillmen.shop.domain.entity.Tenants;
import com.fulfillmen.shop.domain.entity.TenantsInfo;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 租户数据加载服务 负责从数据库加载租户完整信息并组装成 EnhancedTenantContext 对象
 *
 * <AUTHOR>
 * @date 2025/7/3 11:00
 * @description: 租户数据加载服务
 * @since 2025-07-03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TenantDataLoader {

    private final TenantsMapper tenantsMapper;
    private final TenantsInfoMapper tenantsInfoMapper;
    private final TenantPlanRelationMapper tenantPlanRelationMapper;
    private final TenantPlansMapper tenantPlansMapper;
    private final TenantDomainsMapper tenantDomainsMapper;
    private final TenantWarehouseMapper tenantWarehouseMapper;
    // 其他需要的 Mapper 可以后续添加

    /**
     * 加载完整的租户上下文信息
     *
     * @param tenantId 租户ID
     * @return 增强的租户上下文，如果不存在则返回null
     */
    public EnhancedTenantContext loadTenantContext(String tenantId) {
        try {
            Long tenantIdLong = Long.parseLong(tenantId);

            // 1. 加载租户基础信息
            Tenants tenant = tenantsMapper.selectById(tenantIdLong);
            if (tenant == null) {
                log.warn("租户不存在: {}", tenantId);
                return null;
            }

            // 2. 加载租户详细信息
            TenantsInfo tenantInfo = tenantsInfoMapper.selectById(tenantIdLong);

            // 3. 加载套餐关系
            LambdaQueryWrapper<TenantPlanRelation> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper
                .eq(TenantPlanRelation::getTenantId, tenantIdLong)
                .orderByDesc(TenantPlanRelation::getExpiredTime);
            List<TenantPlanRelation> planRelations = tenantPlanRelationMapper.selectList(queryWrapper);

            // 4. 加载域名列表
            LambdaQueryWrapper<TenantDomains> domainsLambdaQueryWrapper = new LambdaQueryWrapper<>();
            domainsLambdaQueryWrapper
                .eq(TenantDomains::getTenantId, tenantIdLong);
            List<TenantDomains> domains = tenantDomainsMapper.selectList(domainsLambdaQueryWrapper);

            // 5. 加载仓库列表
            LambdaQueryWrapper<TenantWarehouse> warehouseLambdaQueryWrapper = new LambdaQueryWrapper<>();
            warehouseLambdaQueryWrapper
                .eq(TenantWarehouse::getTenantId, tenantIdLong)
                .orderByAsc(TenantWarehouse::getWarehouseSort);
            List<TenantWarehouse> warehouses = tenantWarehouseMapper.selectList(warehouseLambdaQueryWrapper);

            // 6. 组装 EnhancedTenantContext
            return buildEnhancedTenantContext(tenant, tenantInfo, planRelations, domains, warehouses);

        } catch (Exception e) {
            log.error("加载租户数据失败: {}", tenantId, e);
            return null;
        }
    }

    /**
     * 组装增强的租户上下文
     */
    private EnhancedTenantContext buildEnhancedTenantContext(
        Tenants tenant,
        TenantsInfo tenantInfo,
        List<TenantPlanRelation> planRelations,
        List<TenantDomains> domains,
        List<TenantWarehouse> warehouses
    ) {

        // 构建基础信息
        EnhancedTenantContext.TenantBasicInfo basicInfo = EnhancedTenantContext.TenantBasicInfo.builder()
            .id(tenant.getId())
            .domainPrefix(tenant.getDomainPrefix())
            .username(tenant.getUsername())
            .email(tenant.getEmail())
            .phone(tenant.getPhone())
            .avatar(tenant.getAvatar())
            .gender(tenant.getGender())
            .birth(tenant.getBirth())
            .lastLoginTime(tenant.getLastLoginTime())
            .lastLoginCount(tenant.getLastLoginCount())
            .lastLoginIp(tenant.getLastLoginIp())
            .lastLoginLocation(tenant.getLastLoginLocation())
            .lastLoginBrowser(tenant.getLastLoginBrowser())
            .lastLoginOs(tenant.getLastLoginOs())
            .lastLoginDevice(tenant.getLastLoginDevice())
            .status(tenant.getStatus() != null ? tenant.getStatus().toString() : "active")
            .gmtCreated(tenant.getGmtCreated())
            .gmtModified(tenant.getGmtModified())
            .build();

        // 构建详细信息
        EnhancedTenantContext.TenantDetailInfo detailInfo = null;
        if (tenantInfo != null) {
            detailInfo = EnhancedTenantContext.TenantDetailInfo.builder()
                .phone(tenantInfo.getPhone())
                .tel(tenantInfo.getTel())
                .companyName(tenantInfo.getCompanyName())
                .country(tenantInfo.getCountry())
                .province(tenantInfo.getProvince())
                .city(tenantInfo.getCity())
                .address(tenantInfo.getAddress())
                .idCard(tenantInfo.getIdCard())
                .businessLicense(tenantInfo.getBusinessLicense())
                .postalCode(tenantInfo.getPostalCode())
                .serviceFee(tenantInfo.getServiceFee())
                .wmsCusId(tenantInfo.getWmsCusId())
                .wmsCusCode(tenantInfo.getWmsCusCode())
                .wmsCusName(tenantInfo.getWmsCusName())
                .wmsApiKey(tenantInfo.getWmsApiKey())
                .wmsApiUrl(tenantInfo.getWmsApiUrl())
                .build();
        }

        // 构建套餐信息 - 获取最新有效的套餐
        EnhancedTenantContext.TenantPlanInfo planInfo = null;
        if (planRelations != null && !planRelations.isEmpty()) {
            // 如果没有有效套餐，使用最新的
            TenantPlanRelation currentPlan = planRelations.stream()
                .filter(plan -> plan.getExpiredTime().isAfter(LocalDateTime.now()))
                .findFirst()
                .orElse(planRelations.get(0));

            if (currentPlan != null) {
                // 获取套餐详细信息
                TenantPlans planDetails = tenantPlansMapper.selectById(currentPlan.getPlanId());
                planInfo = EnhancedTenantContext.TenantPlanInfo.builder()
                    .planId(currentPlan.getPlanId())
                    .planName(currentPlan.getPlanName())
                    .planPrice(currentPlan.getPlanPrice())
                    .planStatus(currentPlan.getStatus() != null ? currentPlan.getStatus().toString() : "active")
                    .expiredTime(currentPlan.getExpiredTime())
                    .maxUsers(planDetails != null ? planDetails.getMaxUsers() : 100)
                    .maxProducts(planDetails != null ? planDetails.getMaxProducts() : 1000)
                    .maxCategories(planDetails != null ? planDetails.getMaxCategories() : 10)
                    .features(planDetails != null ? planDetails.getFeatures() : "{}")
                    .build();
            }
        }

        // 构建域名列表
        List<EnhancedTenantContext.TenantDomainInfo> domainInfoList = domains.stream()
            .map(domain -> EnhancedTenantContext.TenantDomainInfo.builder()
                .id(domain.getId())
                .domainName(domain.getDomainName())
                .certificateInfo(domain.getCertificateInfo())
                .isPrimary(domain.getIsPrimary())
                .verified(domain.getVerified())
                .build())
            .collect(Collectors.toList());

        // 构建仓库列表
        List<EnhancedTenantContext.TenantWarehouseInfo> warehouseInfoList = warehouses.stream()
            .map(warehouse -> {
                Integer isDefault = warehouse.getIsDefault();
                return EnhancedTenantContext.TenantWarehouseInfo.builder()
                    .id(warehouse.getId())
                    .name(warehouse.getName())
                    .warehouseCode(warehouse.getWarehouseCode())
                    .warehouseType(warehouse.getWarehouseType() != null ? warehouse.getWarehouseType().toString() : "1")
                    .warehouseDesc(warehouse.getWarehouseDesc())
                    .warehouseIcon(warehouse.getWarehouseIcon())
                    .warehouseColor(warehouse.getWarehouseColor())
                    .warehouseSort(warehouse.getWarehouseSort())
                    .warehouseStatus(warehouse.getWarehouseStatus() != null ? warehouse.getWarehouseStatus().toString() : "1")
                    .isDefault(isDefault != null && isDefault.equals(1) ? 1 : 0)
                    .country(warehouse.getCountry())
                    .province(warehouse.getProvince())
                    .city(warehouse.getCity())
                    .district(warehouse.getDistrict())
                    .districtCode(warehouse.getDistrictCode())
                    .address(warehouse.getAddress())
                    .postcode(warehouse.getPostcode())
                    .longitude(warehouse.getLongitude())
                    .latitude(warehouse.getLatitude())
                    .contactName(warehouse.getContactName())
                    .contactPhone(warehouse.getContactPhone())
                    .contactEmail(warehouse.getContactEmail())
                    .contactMobile(warehouse.getContactMobile())
                    .remark(warehouse.getRemark())
                    .build();
            })
            .collect(Collectors.toList());

        // 构建本地化信息 - 暂时使用默认值
        EnhancedTenantContext.TenantLocaleInfo localeInfo = EnhancedTenantContext.TenantLocaleInfo.builder()
            .languageCode("zh-CN")
            .currencyCode("CNY")
            .timezone("Asia/Shanghai")
            .dateFormat("yyyy-MM-dd HH:mm:ss")
            .build();

        // 构建缓存元数据
        EnhancedTenantContext.CacheMetadata cacheMetadata = EnhancedTenantContext.CacheMetadata.builder()
            .cacheCreatedTime(LocalDateTime.now())
            .lastAccessTime(LocalDateTime.now())
            .accessCount(0L)
            .cacheVersion("1.0")
            .needRefresh(false)
            .build();

        // 构建完整的租户上下文
        return EnhancedTenantContext.builder()
            .basicInfo(basicInfo)
            .detailInfo(detailInfo)
            .planInfo(planInfo)
            .domains(domainInfoList)
            .warehouses(warehouseInfoList)
            .localeInfo(localeInfo)
            .cacheMetadata(cacheMetadata)
            // 暂时不加载文件存储路径和佣金信息，可以后续添加
            .build();
    }
}
