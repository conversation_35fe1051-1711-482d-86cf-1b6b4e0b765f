/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.common;

import com.fulfillmen.shop.domain.dto.CaptchaDTO;
import jakarta.mail.MessagingException;

/**
 * <AUTHOR>
 * @date 2025/4/24 09:52
 * @description: todo
 * @since 1.0.0
 */
public interface ICaptchaManager {

    /**
     * 获取图形验证码
     *
     * @return 验证码
     */
    CaptchaDTO getCaptchaByImage();

    /**
     * 激活邮箱
     *
     * @param uid      用户 ID
     * @param username 用户名
     * @param email    邮件
     * @return 验证码
     */
    void getCaptchaByActivationMail(Long uid, String username, String email) throws MessagingException;

    /**
     * 获取文字点选验证码
     *
     * @return 验证码
     */
    CaptchaDTO getCaptchaByClick();

    /**
     * 校验验证码
     *
     * @param captcha   验证码
     * @param captchaId 验证码标识
     * @return 校验结果
     */
    boolean validateCaptcha(String captcha, String captchaId);

    /**
     * 发送邮箱验证码
     *
     * @param email 邮箱地址
     * @return
     * @throws MessagingException 邮件发送异常
     */
    String sendEmailCaptcha(String email) throws MessagingException;
}
