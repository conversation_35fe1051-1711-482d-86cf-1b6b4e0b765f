/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.service.sync.chain.handlers;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.dao.mapper.TzProductSkuMapper;
import com.fulfillmen.shop.domain.convert.TzProductMapping;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.domain.entity.TzProductSku;
import com.fulfillmen.shop.domain.entity.TzProductSpu;
import com.fulfillmen.shop.domain.entity.enums.TzProductSpuSingleItemEnum;
import com.fulfillmen.shop.manager.service.sync.chain.core.AbstractProductSyncHandler;
import com.fulfillmen.shop.manager.service.sync.chain.core.ProductSyncContext;
import com.fulfillmen.shop.manager.service.sync.chain.core.ProductSyncResult;
import com.fulfillmen.shop.manager.service.sync.chain.enums.SyncType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * SKU处理器
 *
 * <pre>
 * 处理器职责：
 * 1. 创建或更新SKU数据
 * 2. 处理单品和多规格商品
 * 3. SKU数据映射和转换
 * 4. SKU数据验证和修复
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/07/05
 * @since 2.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SkuProcessHandler extends AbstractProductSyncHandler {

    private final TzProductSkuMapper tzProductSkuMapper;

    @Override
    public String getHandlerName() {
        return "SkuProcessHandler";
    }

    @Override
    public int getPriority() {
        return 50; // 在SPU处理之后执行
    }

    @Override
    protected ProductSyncResult doHandle(ProductSyncContext context) {
        log.debug("开始处理SKU数据: platformProductId={}", context.getPlatformProductId());

        // 1. 检查是否跳过SKU处理
        if (shouldSkipSkuProcess(context)) {
            log.debug("跳过SKU处理: platformProductId={}", context.getPlatformProductId());
            return ProductSyncResult.success(null);
        }

        // 2. 验证必要数据
        if (!validateRequiredData(context)) {
            String errorMsg = "SKU处理所需数据不完整";
            context.addError(errorMsg);
            return ProductSyncResult.failure("SKU_DATA_INCOMPLETE", errorMsg);
        }

        try {
            // 3. 获取SPU信息
            TzProductSpu spu = getSpuForSkuProcess(context);
            if (spu == null) {
                String errorMsg = "无法获取SPU信息进行SKU处理";
                context.addError(errorMsg);
                return ProductSyncResult.failure("SPU_NOT_FOUND", errorMsg);
            }

            // 4. 处理SKU
            List<TzProductSku> processedSkus = processSkus(context, spu);
            if (processedSkus == null) {
                String errorMsg = "SKU处理失败";
                context.addError(errorMsg);
                return ProductSyncResult.failure("SKU_PROCESS_FAILED", errorMsg);
            }

            // 5. 设置处理结果到上下文
            context.setNewSkus(processedSkus);

            // 6. 记录处理成功信息
            recordSkuProcessSuccess(context, processedSkus);

            log.debug("SKU处理完成: platformProductId={}, skuCount={}",
                context.getPlatformProductId(), processedSkus.size());

            return ProductSyncResult.success(null);

        } catch (Exception e) {
            String errorMsg = "SKU处理异常: " + e.getMessage();
            log.error("SKU处理失败: platformProductId={}", context.getPlatformProductId(), e);
            context.addError(errorMsg);
            return ProductSyncResult.failure("SKU_PROCESS_EXCEPTION", errorMsg);
        }
    }

    /**
     * 判断是否应该跳过SKU处理
     */
    private boolean shouldSkipSkuProcess(ProductSyncContext context) {
        // 如果标记跳过同步，则跳过SKU处理
        Boolean skipSync = context.getAttribute("skip_sync");
        if (skipSync != null && skipSync) {
            return true;
        }

        // 如果SPU处理失败，跳过SKU处理
        Boolean spuSuccess = context.getAttribute("SpuProcessHandler.success");
        if (spuSuccess != null && !spuSuccess) {
            return true;
        }

        return false;
    }

    /**
     * 验证处理SKU所需的数据
     */
    private boolean validateRequiredData(ProductSyncContext context) {
        // 检查产品详情数据
        AlibabaProductDetailDTO productDetail = context.getProductDetail();
        if (productDetail == null) {
            context.addError("缺少产品详情数据");
            return false;
        }

        return true;
    }

    /**
     * 获取用于SKU处理的SPU
     */
    private TzProductSpu getSpuForSkuProcess(ProductSyncContext context) {
        // 优先使用新创建的SPU
        TzProductSpu newSpu = context.getNewSpu();
        if (newSpu != null) {
            return newSpu;
        }

        // 使用现有的SPU
        TzProductSpu existingSpu = context.getExistingSpu();
        if (existingSpu != null) {
            return existingSpu;
        }

        return null;
    }

    /**
     * 处理SKU数据
     */
    private List<TzProductSku> processSkus(ProductSyncContext context, TzProductSpu spu) {
        AlibabaProductDetailDTO productDetail = context.getProductDetail();

        // 判断是否需要删除现有SKU
        if (shouldDeleteExistingSkus(context, spu)) {
            deleteExistingSkus(spu.getId());
            addHandlerMetadata(context, "existing_skus_deleted", true);
        }

        // 根据产品类型处理SKU
        if (isSingleItem(spu, productDetail)) {
            return processSingleItemSku(context, spu, productDetail);
        } else {
            return processMultiSpecSkus(context, spu, productDetail);
        }
    }

    /**
     * 判断是否需要删除现有SKU
     */
    private boolean shouldDeleteExistingSkus(ProductSyncContext context, TzProductSpu spu) {
        // 强制更新模式删除现有SKU
        if (context.isForceUpdate() || context.getSyncType() == SyncType.FORCE_RESYNC) {
            return true;
        }

        // 修复模式删除现有SKU
        if (context.getSyncType() == SyncType.REPAIR) {
            return true;
        }

        // 如果是更新操作且SKU数据不完整，删除重建
        String operation = getHandlerMetadata(context, "SpuProcessHandler.operation");
        if ("update".equals(operation)) {
            Boolean skuComplete = context.getAttribute("ExistingDataCheckHandler.sku_complete");
            if (skuComplete != null && !skuComplete) {
                return true;
            }
        }

        return false;
    }

    /**
     * 删除现有SKU
     */
    private void deleteExistingSkus(Long spuId) {
        try {
            LambdaQueryWrapper<TzProductSku> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(TzProductSku::getSpuId, spuId);
            int deleteCount = tzProductSkuMapper.delete(deleteWrapper);
            log.debug("删除现有SKU: spuId={}, 删除数量={}", spuId, deleteCount);
        } catch (Exception e) {
            log.error("删除现有SKU失败: spuId={}", spuId, e);
        }
    }

    /**
     * 判断是否为单品
     */
    private boolean isSingleItem(TzProductSpu spu, AlibabaProductDetailDTO productDetail) {
        // 优先使用SPU中的标记
        if (spu.getIsSingleItem() != null && spu.getIsSingleItem() != TzProductSpuSingleItemEnum.NO) {
            return true;
        }

        // 使用产品详情中的标记
        return productDetail.isSingleItem();
    }

    /**
     * 处理单品SKU
     */
    private List<TzProductSku> processSingleItemSku(ProductSyncContext context, TzProductSpu spu,
        AlibabaProductDetailDTO productDetail) {
        try {
            // 使用Mapstruct创建单品SKU
            TzProductSku singleSku = TzProductMapping.INSTANCE.toSingleItemSku(productDetail, spu.getId());

            if (singleSku == null) {
                log.error("创建单品SKU失败: spuId={}", spu.getId());
                return null;
            }

            // 插入数据库
            int insertResult = tzProductSkuMapper.insert(singleSku);
            if (insertResult <= 0) {
                log.error("单品SKU插入数据库失败: spuId={}", spu.getId());
                return null;
            }

            List<TzProductSku> result = new ArrayList<>();
            result.add(singleSku);

            addHandlerMetadata(context, "sku_type", "single_item");
            addHandlerMetadata(context, "sku_count", 1);

            log.debug("单品SKU创建成功: spuId={}, skuId={}", spu.getId(), singleSku.getId());
            return result;

        } catch (Exception e) {
            log.error("处理单品SKU异常: spuId={}", spu.getId(), e);
            return null;
        }
    }

    /**
     * 处理多规格SKU
     */
    private List<TzProductSku> processMultiSpecSkus(ProductSyncContext context, TzProductSpu spu,
        AlibabaProductDetailDTO productDetail) {
        try {
            // 检查是否有SKU列表
            if (CollectionUtil.isEmpty(productDetail.getProductSkuList())) {
                log.warn("多规格商品但SKU列表为空，创建默认SKU: spuId={}", spu.getId());
                return processSingleItemSku(context, spu, productDetail);
            }

            // 使用Mapstruct创建多规格SKU
            List<TzProductSku> multiSkus = TzProductMapping.INSTANCE.toTzProductSkuList(
                productDetail.getProductSkuList(), spu.getId(), productDetail.getPlatformProductId());

            if (CollectionUtil.isEmpty(multiSkus)) {
                log.error("创建多规格SKU失败: spuId={}", spu.getId());
                return null;
            }

            // 批量插入数据库
            int insertCount = 0;
            for (TzProductSku sku : multiSkus) {
                try {
                    int insertResult = tzProductSkuMapper.insert(sku);
                    if (insertResult > 0) {
                        insertCount++;
                    }
                } catch (Exception e) {
                    log.error("插入SKU失败: spuId={}, skuId={}", spu.getId(), sku.getId(), e);
                }
            }

            if (insertCount == 0) {
                log.error("所有多规格SKU插入失败: spuId={}", spu.getId());
                return null;
            }

            if (insertCount < multiSkus.size()) {
                context.addWarning(String.format("部分SKU插入失败: 成功=%d, 总数=%d", insertCount, multiSkus.size()));
            }

            addHandlerMetadata(context, "sku_type", "multi_spec");
            addHandlerMetadata(context, "sku_count", insertCount);
            addHandlerMetadata(context, "sku_total", multiSkus.size());

            log.debug("多规格SKU创建完成: spuId={}, 成功数量={}, 总数量={}",
                spu.getId(), insertCount, multiSkus.size());

            return multiSkus;

        } catch (Exception e) {
            log.error("处理多规格SKU异常: spuId={}", spu.getId(), e);
            return null;
        }
    }

    /**
     * 记录SKU处理成功信息
     */
    private void recordSkuProcessSuccess(ProductSyncContext context, List<TzProductSku> skus) {
        addHandlerMetadata(context, "success", true);
        addHandlerMetadata(context, "process_time", System.currentTimeMillis());
        addHandlerMetadata(context, "final_sku_count", skus.size());

        if (CollectionUtil.isNotEmpty(skus)) {
            addHandlerMetadata(context, "first_sku_id", skus.get(0).getId());
        }
    }

    @Override
    public boolean canHandle(ProductSyncContext context) {
        // 验证类型的同步不处理SKU
        return context.getSyncType() != SyncType.VALIDATION;
    }

    @Override
    protected boolean shouldContinueOnFailure() {
        return false; // SKU处理失败不继续
    }

    @Override
    protected boolean supportFallback() {
        return true; // 支持降级处理
    }
}
