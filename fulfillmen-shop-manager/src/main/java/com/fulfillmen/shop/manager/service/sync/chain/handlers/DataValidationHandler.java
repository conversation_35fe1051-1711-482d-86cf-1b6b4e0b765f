/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.service.sync.chain.handlers;

import com.fulfillmen.shop.manager.service.sync.chain.core.AbstractProductSyncHandler;
import com.fulfillmen.shop.manager.service.sync.chain.core.ProductSyncContext;
import com.fulfillmen.shop.manager.service.sync.chain.core.ProductSyncResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.regex.Pattern;

/**
 * 数据验证处理器
 * 
 * <pre>
 * 处理器职责：
 * 1. 验证输入参数的有效性
 * 2. 检查platformProductId格式
 * 3. 验证业务规则
 * 4. 设置默认值
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/07/05
 * @since 2.0.0
 */
@Slf4j
@Component
public class DataValidationHandler extends AbstractProductSyncHandler {

    /**
     * 平台产品ID格式正则表达式
     * 1688平台的产品ID通常是数字
     */
    private static final Pattern PLATFORM_PRODUCT_ID_PATTERN = Pattern.compile("^\\d{1,20}$");

    @Override
    public String getHandlerName() {
        return "DataValidationHandler";
    }

    @Override
    public int getPriority() {
        return 10; // 最高优先级，第一个执行
    }

    @Override
    protected ProductSyncResult doHandle(ProductSyncContext context) {
        log.debug("开始数据验证: platformProductId={}", context.getPlatformProductId());

        // 1. 基础参数验证
        if (!validateBasicParameters(context)) {
            return ProductSyncResult.failure("VALIDATION_FAILED", "基础参数验证失败");
        }

        // 2. 平台产品ID格式验证
        if (!validatePlatformProductIdFormat(context)) {
            return ProductSyncResult.failure("INVALID_PLATFORM_PRODUCT_ID", "平台产品ID格式无效");
        }

        // 3. 业务规则验证
        if (!validateBusinessRules(context)) {
            return ProductSyncResult.failure("BUSINESS_RULE_VIOLATION", "业务规则验证失败");
        }

        // 4. 设置默认值
        setDefaultValues(context);

        // 5. 记录验证成功
        addHandlerMetadata(context, "validation_passed", true);
        addHandlerMetadata(context, "validation_time", System.currentTimeMillis());

        log.debug("数据验证通过: platformProductId={}", context.getPlatformProductId());
        return ProductSyncResult.success(null);
    }

    /**
     * 验证基础参数
     */
    private boolean validateBasicParameters(ProductSyncContext context) {
        // 验证上下文对象
        if (!validateContext(context)) {
            return false;
        }

        // 验证平台产品ID
        String platformProductId = context.getPlatformProductId();
        if (platformProductId == null || platformProductId.trim().isEmpty()) {
            context.addError("平台产品ID不能为空");
            return false;
        }

        // 验证同步类型
        if (context.getSyncType() == null) {
            context.addWarning("同步类型为空，使用默认值");
            context.setSyncType(com.fulfillmen.shop.manager.service.sync.chain.enums.SyncType.STANDARD);
        }

        return true;
    }

    /**
     * 验证平台产品ID格式
     */
    private boolean validatePlatformProductIdFormat(ProductSyncContext context) {
        String platformProductId = context.getPlatformProductId().trim();

        // 检查长度
        if (platformProductId.length() > 20) {
            context.addError("平台产品ID长度不能超过20位");
            return false;
        }

        // 检查格式（数字）
        if (!PLATFORM_PRODUCT_ID_PATTERN.matcher(platformProductId).matches()) {
            context.addError("平台产品ID格式无效，必须是数字");
            return false;
        }

        // 检查是否为有效的Long值
        try {
            Long.valueOf(platformProductId);
        } catch (NumberFormatException e) {
            context.addError("平台产品ID数值超出范围");
            return false;
        }

        // 标准化处理：去除前导零（如果有）
        String normalizedId = String.valueOf(Long.valueOf(platformProductId));
        if (!normalizedId.equals(platformProductId)) {
            context.setPlatformProductId(normalizedId);
            context.addWarning("平台产品ID已标准化: " + platformProductId + " -> " + normalizedId);
        }

        return true;
    }

    /**
     * 验证业务规则
     */
    private boolean validateBusinessRules(ProductSyncContext context) {
        // 检查强制更新标志的合理性
        if (context.isForceUpdate() && context.getSyncType().allowCache()) {
            context.addWarning("强制更新标志与同步类型不匹配，将忽略缓存");
        }

        // 检查同步类型的兼容性
        if (context.getSyncType().shouldForceRefresh() && !context.isForceUpdate()) {
            context.setForceUpdate(true);
            context.addWarning("根据同步类型自动启用强制更新");
        }

        return true;
    }

    /**
     * 设置默认值
     */
    private void setDefaultValues(ProductSyncContext context) {
        // 设置请求开始时间（如果未设置）
        if (context.getRequestStartTime() == null) {
            context.setRequestStartTime(java.time.LocalDateTime.now());
        }

        // 初始化集合（如果为null）
        if (context.getWarnings() == null) {
            context.setWarnings(new java.util.ArrayList<>());
        }
        if (context.getErrors() == null) {
            context.setErrors(new java.util.ArrayList<>());
        }
        if (context.getAttributes() == null) {
            context.setAttributes(new java.util.HashMap<>());
        }
        if (context.getHandlerExecutionLog() == null) {
            context.setHandlerExecutionLog(new java.util.ArrayList<>());
        }
    }

    @Override
    protected boolean shouldContinueOnFailure() {
        return false; // 验证失败不继续处理
    }

    @Override
    protected boolean supportFallback() {
        return false; // 验证阶段不支持降级
    }

    @Override
    public boolean canHandle(ProductSyncContext context) {
        // 验证处理器总是需要执行
        return true;
    }
}
