/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.service.sync.chain.handlers;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.template.QuickConfig;
import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.manager.service.sync.chain.core.AbstractProductSyncHandler;
import com.fulfillmen.shop.manager.service.sync.chain.core.ProductSyncContext;
import com.fulfillmen.shop.manager.service.sync.chain.core.ProductSyncResult;
import com.fulfillmen.shop.manager.service.sync.chain.enums.SyncType;
import java.time.Duration;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

/**
 * 缓存更新处理器
 *
 * <pre>
 * 处理器职责：
 * 1. 更新各级缓存数据
 * 2. 缓存失效处理
 * 3. 缓存一致性保证
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/07/05
 * @since 2.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CacheUpdateHandler extends AbstractProductSyncHandler implements InitializingBean {

    private final CacheManager cacheManager;

    /**
     * 产品DTO缓存
     */
    private Cache<String, TzProductDTO> productDTOCache;

    /**
     * 产品详情缓存
     */
    private Cache<String, AlibabaProductDetailDTO> productDetailCache;

    @Override
    public String getHandlerName() {
        return "CacheUpdateHandler";
    }

    @Override
    public int getPriority() {
        return 70; // 在数据完整性检查之后执行
    }

    @Override
    protected ProductSyncResult doHandle(ProductSyncContext context) {
        log.debug("开始更新缓存: platformProductId={}", context.getPlatformProductId());

        try {
            // 检查是否需要更新缓存
            if (context.getSyncType() == SyncType.VALIDATION) {
                return ProductSyncResult.success(null);
            }

            TzProductDTO productDTO = context.getResult();
            if (productDTO == null) {
                context.addWarning("缓存更新跳过：无可用的产品数据");
                return ProductSyncResult.success(null);
            }

            // 更新缓存
            updateCaches(context, context.getPlatformProductId(), productDTO);

            addHandlerMetadata(context, "cache_updated", true);
            log.debug("缓存更新完成: platformProductId={}", context.getPlatformProductId());

            return ProductSyncResult.success(null);

        } catch (Exception e) {
            log.warn("缓存更新失败: platformProductId={}", context.getPlatformProductId(), e);
            context.addWarning("缓存更新失败: " + e.getMessage());
            return ProductSyncResult.success(null); // 缓存失败不中断流程
        }
    }

    /**
     * 更新缓存
     */
    private void updateCaches(ProductSyncContext context, String platformProductId, TzProductDTO productDTO) {
        try {
            // 1. 更新产品DTO缓存
            updateProductDTOCache(platformProductId, productDTO);

            // 2. 更新产品详情缓存（如果有）
            AlibabaProductDetailDTO productDetail = context.getProductDetail();
            if (productDetail != null) {
                updateProductDetailCache(platformProductId, productDetail);
            }

            // 3. 清除相关的VO缓存
            clearRelatedVOCaches(platformProductId);

            log.debug("缓存更新成功: platformProductId={}", platformProductId);

        } catch (Exception e) {
            log.warn("缓存操作失败: platformProductId={}", platformProductId, e);
        }
    }

    /**
     * 更新产品DTO缓存
     */
    private void updateProductDTOCache(String platformProductId, TzProductDTO productDTO) {
        try {
            String cacheKey = buildProductDTOCacheKey(platformProductId);
            productDTOCache.put(cacheKey, productDTO);
            log.debug("产品DTO缓存更新成功: key={}", cacheKey);
        } catch (Exception e) {
            log.error("更新产品DTO缓存失败: platformProductId={}", platformProductId, e);
        }
    }

    /**
     * 更新产品详情缓存
     */
    private void updateProductDetailCache(String platformProductId, AlibabaProductDetailDTO productDetail) {
        try {
            String cacheKey = buildProductDetailCacheKey(platformProductId);
            productDetailCache.put(cacheKey, productDetail);
            log.debug("产品详情缓存更新成功: key={}", cacheKey);
        } catch (Exception e) {
            log.error("更新产品详情缓存失败: platformProductId={}", platformProductId, e);
        }
    }

    /**
     * 清除相关VO缓存
     */
    private void clearRelatedVOCaches(String platformProductId) {
        try {
            // 这里可以根据实际需要清除相关的VO缓存
            // 例如：产品列表缓存、搜索结果缓存等
            log.debug("相关VO缓存清除完成: platformProductId={}", platformProductId);
        } catch (Exception e) {
            log.error("清除相关VO缓存失败: platformProductId={}", platformProductId, e);
        }
    }

    /**
     * 构建产品DTO缓存键
     */
    private String buildProductDTOCacheKey(String platformProductId) {
        return "product:dto:" + platformProductId;
    }

    /**
     * 构建产品详情缓存键
     */
    private String buildProductDetailCacheKey(String platformProductId) {
        return "product:detail:" + platformProductId;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 初始化产品DTO缓存
        QuickConfig productDTOConfig = QuickConfig.newBuilder("product:dto:")
            .localExpire(Duration.ofMinutes(30))  // 本地缓存30分钟
            .expire(Duration.ofHours(2))          // 远程缓存2小时
            .cacheType(CacheType.BOTH)            // 两级缓存
            .syncLocal(true)                      // 同步本地缓存
            .build();
        productDTOCache = cacheManager.getOrCreateCache(productDTOConfig);

        // 初始化产品详情缓存
        QuickConfig productDetailConfig = QuickConfig.newBuilder("product:detail:")
            .localExpire(Duration.ofMinutes(15))  // 本地缓存15分钟
            .expire(Duration.ofHours(1))          // 远程缓存1小时
            .cacheType(CacheType.BOTH)            // 两级缓存
            .syncLocal(true)                      // 同步本地缓存
            .build();
        productDetailCache = cacheManager.getOrCreateCache(productDetailConfig);

        log.info("产品同步缓存初始化完成");
    }

    @Override
    public boolean canHandle(ProductSyncContext context) {
        return context.getSyncType() != SyncType.VALIDATION;
    }

    @Override
    protected boolean shouldContinueOnFailure() {
        return true; // 缓存更新失败不影响主流程
    }
}
