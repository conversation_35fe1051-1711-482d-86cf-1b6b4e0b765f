/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.service.sync.chain.handlers;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.dao.mapper.TzProductSkuMapper;
import com.fulfillmen.shop.dao.mapper.TzProductSpuMapper;
import com.fulfillmen.shop.domain.entity.TzProductSku;
import com.fulfillmen.shop.domain.entity.TzProductSpu;
import com.fulfillmen.shop.domain.entity.enums.PdcProductMappingSyncStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.PlatformCodeEnum;
import com.fulfillmen.shop.manager.service.sync.chain.core.AbstractProductSyncHandler;
import com.fulfillmen.shop.manager.service.sync.chain.core.ProductSyncContext;
import com.fulfillmen.shop.manager.service.sync.chain.core.ProductSyncResult;
import com.fulfillmen.shop.manager.service.sync.chain.enums.SyncType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 现有数据检查处理器
 *
 * <pre>
 * 处理器职责：
 * 1. 检查SPU是否已存在
 * 2. 检查SKU数据完整性
 * 3. 判断是否需要重新同步
 * 4. 设置数据状态标志
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/07/05
 * @since 2.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ExistingDataCheckHandler extends AbstractProductSyncHandler {

    private final TzProductSpuMapper tzProductSpuMapper;
    private final TzProductSkuMapper tzProductSkuMapper;

    /**
     * 数据过期天数阈值
     */
    private static final int DATA_EXPIRY_DAYS = 3;

    @Override
    public String getHandlerName() {
        return "ExistingDataCheckHandler";
    }

    @Override
    public int getPriority() {
        return 20; // 第二个执行
    }

    @Override
    protected ProductSyncResult doHandle(ProductSyncContext context) {
        log.debug("开始检查现有数据: platformProductId={}", context.getPlatformProductId());

        // 1. 检查现有SPU
        TzProductSpu existingSpu = findExistingSpu(context.getPlatformProductId());
        context.setExistingSpu(existingSpu);

        if (existingSpu != null) {
            log.debug("找到现有SPU: spuId={}, platformProductId={}",
                existingSpu.getId(), context.getPlatformProductId());

            // 2. 检查现有SKU
            List<TzProductSku> existingSkus = findExistingSkus(existingSpu.getId());
            context.setExistingSkus(existingSkus);

            // 3. 分析数据状态
            analyzeDataStatus(context, existingSpu, existingSkus);

            // 4. 判断是否需要重新同步
            if (shouldSkipSync(context, existingSpu)) {
                // 数据完整且不需要更新，可以直接使用现有数据
                markAsSkippable(context);
                return ProductSyncResult.success(null);
            }
        } else {
            log.debug("未找到现有SPU，需要创建新数据: platformProductId={}", context.getPlatformProductId());
            addHandlerMetadata(context, "spu_exists", false);
            addHandlerMetadata(context, "needs_create", true);
        }

        // 5. 记录检查结果
        recordCheckResults(context, existingSpu);

        Boolean skipSync = context.getAttribute("skip_sync");
        log.debug("现有数据检查完成: platformProductId={}, spuExists={}, needsSync={}",
            context.getPlatformProductId(), existingSpu != null, !(skipSync != null && skipSync));

        return ProductSyncResult.success(null);
    }

    /**
     * 查找现有SPU
     */
    private TzProductSpu findExistingSpu(String platformProductId) {
        LambdaQueryWrapper<TzProductSpu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TzProductSpu::getPlatformCode, PlatformCodeEnum.PLATFORM_CODE_1688)
            .eq(TzProductSpu::getPdcPlatformProductId, platformProductId)
            .or()
            .eq(TzProductSpu::getIsPdcSync, PdcProductMappingSyncStatusEnum.SYNCED)
            .eq(TzProductSpu::getPdcProductMappingId, platformProductId);

        return tzProductSpuMapper.selectOne(queryWrapper);
    }

    /**
     * 查找现有SKU
     */
    private List<TzProductSku> findExistingSkus(Long spuId) {
        LambdaQueryWrapper<TzProductSku> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TzProductSku::getSpuId, spuId);
        return tzProductSkuMapper.selectList(queryWrapper);
    }

    /**
     * 分析数据状态
     */
    private void analyzeDataStatus(ProductSyncContext context, TzProductSpu spu, List<TzProductSku> skus) {
        // 检查SPU数据完整性
        boolean spuComplete = isSpuDataComplete(spu);
        addHandlerMetadata(context, "spu_complete", spuComplete);

        if (!spuComplete) {
            context.addWarning("SPU数据不完整，需要更新");
        }

        // 检查SKU数据完整性
        boolean skuComplete = isSkuDataComplete(spu, skus);
        addHandlerMetadata(context, "sku_complete", skuComplete);

        if (!skuComplete) {
            context.addWarning("SKU数据不完整，需要修复");
        }

        // 检查数据是否过期
        boolean dataExpired = isDataExpired(spu);
        addHandlerMetadata(context, "data_expired", dataExpired);

        if (dataExpired) {
            context.addWarning("数据已过期，需要重新同步");
        }

        // 设置整体数据状态
        boolean dataValid = spuComplete && skuComplete && !dataExpired;
        addHandlerMetadata(context, "data_valid", dataValid);
    }

    /**
     * 检查SPU数据是否完整
     */
    private boolean isSpuDataComplete(TzProductSpu spu) {
        return spu != null
            && spu.getTitle() != null && !spu.getTitle().trim().isEmpty()
            && spu.getMainImage() != null && !spu.getMainImage().trim().isEmpty()
            && spu.getPdcPlatformProductId() != null;
    }

    /**
     * 检查SKU数据是否完整
     */
    private boolean isSkuDataComplete(TzProductSpu spu, List<TzProductSku> skus) {
        if (CollectionUtil.isEmpty(skus)) {
            return false;
        }

        // 检查每个SKU的基本信息
        for (TzProductSku sku : skus) {
            if (sku.getPlatformProductId() == null
                || sku.getPrice() == null
                || sku.getDropShippingPrice() == null) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查数据是否过期
     */
    private boolean isDataExpired(TzProductSpu spu) {
        if (spu.getGmtModified() == null) {
            return true; // 没有修改时间，认为过期
        }

        LocalDateTime lastModified = spu.getGmtModified();
        LocalDateTime now = LocalDateTime.now();
        Duration timeSinceUpdate = Duration.between(lastModified, now);

        return timeSinceUpdate.toDays() >= DATA_EXPIRY_DAYS;
    }

    /**
     * 判断是否应该跳过同步
     */
    private boolean shouldSkipSync(ProductSyncContext context, TzProductSpu spu) {
        // 强制更新或强制重新同步类型不跳过
        if (context.isForceUpdate() || context.getSyncType().shouldForceRefresh()) {
            return false;
        }

        // 数据无效不跳过
        Boolean dataValid = getHandlerMetadata(context, "data_valid");
        if (dataValid == null || !dataValid) {
            return false;
        }

        // 修复类型的同步不跳过
        if (context.getSyncType() == SyncType.REPAIR) {
            return false;
        }

        return true;
    }

    /**
     * 标记为可跳过同步
     */
    private void markAsSkippable(ProductSyncContext context) {
        context.setAttribute("skip_sync", true);
        context.setAttribute("use_existing_data", true);
        addHandlerMetadata(context, "sync_skipped", true);
        addHandlerMetadata(context, "skip_reason", "数据完整且未过期");

        log.debug("标记跳过同步: platformProductId={}", context.getPlatformProductId());
    }

    /**
     * 记录检查结果
     */
    private void recordCheckResults(ProductSyncContext context, TzProductSpu existingSpu) {
        addHandlerMetadata(context, "spu_exists", existingSpu != null);
        addHandlerMetadata(context, "check_time", System.currentTimeMillis());

        if (existingSpu != null) {
            addHandlerMetadata(context, "spu_id", existingSpu.getId());
            addHandlerMetadata(context, "spu_last_modified", existingSpu.getGmtModified());

            List<TzProductSku> skus = context.getExistingSkus();
            addHandlerMetadata(context, "sku_count", skus != null ? skus.size() : 0);
        }
    }

    @Override
    public boolean canHandle(ProductSyncContext context) {
        // 所有同步类型都需要检查现有数据
        return true;
    }

    @Override
    protected boolean shouldContinueOnFailure() {
        return true; // 检查失败也继续处理，可能是新数据
    }
}
