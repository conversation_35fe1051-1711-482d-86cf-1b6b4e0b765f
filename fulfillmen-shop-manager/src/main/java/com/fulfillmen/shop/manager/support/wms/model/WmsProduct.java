/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.wms.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * WMS 产品模型（用于创建和查询）
 *
 * <AUTHOR>
 * @date 2025/7/1
 * @description: WMS Product model for creation and querying
 * @since 1.0.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WmsProduct {

    /**
     * 客户id
     */
    @JsonProperty("CustomerID")
    private String customerId;

    /**
     * 总数量
     */
    @JsonProperty("count")
    private Integer count;

    /**
     * sku
     */
    @JsonProperty("SKU")
    private String sku;

    /**
     * 产品条码（不可重复）
     */
    @JsonProperty("Barcode")
    private String barcode;

    /**
     * 英文名称
     */
    @JsonProperty("EnName")
    private String enName;

    /**
     * 中文名称
     */
    @JsonProperty("CnName")
    private String cnName;

    /**
     * 描述
     */
    @JsonProperty("Remark")
    private String remark;

    /**
     * 价格
     */
    @JsonProperty("Price")
    private BigDecimal price;

    /**
     * 重量
     */
    @JsonProperty("Weight")
    private BigDecimal weight;

    /**
     * 长度
     */
    @JsonProperty("Length")
    private BigDecimal length;

    /**
     * 宽度
     */
    @JsonProperty("Width")
    private BigDecimal width;

    /**
     * 高度
     */
    @JsonProperty("Height")
    private BigDecimal height;

    /**
     * 海关编码
     */
    @JsonProperty("HSCode")
    private String hsCode;

    /**
     * 原产地
     */
    @JsonProperty("Origin")
    private String origin;

    /**
     * 品牌
     */
    @JsonProperty("Brand")
    private String brand;

    /**
     * 是否有电池，0不含，1含电池
     */
    @JsonProperty("Battery")
    private String battery;

    /**
     * 预警数量
     */
    @JsonProperty("ExpectNum")
    private Integer expectNum;

    /**
     * 质检重量
     */
    @JsonProperty("CheckWeight")
    private String checkWeight;

    /**
     * 质检长度
     */
    @JsonProperty("CheckLength")
    private String checkLength;

    /**
     * 质检宽度
     */
    @JsonProperty("CheckWidth")
    private String checkWidth;

    /**
     * 质检高度
     */
    @JsonProperty("CheckHeight")
    private String checkHeight;

    @JsonProperty("temprownumber")
    private Integer tempRowNumber;
}
