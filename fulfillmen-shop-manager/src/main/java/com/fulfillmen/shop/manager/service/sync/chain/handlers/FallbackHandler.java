/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.service.sync.chain.handlers;

import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.manager.service.sync.chain.core.AbstractProductSyncHandler;
import com.fulfillmen.shop.manager.service.sync.chain.core.ProductSyncContext;
import com.fulfillmen.shop.manager.service.sync.chain.core.ProductSyncResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 降级处理器
 * 
 * <pre>
 * 处理器职责：
 * 1. 异常情况下的降级逻辑
 * 2. 构建基础的产品信息
 * 3. 错误恢复处理
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/07/05
 * @since 2.0.0
 */
@Slf4j
@Component
public class FallbackHandler extends AbstractProductSyncHandler {

    @Override
    public String getHandlerName() {
        return "FallbackHandler";
    }

    @Override
    public int getPriority() {
        return 999; // 最低优先级，最后执行
    }

    @Override
    protected ProductSyncResult doHandle(ProductSyncContext context) {
        log.warn("执行降级处理: platformProductId={}", context.getPlatformProductId());

        try {
            // 如果已经有结果，直接返回
            if (context.getResult() != null) {
                return ProductSyncResult.success(context.getResult());
            }

            // 尝试构建基础的产品信息
            TzProductDTO fallbackResult = buildFallbackResult(context);
            if (fallbackResult != null) {
                context.setResult(fallbackResult);
                addHandlerMetadata(context, "fallback_used", true);
                log.warn("降级处理成功: platformProductId={}", context.getPlatformProductId());
                return ProductSyncResult.success(fallbackResult).markFallbackUsed();
            }

            // 降级也失败，返回错误
            String errorMsg = "降级处理失败，无法构建基础产品信息";
            context.addError(errorMsg);
            return ProductSyncResult.failure("FALLBACK_FAILED", errorMsg);

        } catch (Exception e) {
            String errorMsg = "降级处理异常: " + e.getMessage();
            log.error("降级处理异常: platformProductId={}", context.getPlatformProductId(), e);
            context.addError(errorMsg);
            return ProductSyncResult.failure("FALLBACK_EXCEPTION", errorMsg);
        }
    }

    /**
     * 构建降级结果
     */
    private TzProductDTO buildFallbackResult(ProductSyncContext context) {
        try {
            AlibabaProductDetailDTO productDetail = context.getProductDetail();
            if (productDetail == null) {
                return null;
            }

            // 构建基础的TzProductDTO
            return TzProductDTO.builder()
                .pdcPlatformProductId(context.getPlatformProductId())
                .title(productDetail.getTitle())
                .titleTrans(productDetail.getTitleTrans())
                .mainImage(getMainImage(productDetail))
                .categoryId(productDetail.getCategoryId())
                .categoryName(productDetail.getCategoryName())
                .description(productDetail.getDescription())
                .unit(productDetail.getUnit())
                .build();

        } catch (Exception e) {
            log.error("构建降级结果失败", e);
            return null;
        }
    }

    private String getMainImage(AlibabaProductDetailDTO productDetail) {
        if (productDetail.getImages() != null && !productDetail.getImages().isEmpty()) {
            return productDetail.getImages().get(0);
        }
        return productDetail.getWhiteImage();
    }

    @Override
    public boolean canHandle(ProductSyncContext context) {
        return context.isUseFallback(); // 只有启用降级时才处理
    }

    @Override
    protected boolean shouldContinueOnFailure() {
        return false; // 降级处理失败不继续
    }

    @Override
    protected boolean supportFallback() {
        return false; // 降级处理器本身不支持降级
    }
}
