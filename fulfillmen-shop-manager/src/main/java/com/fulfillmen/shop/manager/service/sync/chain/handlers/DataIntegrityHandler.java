/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.service.sync.chain.handlers;

import cn.hutool.core.collection.CollectionUtil;
import com.fulfillmen.shop.domain.convert.TzProductMapping;
import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.domain.entity.TzProductSku;
import com.fulfillmen.shop.domain.entity.TzProductSpu;
import com.fulfillmen.shop.manager.service.sync.chain.core.AbstractProductSyncHandler;
import com.fulfillmen.shop.manager.service.sync.chain.core.ProductSyncContext;
import com.fulfillmen.shop.manager.service.sync.chain.core.ProductSyncResult;
import com.fulfillmen.shop.manager.service.sync.chain.enums.SyncType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * 数据完整性处理器
 *
 * <pre>
 * 处理器职责：
 * 1. 验证同步后的数据完整性
 * 2. 修复缺失或异常的数据
 * 3. 构建最终的TzProductDTO
 * 4. 数据一致性检查
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/07/05
 * @since 2.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DataIntegrityHandler extends AbstractProductSyncHandler {

    @Override
    public String getHandlerName() {
        return "DataIntegrityHandler";
    }

    @Override
    public int getPriority() {
        return 60; // 在SKU处理之后执行
    }

    @Override
    protected ProductSyncResult doHandle(ProductSyncContext context) {
        log.debug("开始数据完整性检查: platformProductId={}", context.getPlatformProductId());

        try {
            // 1. 收集所有数据
            DataCollectionResult dataResult = collectAllData(context);
            if (!dataResult.isValid()) {
                String errorMsg = "数据收集失败: " + dataResult.getErrorMessage();
                context.addError(errorMsg);
                return ProductSyncResult.failure("DATA_COLLECTION_FAILED", errorMsg);
            }

            // 2. 验证数据完整性
            IntegrityCheckResult integrityResult = validateDataIntegrity(context, dataResult);
            if (!integrityResult.isValid()) {
                // 尝试修复数据
                boolean repaired = repairDataIntegrity(context, dataResult, integrityResult);
                if (!repaired) {
                    String errorMsg = "数据完整性验证失败且无法修复: " + integrityResult.getErrorMessage();
                    context.addError(errorMsg);
                    return ProductSyncResult.failure("DATA_INTEGRITY_FAILED", errorMsg);
                }
            }

            // 3. 构建最终的TzProductDTO
            TzProductDTO finalResult = buildFinalResult(context, dataResult);
            if (finalResult == null) {
                String errorMsg = "构建最终结果失败";
                context.addError(errorMsg);
                return ProductSyncResult.failure("RESULT_BUILD_FAILED", errorMsg);
            }

            // 4. 设置结果到上下文
            context.setResult(finalResult);

            // 5. 记录完整性检查成功信息
            recordIntegrityCheckSuccess(context, finalResult);

            log.debug("数据完整性检查完成: platformProductId={}, spuId={}, skuCount={}",
                context.getPlatformProductId(), finalResult.getId(),
                finalResult.getSkuList() != null ? finalResult.getSkuList().size() : 0);

            return ProductSyncResult.success(finalResult);

        } catch (Exception e) {
            String errorMsg = "数据完整性检查异常: " + e.getMessage();
            log.error("数据完整性检查失败: platformProductId={}", context.getPlatformProductId(), e);
            context.addError(errorMsg);
            return ProductSyncResult.failure("INTEGRITY_CHECK_EXCEPTION", errorMsg);
        }
    }

    /**
     * 收集所有数据
     */
    private DataCollectionResult collectAllData(ProductSyncContext context) {
        DataCollectionResult result = new DataCollectionResult();

        // 收集SPU数据
        TzProductSpu spu = context.getNewSpu();
        if (spu == null) {
            spu = context.getExistingSpu();
        }

        if (spu == null) {
            return result.setError("无法获取SPU数据");
        }
        result.setSpu(spu);

        // 收集SKU数据
        List<TzProductSku> skus = context.getNewSkus();
        if (CollectionUtil.isEmpty(skus)) {
            skus = context.getExistingSkus();
        }

        if (CollectionUtil.isEmpty(skus)) {
            return result.setError("无法获取SKU数据");
        }
        result.setSkus(skus);

        // 收集产品详情数据
        AlibabaProductDetailDTO productDetail = context.getProductDetail();
        if (productDetail == null) {
            return result.setError("无法获取产品详情数据");
        }
        result.setProductDetail(productDetail);

        return result.setValid(true);
    }

    /**
     * 验证数据完整性
     */
    private IntegrityCheckResult validateDataIntegrity(ProductSyncContext context, DataCollectionResult dataResult) {
        IntegrityCheckResult result = new IntegrityCheckResult();

        // 验证SPU数据完整性
        if (!validateSpuIntegrity(dataResult.getSpu())) {
            result.addError("SPU数据不完整");
        }

        // 验证SKU数据完整性
        if (!validateSkuIntegrity(dataResult.getSkus())) {
            result.addError("SKU数据不完整");
        }

        // 验证SPU和SKU的关联性
        if (!validateSpuSkuRelation(dataResult.getSpu(), dataResult.getSkus())) {
            result.addError("SPU和SKU关联性异常");
        }

        // 验证价格数据
        if (!validatePriceData(dataResult.getSkus())) {
            result.addError("价格数据异常");
        }

        // 验证库存数据
        if (!validateInventoryData(dataResult.getSkus())) {
            result.addError("库存数据异常");
        }

        return result;
    }

    /**
     * 验证SPU完整性
     */
    private boolean validateSpuIntegrity(TzProductSpu spu) {
        if (spu.getId() == null) {
            return false;
        }

        if (spu.getTitle() == null || spu.getTitle().trim().isEmpty()) {
            return false;
        }

        if (spu.getPdcPlatformProductId() == null) {
            return false;
        }

        return true;
    }

    /**
     * 验证SKU完整性
     */
    private boolean validateSkuIntegrity(List<TzProductSku> skus) {
        if (CollectionUtil.isEmpty(skus)) {
            return false;
        }

        for (TzProductSku sku : skus) {
            if (sku.getId() == null || sku.getSpuId() == null) {
                return false;
            }

            if (sku.getPlatformProductId() == null) {
                return false;
            }
        }

        return true;
    }

    /**
     * 验证SPU和SKU关联性
     */
    private boolean validateSpuSkuRelation(TzProductSpu spu, List<TzProductSku> skus) {
        Long spuId = spu.getId();

        for (TzProductSku sku : skus) {
            if (!spuId.equals(sku.getSpuId())) {
                return false;
            }
        }

        return true;
    }

    /**
     * 验证价格数据
     */
    private boolean validatePriceData(List<TzProductSku> skus) {
        for (TzProductSku sku : skus) {
            if (sku.getPrice() == null || sku.getPrice().compareTo(BigDecimal.ZERO) < 0) {
                return false;
            }

            if (sku.getDropShippingPrice() == null || sku.getDropShippingPrice().compareTo(BigDecimal.ZERO) < 0) {
                return false;
            }
        }

        return true;
    }

    /**
     * 验证库存数据
     */
    private boolean validateInventoryData(List<TzProductSku> skus) {
        for (TzProductSku sku : skus) {
            if (sku.getQuantity() == null || sku.getQuantity() < 0) {
                return false;
            }
        }

        return true;
    }

    /**
     * 修复数据完整性
     */
    private boolean repairDataIntegrity(ProductSyncContext context, DataCollectionResult dataResult,
        IntegrityCheckResult integrityResult) {
        log.warn("尝试修复数据完整性问题: platformProductId={}, 错误={}",
            context.getPlatformProductId(), integrityResult.getErrors());

        boolean repaired = true;

        // 修复价格数据
        if (integrityResult.getErrors().contains("价格数据异常")) {
            repaired &= repairPriceData(dataResult.getSkus());
        }

        // 修复库存数据
        if (integrityResult.getErrors().contains("库存数据异常")) {
            repaired &= repairInventoryData(dataResult.getSkus());
        }

        if (repaired) {
            context.addWarning("数据完整性问题已自动修复");
            addHandlerMetadata(context, "data_repaired", true);
        }

        return repaired;
    }

    /**
     * 修复价格数据
     */
    private boolean repairPriceData(List<TzProductSku> skus) {
        try {
            for (TzProductSku sku : skus) {
                if (sku.getPrice() == null || sku.getPrice().compareTo(BigDecimal.ZERO) < 0) {
                    sku.setPrice(BigDecimal.ZERO);
                }

                if (sku.getDropShippingPrice() == null || sku.getDropShippingPrice().compareTo(BigDecimal.ZERO) < 0) {
                    sku.setDropShippingPrice(BigDecimal.ZERO);
                }
            }
            return true;
        } catch (Exception e) {
            log.error("修复价格数据失败", e);
            return false;
        }
    }

    /**
     * 修复库存数据
     */
    private boolean repairInventoryData(List<TzProductSku> skus) {
        try {
            for (TzProductSku sku : skus) {
                if (sku.getQuantity() == null || sku.getQuantity() < 0) {
                    sku.setQuantity(0);
                }
            }
            return true;
        } catch (Exception e) {
            log.error("修复库存数据失败", e);
            return false;
        }
    }

    /**
     * 构建最终结果
     */
    private TzProductDTO buildFinalResult(ProductSyncContext context, DataCollectionResult dataResult) {
        try {
            // 使用Mapstruct构建TzProductDTO
            TzProductDTO result = TzProductMapping.INSTANCE.toTzProductDTO(
                dataResult.getSpu(),
                dataResult.getSkus(),
                dataResult.getProductDetail()
            );

            if (result == null) {
                log.error("Mapstruct转换返回null: platformProductId={}", context.getPlatformProductId());
                return null;
            }

            // 设置额外的元数据
            result.setPdcPlatformProductId(context.getPlatformProductId());

            return result;

        } catch (Exception e) {
            log.error("构建最终结果异常: platformProductId={}", context.getPlatformProductId(), e);
            return null;
        }
    }

    /**
     * 记录完整性检查成功信息
     */
    private void recordIntegrityCheckSuccess(ProductSyncContext context, TzProductDTO result) {
        addHandlerMetadata(context, "success", true);
        addHandlerMetadata(context, "check_time", System.currentTimeMillis());
        addHandlerMetadata(context, "final_spu_id", result.getId());
        addHandlerMetadata(context, "final_sku_count", result.getSkuList() != null ? result.getSkuList().size() : 0);
        addHandlerMetadata(context, "result_built", true);
    }

    @Override
    public boolean canHandle(ProductSyncContext context) {
        // 所有需要构建结果的同步类型都需要完整性检查
        return context.getSyncType() != SyncType.VALIDATION;
    }

    @Override
    protected boolean shouldContinueOnFailure() {
        return false; // 完整性检查失败不继续
    }

    @Override
    protected boolean supportFallback() {
        return true; // 支持降级处理
    }

    // ==================== 内部数据类 ====================

    /**
     * 数据收集结果
     */
    private static class DataCollectionResult {

        private boolean valid = false;
        private String errorMessage;
        private TzProductSpu spu;
        private List<TzProductSku> skus;
        private AlibabaProductDetailDTO productDetail;

        public boolean isValid() {
            return valid;
        }

        public DataCollectionResult setValid(boolean valid) {
            this.valid = valid;
            return this;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public DataCollectionResult setError(String errorMessage) {
            this.errorMessage = errorMessage;
            this.valid = false;
            return this;
        }

        public TzProductSpu getSpu() {
            return spu;
        }

        public void setSpu(TzProductSpu spu) {
            this.spu = spu;
        }

        public List<TzProductSku> getSkus() {
            return skus;
        }

        public void setSkus(List<TzProductSku> skus) {
            this.skus = skus;
        }

        public AlibabaProductDetailDTO getProductDetail() {
            return productDetail;
        }

        public void setProductDetail(AlibabaProductDetailDTO productDetail) {
            this.productDetail = productDetail;
        }
    }

    /**
     * 完整性检查结果
     */
    private static class IntegrityCheckResult {

        private final java.util.List<String> errors = new java.util.ArrayList<>();

        public boolean isValid() {
            return errors.isEmpty();
        }

        public void addError(String error) {
            errors.add(error);
        }

        public java.util.List<String> getErrors() {
            return errors;
        }

        public String getErrorMessage() {
            return String.join("; ", errors);
        }
    }
}
