/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.service.sync.chain.core;

/**
 * 产品同步处理器接口
 * 
 * <pre>
 * 责任链模式核心接口：
 * 1. 定义统一的处理方法
 * 2. 支持链式调用
 * 3. 提供处理器识别能力
 * 4. 支持条件处理
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/07/05
 * @since 2.0.0
 */
public interface ProductSyncHandler {

    /**
     * 处理产品同步请求
     *
     * @param context 同步上下文
     * @return 处理结果
     */
    ProductSyncResult handle(ProductSyncContext context);

    /**
     * 设置下一个处理器
     *
     * @param next 下一个处理器
     * @return 当前处理器（支持链式调用）
     */
    ProductSyncHandler setNext(ProductSyncHandler next);

    /**
     * 获取下一个处理器
     *
     * @return 下一个处理器
     */
    ProductSyncHandler getNext();

    /**
     * 获取处理器名称
     *
     * @return 处理器名称
     */
    String getHandlerName();

    /**
     * 判断是否可以处理当前请求
     *
     * @param context 同步上下文
     * @return true-可以处理，false-跳过
     */
    boolean canHandle(ProductSyncContext context);

    /**
     * 获取处理器优先级
     * 数值越小优先级越高
     *
     * @return 优先级
     */
    default int getPriority() {
        return 100;
    }
}
