/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.service.sync.chain.core;

import com.fulfillmen.shop.domain.dto.TzProductDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 产品同步结果对象
 * 
 * <pre>
 * 结果对象职责：
 * 1. 封装处理结果状态
 * 2. 携带成功或失败的数据
 * 3. 提供详细的处理信息
 * 4. 支持性能监控数据
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/07/05
 * @since 2.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductSyncResult {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 产品DTO结果
     */
    private TzProductDTO productDTO;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 警告信息列表
     */
    @Builder.Default
    private List<String> warnings = new ArrayList<>();

    /**
     * 处理开始时间
     */
    private LocalDateTime startTime;

    /**
     * 处理结束时间
     */
    private LocalDateTime endTime;

    /**
     * 处理耗时（毫秒）
     */
    private long processingTimeMs;

    /**
     * 处理的平台产品ID
     */
    private String platformProductId;

    /**
     * 是否使用了缓存
     */
    private boolean usedCache;

    /**
     * 是否使用了降级处理
     */
    private boolean usedFallback;

    /**
     * 处理器执行记录
     */
    @Builder.Default
    private List<String> handlerExecutionLog = new ArrayList<>();

    /**
     * 元数据信息
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();

    // ==================== 便捷构造方法 ====================

    /**
     * 创建成功结果
     */
    public static ProductSyncResult success(TzProductDTO productDTO) {
        return ProductSyncResult.builder()
            .success(true)
            .productDTO(productDTO)
            .endTime(LocalDateTime.now())
            .build();
    }

    /**
     * 创建成功结果（带警告）
     */
    public static ProductSyncResult successWithWarnings(TzProductDTO productDTO, List<String> warnings) {
        return ProductSyncResult.builder()
            .success(true)
            .productDTO(productDTO)
            .warnings(warnings)
            .endTime(LocalDateTime.now())
            .build();
    }

    /**
     * 创建失败结果
     */
    public static ProductSyncResult failure(String errorMessage) {
        return ProductSyncResult.builder()
            .success(false)
            .errorMessage(errorMessage)
            .endTime(LocalDateTime.now())
            .build();
    }

    /**
     * 创建失败结果（带错误代码）
     */
    public static ProductSyncResult failure(String errorCode, String errorMessage) {
        return ProductSyncResult.builder()
            .success(false)
            .errorCode(errorCode)
            .errorMessage(errorMessage)
            .endTime(LocalDateTime.now())
            .build();
    }

    /**
     * 从上下文创建结果
     */
    public static ProductSyncResult fromContext(ProductSyncContext context) {
        ProductSyncResultBuilder builder = ProductSyncResult.builder()
            .platformProductId(context.getPlatformProductId())
            .startTime(context.getRequestStartTime())
            .endTime(LocalDateTime.now())
            .warnings(context.getWarnings())
            .handlerExecutionLog(context.getHandlerExecutionLog())
            .usedFallback(context.isUseFallback());

        // 计算处理时间
        if (context.getRequestStartTime() != null) {
            long processingTime = java.time.Duration.between(
                context.getRequestStartTime(),
                LocalDateTime.now()
            ).toMillis();
            builder.processingTimeMs(processingTime);
        }

        if (context.isHasErrors()) {
            return builder
                .success(false)
                .errorMessage(String.join("; ", context.getErrors()))
                .build();
        } else {
            return builder
                .success(true)
                .productDTO(context.getResult())
                .build();
        }
    }

    // ==================== 便捷方法 ====================

    /**
     * 添加元数据
     */
    public ProductSyncResult addMetadata(String key, Object value) {
        this.metadata.put(key, value);
        return this;
    }

    /**
     * 添加警告
     */
    public ProductSyncResult addWarning(String warning) {
        this.warnings.add(warning);
        return this;
    }

    /**
     * 设置处理时间
     */
    public ProductSyncResult withProcessingTime(long startTimeMs) {
        this.processingTimeMs = System.currentTimeMillis() - startTimeMs;
        return this;
    }

    /**
     * 标记使用了缓存
     */
    public ProductSyncResult markCacheUsed() {
        this.usedCache = true;
        return this;
    }

    /**
     * 标记使用了降级处理
     */
    public ProductSyncResult markFallbackUsed() {
        this.usedFallback = true;
        return this;
    }
}
