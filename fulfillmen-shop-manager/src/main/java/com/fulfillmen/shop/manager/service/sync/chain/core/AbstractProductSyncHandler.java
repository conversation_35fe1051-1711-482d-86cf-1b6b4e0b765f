/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.service.sync.chain.core;

import lombok.extern.slf4j.Slf4j;

/**
 * 产品同步处理器抽象基类
 * 
 * <pre>
 * 抽象基类职责：
 * 1. 实现责任链的基础逻辑
 * 2. 提供统一的异常处理框架
 * 3. 实现通用的日志记录
 * 4. 提供模板方法支持
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/07/05
 * @since 2.0.0
 */
@Slf4j
public abstract class AbstractProductSyncHandler implements ProductSyncHandler {

    /**
     * 下一个处理器
     */
    protected ProductSyncHandler next;

    @Override
    public ProductSyncHandler setNext(ProductSyncHandler next) {
        this.next = next;
        return next;
    }

    @Override
    public ProductSyncHandler getNext() {
        return this.next;
    }

    @Override
    public final ProductSyncResult handle(ProductSyncContext context) {
        long startTime = System.currentTimeMillis();
        String handlerName = getHandlerName();

        try {
            log.debug("开始执行处理器: {}, platformProductId: {}", handlerName, context.getPlatformProductId());

            // 记录处理器执行
            context.logHandlerExecution(handlerName);

            // 检查是否可以处理
            if (!canHandle(context)) {
                log.debug("处理器跳过: {}, 原因: 不满足处理条件", handlerName);
                return passToNext(context);
            }

            // 检查是否应该继续处理
            if (!context.isShouldContinue()) {
                log.debug("处理器跳过: {}, 原因: 上下文标记停止处理", handlerName);
                return ProductSyncResult.fromContext(context);
            }

            // 执行具体的处理逻辑
            ProductSyncResult result = doHandle(context);

            // 记录处理时间
            long processingTime = System.currentTimeMillis() - startTime;
            log.debug("处理器执行完成: {}, 耗时: {}ms, 成功: {}",
                handlerName, processingTime, result.isSuccess());

            // 如果处理失败且不允许继续，直接返回
            if (!result.isSuccess() && !shouldContinueOnFailure()) {
                log.warn("处理器执行失败，停止处理链: {}, 错误: {}", handlerName, result.getErrorMessage());
                context.stopProcessing();
                return result;
            }

            // 如果当前处理器成功，但上下文标记停止，返回当前结果
            if (!context.isShouldContinue()) {
                return ProductSyncResult.fromContext(context);
            }

            // 传递给下一个处理器
            return passToNext(context);

        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            log.error("处理器执行异常: {}, 耗时: {}ms, platformProductId: {}",
                handlerName, processingTime, context.getPlatformProductId(), e);

            // 处理异常
            return handleException(context, e);
        }
    }

    /**
     * 具体的处理逻辑（由子类实现）
     *
     * @param context 同步上下文
     * @return 处理结果
     */
    protected abstract ProductSyncResult doHandle(ProductSyncContext context);

    /**
     * 传递给下一个处理器
     */
    private ProductSyncResult passToNext(ProductSyncContext context) {
        if (next != null) {
            return next.handle(context);
        } else {
            // 链条结束，从上下文构建最终结果
            return ProductSyncResult.fromContext(context);
        }
    }

    /**
     * 处理异常
     */
    protected ProductSyncResult handleException(ProductSyncContext context, Exception e) {
        String errorMessage = String.format("处理器 %s 执行异常: %s", getHandlerName(), e.getMessage());
        context.addError(errorMessage);

        // 如果支持降级处理，标记使用降级
        if (supportFallback()) {
            context.enableFallback();
            log.warn("处理器异常，启用降级处理: {}", getHandlerName());
            return passToNext(context);
        } else {
            // 不支持降级，直接返回失败结果
            context.stopProcessing();
            return ProductSyncResult.failure(errorMessage);
        }
    }

    /**
     * 是否在失败时继续执行链条
     * 默认为false，子类可以重写
     */
    protected boolean shouldContinueOnFailure() {
        return false;
    }

    /**
     * 是否支持降级处理
     * 默认为true，子类可以重写
     */
    protected boolean supportFallback() {
        return true;
    }

    @Override
    public boolean canHandle(ProductSyncContext context) {
        // 默认都可以处理，子类可以重写添加特定条件
        return true;
    }

    @Override
    public int getPriority() {
        return 100; // 默认优先级
    }

    /**
     * 验证上下文必要参数
     */
    protected boolean validateContext(ProductSyncContext context) {
        if (context == null) {
            log.error("处理器 {} 接收到空的上下文", getHandlerName());
            return false;
        }

        if (context.getPlatformProductId() == null || context.getPlatformProductId().trim().isEmpty()) {
            log.error("处理器 {} 接收到空的platformProductId", getHandlerName());
            return false;
        }

        return true;
    }

    /**
     * 添加处理器特定的元数据
     */
    protected void addHandlerMetadata(ProductSyncContext context, String key, Object value) {
        String metadataKey = getHandlerName() + "." + key;
        context.setAttribute(metadataKey, value);
    }

    /**
     * 获取处理器特定的元数据
     */
    protected <T> T getHandlerMetadata(ProductSyncContext context, String key) {
        String metadataKey = getHandlerName() + "." + key;
        return context.getAttribute(metadataKey);
    }
}
