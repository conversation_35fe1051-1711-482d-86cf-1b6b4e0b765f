/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.strategy.impl;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import com.fulfillmen.shop.common.util.MetaInfoHashUtils;
import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.domain.entity.PdcProductMapping;
import com.fulfillmen.shop.manager.core.repository.PdcProductMappingRepository;
import com.fulfillmen.shop.manager.strategy.ISyncStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 自动同步策略
 * <p>
 * 🔥 优化后的智能同步策略：
 * 1. 优先通过 metaInfoHash 检测数据变更（MD5签名对比）
 * 2. 兜底使用时间间隔检测（默认3天）
 * 3. 避免不必要的API调用，提升性能
 * </p>
 *
 * <AUTHOR>
 * @date 2025/06/19
 * @since 2.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AutoSyncStrategy implements ISyncStrategy {

    private final PdcProductMappingRepository pdcProductMappingRepository;

    /**
     * 自动同步时间间隔（天）- 兜底机制
     */
    private static final long AUTO_SYNC_INTERVAL_DAYS = 3;

    @Override
    public boolean shouldSync(String platformProductId, PdcProductMapping existingMapping) {
        // 1. 如果数据不存在，需要同步
        if (existingMapping == null) {
            log.debug("产品数据不存在，需要同步: {}", platformProductId);
            return true;
        }

        // 2. 🔥 智能检测：通过 metaInfoHash 检测数据变更
        if (hasDataChanged(platformProductId, existingMapping)) {
            log.debug("产品数据已变更（基于metaInfoHash检测），需要同步: {}", platformProductId);
            return true;
        }

        // 3. 兜底机制：检查最后修改时间（防止metaInfoHash检测失败的情况）
        if (isDataExpiredByTime(existingMapping, platformProductId)) {
            log.debug("产品数据已过期（基于时间检测），需要同步: {}", platformProductId);
            return true;
        }

        log.debug("产品数据无需同步: {}", platformProductId);
        return false;
    }

    @Override
    public TzProductDTO executeSync(String platformProductId, boolean forceRefresh) {
        log.debug("执行自动同步策略: platformProductId={}, forceRefresh={}", platformProductId, forceRefresh);

        try {
            // 使用Repository的缓存机制获取数据
            var productDetail = pdcProductMappingRepository.getProductDetailWithCache(Long
                .valueOf(platformProductId), forceRefresh);

            if (productDetail == null) {
                log.warn("自动同步失败，未找到产品详情: {}", platformProductId);
                return null;
            }

            // TODO: 这里应该调用TzProduct的同步逻辑
            // 暂时返回基础数据，后续完善
            log.info("自动同步完成: {}", platformProductId);

            return buildBasicProductDTO(productDetail, platformProductId);

        } catch (Exception e) {
            log.error("自动同步异常: platformProductId={}", platformProductId, e);
            return null;
        }
    }

    @Override
    public List<TzProductDTO> batchSync(List<String> platformProductIds) {
        log.info("执行批量自动同步，数量: {}", platformProductIds.size());

        return platformProductIds.stream()
            .map(id -> executeSync(id, false))
            .filter(dto -> dto != null)
            .collect(Collectors.toList());
    }

    @Override
    public String getStrategyName() {
        return "AutoSyncStrategy";
    }

    /**
     * 🔥 智能数据变更检测 - 基于metaInfoHash的MD5签名对比
     *
     * <p>
     * 检测逻辑：
     * 1. 获取远程API最新的商品元数据
     * 2. 计算最新数据的MD5签名
     * 3. 与现有PdcProductMapping.metaInfoHash对比
     * 4. 签名不一致则说明数据已变更
     * </p>
     *
     * @param platformProductId 平台产品ID
     * @param existingMapping   现有的产品映射数据
     * @return true-数据已变更，false-数据未变更
     */
    private boolean hasDataChanged(String platformProductId, PdcProductMapping existingMapping) {
        try {
            // 如果现有数据没有metaInfoHash，说明是老数据，需要同步
            String currentHash = existingMapping.getMetaInfoHash();
            if (currentHash == null || currentHash.trim().isEmpty()) {
                log.debug("现有数据缺少metaInfoHash，判定为需要同步: {}", platformProductId);
                return true;
            }

            // 🚀 获取最新的商品详情数据（这里会触发API调用）
            AlibabaProductDetailDTO latestProductDetail = pdcProductMappingRepository.getProductDetailWithCache(Long
                .valueOf(platformProductId), true); // 强制刷新获取最新数据

            if (latestProductDetail == null) {
                log.warn("无法获取最新商品详情，跳过hash比较: {}", platformProductId);
                return false; // 无法获取最新数据，不强制同步
            }

            // 🔥 计算最新数据的MD5签名
            // 使用统一的MetaInfoHashUtils工具类确保计算逻辑一致
            String latestHash = MetaInfoHashUtils.calculateMetaInfoHash(latestProductDetail);

            // 📊 对比MD5签名
            boolean hasChanged = MetaInfoHashUtils.needsUpdate(currentHash, latestHash);

            if (hasChanged) {
                log.info("检测到数据变更 [platformProductId={}]: 当前hash={}, 最新hash={}", platformProductId, currentHash, latestHash);
            } else {
                log.debug("数据未变更 [platformProductId={}]: hash={}", platformProductId, currentHash);
            }

            return hasChanged;

        } catch (Exception e) {
            log.warn("metaInfoHash检测失败，降级到时间检测: platformProductId={}", platformProductId, e);
            // 检测失败时，降级到时间检测
            return false;
        }
    }

    /**
     * 兜底机制：基于时间的过期检测
     *
     * @param existingMapping   现有映射数据
     * @param platformProductId 平台产品ID（用于日志）
     * @return true-数据已过期，false-数据仍有效
     */
    private boolean isDataExpiredByTime(PdcProductMapping existingMapping, String platformProductId) {
        LocalDateTime lastModified = existingMapping.getGmtModified();
        if (lastModified == null) {
            log.debug("产品最后修改时间为空，判定为需要同步: {}", platformProductId);
            return true;
        }

        Duration timeSinceUpdate = Duration.between(lastModified, LocalDateTime.now());
        boolean isExpired = timeSinceUpdate.toDays() >= AUTO_SYNC_INTERVAL_DAYS;

        if (isExpired) {
            log.debug("产品数据已过期{}天（时间检测）: {}", timeSinceUpdate.toDays(), platformProductId);
        }

        return isExpired;
    }

    /**
     * 构建基础产品DTO（临时方法，后续会被真正的转换逻辑替代）
     */
    private TzProductDTO buildBasicProductDTO(AlibabaProductDetailDTO productDetail, String platformProductId) {
        return TzProductDTO.builder()
            .pdcPlatformProductId(platformProductId)
            .title(productDetail.getTitle())
            .titleTrans(productDetail.getTitleTrans())
            .mainImage(productDetail.getImages() != null && !productDetail.getImages().isEmpty()
                ? productDetail.getImages().get(0)
                : null)
            .categoryId(productDetail.getCategoryId())
            .build();
    }
}
