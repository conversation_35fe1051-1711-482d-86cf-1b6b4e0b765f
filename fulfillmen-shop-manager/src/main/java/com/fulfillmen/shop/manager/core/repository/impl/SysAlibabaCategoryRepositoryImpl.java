/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.repository.impl;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.alicp.jetcache.template.QuickConfig;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.domain.entity.enums.EnabledStatusEnum;
import com.fulfillmen.shop.manager.core.repository.SysAlibabaCategoryRepository;
import com.fulfillmen.shop.common.util.CacheConstants;
import com.fulfillmen.shop.dao.mapper.SysAlibabaCategoryMapper;
import com.fulfillmen.shop.domain.convert.AlibabaCategoryDTOMapping;
import com.fulfillmen.shop.domain.dto.AlibabaCategoryTreeDTO;
import com.fulfillmen.shop.domain.entity.SysAlibabaCategory;
import com.fulfillmen.starter.core.exception.BusinessException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

/**
 * 阿里巴巴商品类目服务实现
 *
 * <AUTHOR>
 * @date 2025/2/15 13:33
 * @since 1.0.0
 */
@Slf4j
@Repository
public class SysAlibabaCategoryRepositoryImpl implements SysAlibabaCategoryRepository, InitializingBean {

    /**
     * 热门类目ID
     */
    private static final String HOT_CATEGORY_KEY = "hotCategoryIds";
    private final SysAlibabaCategoryMapper sysAlibabaCategoryMapper;
    private final CacheManager cacheManager;
    private Cache<Long, AlibabaCategoryTreeDTO> alibabaCategoriesCache;

    public SysAlibabaCategoryRepositoryImpl(SysAlibabaCategoryMapper sysAlibabaCategoryMapper,
        CacheManager cacheManager) {
        this.sysAlibabaCategoryMapper = sysAlibabaCategoryMapper;
        this.cacheManager = cacheManager;
    }

    @Override
    public List<AlibabaCategoryTreeDTO> getAlibabaCategoryAll() {
        try {
            // 获取全部类目信息 status =1 并且升序
            LambdaQueryWrapper<SysAlibabaCategory> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysAlibabaCategory::getStatus, EnabledStatusEnum.ENABLED.getCode())
                .orderByAsc(SysAlibabaCategory::getSort);
            List<SysAlibabaCategory> allCategories = sysAlibabaCategoryMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(allCategories)) {
                return new ArrayList<>();
            }
            return AlibabaCategoryDTOMapping.INSTANCE.toDTOList(allCategories);
        } catch (Exception e) {
            log.error("Failed to get alibaba categories", e);
            throw new BusinessException("Failed to get alibaba categories");
        }
    }

    @Override
    @Cached(name = CacheConstants.ALIBABA_CATEGORY + "list:", expire = 7300, localLimit = 500, cacheType = CacheType.BOTH, key = "'tree'")
    @CacheRefresh(refresh = 7200, refreshLockTimeout = 20)
    public List<AlibabaCategoryTreeDTO> getAlibabaCategoryAllWithCache() {
        // 获取全部类目信息 status =1 并且升序
        LambdaQueryWrapper<SysAlibabaCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysAlibabaCategory::getStatus, EnabledStatusEnum.ENABLED.getCode())
            .orderByAsc(SysAlibabaCategory::getSort);
        List<SysAlibabaCategory> allCategories = sysAlibabaCategoryMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(allCategories)) {
            return new ArrayList<>();
        }
        // 构建类目树 并缓存
        return AlibabaCategoryDTOMapping.INSTANCE.buildTree(allCategories);
    }

    @Override
    public List<AlibabaCategoryTreeDTO> getTop20AlibabaCategoryByLevelOrderBySort(int level) {
        //        Map<Long, AlibabaCategoryTreeDTO> hotCategoryList = alibabaCategoriesCache.getAll();
        //        if (Objects.isNull(hotCategoryList) || hotCategoryList.isEmpty()) {
        //            // 查询 Db
        //            LambdaQueryWrapper<SysAlibabaCategory> queryWrapper = new LambdaQueryWrapper<>();
        //            queryWrapper.eq(SysAlibabaCategory::getStatus, EnabledStatusEnum.ENABLED.getCode())
        //              .orderByAsc(SysAlibabaCategory::getSort)
        //              .last("limit 20");
        //            List<SysAlibabaCategory> hotCategories = sysAlibabaCategoryMapper.selectList(queryWrapper);
        //            // 使用 CategoryId 作为 key ， AlibabaCategoryTreeDTO 作为 value ，缓存到 alibabaCategoriesCache
        //            Map<Long, AlibabaCategoryTreeDTO> hotCategoryMap = hotCategories.stream()
        //              .map(AlibabaCategoryDTOMapping.INSTANCE::toDTO)
        //              .collect(Collectors.toMap(AlibabaCategoryTreeDTO::getCategoryId, Function.identity()));
        //            alibabaCategoriesCache.putAll(hotCategoryMap);
        //            return AlibabaCategoryDTOMapping.INSTANCE.toDTOList(hotCategories);
        //        } else {
        //            return new ArrayList<>(hotCategoryList.values());
        //        }
        return null;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QuickConfig quickConfig = QuickConfig.newBuilder("categories.")
            .localExpire(Duration.ofHours(5))
            .expire(Duration.ofHours(6))
            // two level cache
            .cacheType(CacheType.BOTH)
            .syncLocal(true)
            .build();
        alibabaCategoriesCache = cacheManager.getOrCreateCache(quickConfig);
    }
}
