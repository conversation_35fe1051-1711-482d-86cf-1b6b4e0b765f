/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.repository.impl;

import java.util.List;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.fulfillmen.shop.common.context.OrderContext;
import com.fulfillmen.shop.common.enums.FulfillmenValidationCodeEnum;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.dao.mapper.TzOrderItemMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.dao.mapper.TzShoppingCartMapper;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.manager.core.repository.TzOrderPurchaseRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 订单仓储实现
 *
 * <AUTHOR>
 * @date 2025/6/30 14:50
 * @description 订单仓储层实现，负责订单的持久化操作
 * @since 1.0.0
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class TzOrderPurchaseRepositoryImpl extends CrudRepository<TzOrderPurchaseMapper, TzOrderPurchase> implements TzOrderPurchaseRepository {

    private final TzOrderSupplierMapper orderSupplierMapper;
    private final TzOrderItemMapper orderItemMapper;
    private final TzShoppingCartMapper shoppingCartMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createPurchaseOrder(OrderContext orderContext) {
        TzOrderPurchase purchaseOrder = orderContext.getPurchaseOrder();
        List<TzOrderSupplier> supplierOrders = orderContext.getSupplierOrders();
        List<TzOrderItem> orderItems = orderContext.getOrderItems();

        log.info("开始创建订单，买家ID: {}, 采购的供应商数量: {} , sku 数量: {}", purchaseOrder.getBuyerId(), supplierOrders.size(), orderItems.size());

        try {
            // 1. 创建采购订单（主订单）
            this.save(purchaseOrder);

            // 2. 按供应商创建子订单并创建订单项
            orderSupplierMapper.insertBatch(supplierOrders);

            // 3. 创建订单项
            orderItemMapper.insertBatch(orderItems);

            log.info("订单创建成功，采购订单号: {}, 供应商数量: {}, 商品项数: {}", purchaseOrder.getPurchaseOrderNo(), supplierOrders.size(), orderItems.size());

            // 4. 删除购物车
            if (orderContext.getShoppingCartIds() != null && !orderContext.getShoppingCartIds().isEmpty()) {
                shoppingCartMapper.deleteByIds(orderContext.getShoppingCartIds());
            }
        } catch (BusinessExceptionI18n e) {
            log.error("订单创建业务异常，买家ID: {}, 错误: {}", purchaseOrder.getBuyerId(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("订单创建系统异常，买家ID: {}", purchaseOrder.getBuyerId(), e);
            throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.VALIDATION_FAILED, e.getMessage());
        }
    }

}
