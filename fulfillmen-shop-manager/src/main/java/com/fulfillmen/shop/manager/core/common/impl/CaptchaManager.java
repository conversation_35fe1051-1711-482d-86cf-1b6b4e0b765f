/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.common.impl;

import static com.fulfillmen.shop.common.properties.CommonConstants.ACTIVATION_KEY_PREFIX;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import com.feiniaojin.gracefulresponse.GracefulResponseException;
import com.fulfillmen.shop.common.properties.CaptchaProperties;
import com.fulfillmen.shop.common.properties.CaptchaProperties.CaptchaMail;
import com.fulfillmen.shop.common.util.CacheConstants;
import com.fulfillmen.shop.domain.dto.CaptchaDTO;
import com.fulfillmen.shop.manager.core.common.ICaptchaManager;
import com.fulfillmen.starter.cache.redisson.util.RedisUtils;
import com.fulfillmen.starter.captcha.graphic.core.GraphicCaptchaService;
import com.fulfillmen.starter.core.autoconfigure.project.ProjectProperties;
import com.fulfillmen.starter.core.util.TemplateUtils;
import com.fulfillmen.starter.messaging.mail.util.MailUtils;
import com.wf.captcha.base.Captcha;
import java.time.Duration;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 验证码管理器实现类
 *
 * <p>
 * 功能特点：
 * <ul>
 * <li>支持多种验证码类型：图形、邮箱、短信</li>
 * <li>唯一ID生成和管理</li>
 * <li>频率控制和防刷机制</li>
 * <li>国际化异常处理</li>
 * <li>缓存管理和过期控制</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 * @date 2025/4/24 10:05
 * @description: todo
 * @since 1.0.0
 */
@Slf4j
@Service
public class CaptchaManager implements ICaptchaManager {

    private final GraphicCaptchaService graphicCaptchaService;
    //    private final CaptchaService behaviorCaptchaService;
    private final CaptchaProperties captchaProperties;
    private final ProjectProperties projectProperties;

    public CaptchaManager(CaptchaProperties captchaProperties,
        GraphicCaptchaService graphicCaptchaService,
        ProjectProperties projectProperties) {
        this.captchaProperties = captchaProperties;
        this.graphicCaptchaService = graphicCaptchaService;
        this.projectProperties = projectProperties;
    }

    @Override
    public CaptchaDTO getCaptchaByImage() {
        String uuid = IdUtil.fastUUID();
        Captcha captcha = graphicCaptchaService.getCaptcha();
        long expireTime = LocalDateTimeUtil.toEpochMilli(LocalDateTime.now()
            .plusMinutes(captchaProperties.getExpirationInMinutes()));
        CaptchaDTO captchaDTO = CaptchaDTO.ofImage(uuid, captcha.text(), captcha.toBase64(), expireTime);
        String captchaKey = CacheConstants.CAPTCHA_KEY_PREFIX + uuid;
        RedisUtils.set(captchaKey, captcha.text(), Duration.ofMinutes(captchaProperties.getExpirationInMinutes()));
        return captchaDTO;
    }

    @Override
    public void getCaptchaByActivationMail(Long uid, String username, String email) {
        log.debug("发送邮箱验证码 : [uid: {} , username: {} , email: {}] ", uid, username, email);
        // 生成验证码
        CaptchaMail captchaMail = captchaProperties.getMail();
        String captchaCode = RandomUtil.randomNumbers(captchaMail.getLength());
        int expirationInHours = captchaMail.getActivationExpirationInHours();
        // 拼接 激活链接
        String activationUrl = "%s/api/auth/activation/%s?code=%s".formatted(captchaMail
            .getActivationBaseUrl(), uid, captchaCode);
        // 填写邮件内容
        String content = TemplateUtils.render(captchaMail.getActivationTemplatePath(), Dict.create()
            .set("siteUrl", projectProperties.getUrl())
            .set("username", username)
            .set("email", email)
            .set("activationUrl", activationUrl)
            .set("expireTime", expirationInHours)
            // TODO: 2025年04月25日10:54:01 后期 优化成 系统配置项 。 siteTitle  siteCopyright
            .set("siteTitle", projectProperties.getName())
            .set("siteCopyright", projectProperties.getCopyright()));
        try {
            MailUtils.sendHtml(email, "【%s】- Activation Email ".formatted("Fulfillmen Shop"), content);
            String captchaKey = ACTIVATION_KEY_PREFIX.formatted(uid);
            RedisUtils.set(captchaKey, captchaCode, Duration.ofHours(expirationInHours));
        } catch (Exception e) {
            log.error("发送激活邮件失败 : [{} - {} : {}] ", uid, username, email, e);
            throw new GracefulResponseException("Failed to send activation email");
        }
    }

    @Override
    public CaptchaDTO getCaptchaByClick() {
        return CaptchaDTO.ofBehavior(null, null, null, 0L);
    }

    @Override
    public boolean validateCaptcha(String captcha, String captchaId) {
        String captchaKey = CacheConstants.CAPTCHA_KEY_PREFIX + captchaId;
        String storedCaptcha = RedisUtils.get(captchaKey).map(String::valueOf).orElse(null);
        if (storedCaptcha != null && storedCaptcha.equalsIgnoreCase(captcha)) {
            log.debug("验证码校验 匹配成功 : [{} => {}] ", captchaId, captcha);
            RedisUtils.delete(captchaKey);
            return true;
        }
        return false;
    }

    @Override
    public String sendEmailCaptcha(String email) {
        // 生成验证码
        CaptchaMail captchaMail = captchaProperties.getMail();
        String captchaCode = RandomUtil.randomNumbers(captchaMail.getLength());
        String uuid = IdUtil.fastUUID();
        int expirationInMinutes = 10;
        // 填写邮件内容
        String content = TemplateUtils.render(captchaMail.getTemplatePath(), Dict.create()
            .set("siteUrl", projectProperties.getUrl())
            .set("siteTitle", projectProperties.getName())
            .set("captcha", captchaCode)
            .set("expiration", expirationInMinutes)
            .set("siteCopyright", projectProperties.getCopyright()));

        // 发送邮件
        try {
            MailUtils.sendHtml(email, "【%s】- Email Verification Code".formatted(projectProperties.getName()), content);
            // 保存验证码 - 通过 邮箱验证
            String captchaKey = CacheConstants.CAPTCHA_KEY_PREFIX + email;
            RedisUtils.set(captchaKey, captchaCode, Duration.ofMinutes(expirationInMinutes));
            log.debug("Email captcha sent to [{}], code: [{}], uuid: [{}]", email, captchaCode, uuid);
        } catch (Exception e) {
            log.error("发送邮箱验证码失败 : [{}] ", email, e);
            throw new GracefulResponseException("Failed to send captcha email");
        }
        return uuid;
    }
}
