/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.repository;

import com.fulfillmen.shop.domain.dto.AlibabaCategoryTreeDTO;
import java.util.List;

/**
 * 阿里巴巴 类目 仓储服务
 *
 * <AUTHOR>
 * @date 2025/2/15 13:30
 * @description: todo
 * @since 1.0.0
 */
public interface SysAlibabaCategoryRepository {

    /**
     * 获取 alibaba 所有类目信息
     *
     * @return List<AlibabaCategoryTreeDTO> 树形结构的类目列表
     */
    List<AlibabaCategoryTreeDTO> getAlibabaCategoryAll();

    /**
     * 获取 alibaba 所有类目信息
     * <pre>
     * 1. 默认从缓存获取，获取失败将从 DB 获取
     * 2. 获取成功后，将缓存起来
     * </pre>
     *
     * @param isForceRefresh 是否强制刷新缓存
     * @return List<AlibabaCategoryTreeDTO> 树形结构的类目列表
     */
    List<AlibabaCategoryTreeDTO> getAlibabaCategoryAllWithCache();

    /**
     * 获取三级类目的前 20 条类目信息
     * <pre>
     * 通过sort 升序获取 前 20 条类目
     * </pre>
     */
    List<AlibabaCategoryTreeDTO> getTop20AlibabaCategoryByLevelOrderBySort(int level);

}
