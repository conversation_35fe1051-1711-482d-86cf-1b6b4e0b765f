/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.impl;

import com.fulfillmen.shop.common.enums.FulfillmenErrorCodeEnum;
import com.fulfillmen.shop.common.enums.FulfillmenValidationCodeEnum;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.domain.convert.order.AlibabaCreateOrderConvert;
import com.fulfillmen.shop.domain.dto.order.CreateOrderRespDTO;
import com.fulfillmen.shop.manager.support.alibaba.IOrderManager;
import com.fulfillmen.shop.manager.support.alibaba.util.OrderErrorCodeUtil;
import com.fulfillmen.starter.core.exception.BusinessException;
import com.fulfillmen.support.alibaba.api.request.order.OrderBuyerListRequestRecord;
import com.fulfillmen.support.alibaba.api.request.order.OrderCancelRequestRecord;
import com.fulfillmen.support.alibaba.api.request.order.OrderCreateRequestRecord;
import com.fulfillmen.support.alibaba.api.request.order.OrderDetailRequestRecord;
import com.fulfillmen.support.alibaba.api.request.order.OrderPreviewRequestRecord;
import com.fulfillmen.support.alibaba.api.request.order.TradeFeedbackRequestRecord;
import com.fulfillmen.support.alibaba.api.response.order.OrderBuyerListResponse;
import com.fulfillmen.support.alibaba.api.response.order.OrderCancelResponse;
import com.fulfillmen.support.alibaba.api.response.order.OrderCreateResponse;
import com.fulfillmen.support.alibaba.api.response.order.OrderDetailResponse;
import com.fulfillmen.support.alibaba.api.response.order.OrderPreviewResponse;
import com.fulfillmen.support.alibaba.api.response.order.TradeFeedbackResponse;
import com.fulfillmen.support.alibaba.service.IOrderService;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 订单管理
 *
 * <AUTHOR>
 * @date 2025/5/14 18:40
 * @description: 适配并封装 Alibaba 订单相关的 API 调用
 * @since 1.0.0
 */
@Slf4j
@Component
public class OrderManager implements IOrderManager {

    private final IOrderService orderService;

    public OrderManager(IOrderService orderService) {
        this.orderService = orderService;
    }

    @Override
    public CreateOrderRespDTO createCrossOrder(OrderCreateRequestRecord request) {
        final String operationName = "创建跨境订单";
        if (request == null) {
            throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PARAM_MISSING);
        }
        log.info("{}请求: {}", operationName, request);
        try {
            return orderService.createCrossOrder(request).<OrderCreateResponse.OrderCreateResult>handle((resp, sink) -> {
                if (resp == null || !resp.getSuccess()) {
                    log.error("{}失败，响应: {}", operationName, resp);
                    sink.error(BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.REMOTE_SERVICE_ERROR));
                    return;
                }
                log.debug("响应的结果: result => {} ", resp.getResult());

                sink.next(resp.getResult());
            }).<CreateOrderRespDTO>handle((result, sink) -> {
                CreateOrderRespDTO createOrderRespDTO = AlibabaCreateOrderConvert.INSTANCE.convertAlibabaCreateOrderRespDTO(result);
                log.info("成功{}, response: {}", operationName, createOrderRespDTO);
                sink.next(createOrderRespDTO);
            })
                .block();
        } catch (Exception e) {
            if (e instanceof BusinessExceptionI18n) {
                throw e;
            }
            log.error("调用{}接口异常, request: {}", operationName, request, e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.API_CALL_FAILED, "调用" + operationName + "接口异常: " + e.getMessage());
        }
    }

    @Override
    public OrderCancelResponse cancelOrder(OrderCancelRequestRecord request) {
        final String operationName = "取消交易";
        if (request == null) {
            throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PARAM_MISSING);
        }
        log.info("{}请求: {}", operationName, request);
        try {
            return orderService.cancelOrder(request).<OrderCancelResponse>handle((resp, sink) -> {
                if (resp == null || !resp.getSuccess()) {
                    log.error("{}失败，API无响应。请求: {}", operationName, request);
                    sink.error(BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.REMOTE_SERVICE_ERROR));
                    return;
                }
                log.info("成功{}, response: {}", operationName, resp);
                sink.next(resp);
            }).block();
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                throw (BusinessException) e;
            }
            log.error("调用{}接口异常, request: {}", operationName, request, e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.API_CALL_FAILED, "调用" + operationName + "接口异常: " + e.getMessage());
        }
    }

    @Override
    public OrderBuyerListResponse getBuyerOrderList(OrderBuyerListRequestRecord request) {
        final String operationName = "获取买家订单列表";
        if (request == null) {
            throw new IllegalArgumentException(operationName + "请求不能为空");
        }
        log.info("{}请求: {}", operationName, request);
        try {
            return orderService.getBuyerOrderList(request).<OrderBuyerListResponse>handle((resp, sink) -> {
                if (resp == null || Objects.isNull(resp.getResult())) {
                    log.error("{}失败，API无响应。请求: {}", operationName, request);
                    sink.error(BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.REMOTE_SERVICE_ERROR));
                    return;
                }
                log.info("成功{}, response: {}", operationName, resp);
                sink.next(resp);
            }).block();
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                throw (BusinessException) e;
            }
            log.error("调用{}接口异常, request: {}", operationName, request, e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.API_CALL_FAILED, "调用" + operationName + "接口异常: " + e.getMessage());
        }
    }

    @Override
    public OrderDetailResponse getOrderDetail(OrderDetailRequestRecord request) {
        final String operationName = "获取订单详情";
        if (request == null) {
            throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PARAM_MISSING);
        }
        log.info("{}请求: {}", operationName, request);
        try {
            return orderService.getOrderDetail(request).<OrderDetailResponse>handle((resp, sink) -> {
                if (resp == null || !resp.getSuccess()) {
                    log.error("{}失败，API无响应。请求: {}", operationName, request);
                    sink.error(BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.REMOTE_SERVICE_ERROR));
                    return;
                }
            }).block();
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                throw (BusinessException) e;
            }
            log.error("调用{}接口异常, request: {}", operationName, request, e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.API_CALL_FAILED, "调用" + operationName + "接口异常: " + e.getMessage());
        }
    }

    @Override
    public TradeFeedbackResponse addFeedback(TradeFeedbackRequestRecord request) {
        final String operationName = "补充订单留言";
        if (request == null) {
            throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PARAM_MISSING);
        }
        log.info("{}请求: {}", operationName, request);
        try {
            return orderService.addFeedback(request).<TradeFeedbackResponse>handle((resp, sink) -> {
                if (resp == null || !resp.getSuccess()) {
                    log.error("{}失败，API无响应。请求: {}", operationName, request);
                    sink.error(BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.REMOTE_SERVICE_ERROR));
                    return;
                }
                log.info("成功{}, response: {}", operationName, resp);
                sink.next(resp);
            }).block();
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                throw (BusinessException) e;
            }
            log.error("调用{}接口异常, request: {}", operationName, request, e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.API_CALL_FAILED, "调用" + operationName + "接口异常: " + e.getMessage());
        }
    }

    @Override
    public OrderPreviewResponse previewOrder(OrderPreviewRequestRecord request) {
        final String operationName = "预览订单";
        if (request == null) {
            throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PARAM_MISSING);
        }
        log.info("{}请求: {}", operationName, request);
        try {
            return orderService.previewOrder(request).<OrderPreviewResponse>handle((resp, sink) -> {
                // 判断是否请求成功
                if (!resp.getSuccess()) {
                    String errorCode = resp.getErrorCode();
                    String errorMessage = resp.getErrorMessage() != null ? resp.getErrorMessage() : resp.getErrorMsg();

                    // 使用错误码工具类获取用户友好的错误消息
                    String friendlyMessage = OrderErrorCodeUtil.getFriendlyMessage(errorCode);

                    log.error("{}失败。\n 请求参数: {} \n 响应结果: code: {} - message: {} \n 用户友好消息: {}",
                        operationName, request, errorCode, errorMessage, friendlyMessage);

                    // 对于已知的业务错误，将友好消息设置到响应中
                    if (OrderErrorCodeUtil.isKnownBusinessError(errorCode)) {
                        resp.setErrorMessage(friendlyMessage);
                        resp.setErrorMsg(friendlyMessage);
                    }

                    // 记录具体的错误类型，方便后续处理
                    if (OrderErrorCodeUtil.isInventoryError(errorCode)) {
                        log.warn("库存不足错误: {}", friendlyMessage);
                    } else if (OrderErrorCodeUtil.isMinOrderQuantityError(errorCode)) {
                        log.warn("起批量限制错误: {}", friendlyMessage);
                    } else if (OrderErrorCodeUtil.isMixOrderError(errorCode)) {
                        log.warn("混批条件错误: {}", friendlyMessage);
                    } else if (OrderErrorCodeUtil.isProductUnavailableError(errorCode)) {
                        log.warn("商品不可购买错误: {}", friendlyMessage);
                    }

                    // 继续传递响应，让上层决定如何处理
                    sink.next(resp);
                    return;
                }
                log.info("成功{}, response: {}", operationName, resp);
                sink.next(resp);
            }).block();
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                throw (BusinessException) e;
            }
            log.error("调用{}接口异常, request: {}", operationName, request, e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.API_CALL_FAILED, "调用" + operationName + "接口异常: " + e.getMessage());
        }
    }
}
