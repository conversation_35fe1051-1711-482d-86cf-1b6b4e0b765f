/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.wms.model;

import lombok.Data;

import java.util.List;

/**
 * WMS 创建采购订单请求
 *
 * <AUTHOR>
 * @date 2025/6/27
 * @description: WMS 创建采购单请求模型
 * @since 1.0.0
 */
@Data
public class WmsCreateOrder {

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * WMS API 接口的 key
     */
    private String apiKey;

    /**
     * 创建时间戳 (秒)
     */
    private Long timestamp;

    /**
     * 请求签名
     */
    private String signature;

    /**
     * 订单信息列表
     */
    private List<WmsOrder> orders;

}
