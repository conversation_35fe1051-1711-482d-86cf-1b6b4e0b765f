/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.service.impl;

import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.domain.entity.TzProductSku;
import com.fulfillmen.shop.manager.service.IProductSyncService;
import com.fulfillmen.shop.manager.service.sync.chain.core.ProductSyncChainBuilder;
import com.fulfillmen.shop.manager.service.sync.chain.core.ProductSyncContext;
import com.fulfillmen.shop.manager.service.sync.chain.core.ProductSyncHandler;
import com.fulfillmen.shop.manager.service.sync.chain.core.ProductSyncResult;
import com.fulfillmen.shop.manager.service.sync.chain.enums.SyncType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * 基于责任链模式的产品同步服务实现
 *
 * <pre>
 * 新架构特性：
 * 1. 使用责任链模式重构同步逻辑
 * 2. 提高代码的可维护性和扩展性
 * 3. 支持灵活的处理器组合
 * 4. 保持与原有接口的兼容性
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/07/05
 * @since 2.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "product.sync.chain.enabled", havingValue = "true")
public class ChainBasedProductSyncServiceImpl implements IProductSyncService {

    private final ProductSyncChainBuilder chainBuilder;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TzProductDTO syncProductByPlatformId(String platformProductId) {
        log.debug("开始同步产品数据（责任链模式）: platformProductId={}", platformProductId);

        // 创建同步上下文
        ProductSyncContext context = ProductSyncContext.builder()
            .platformProductId(platformProductId)
            .syncType(SyncType.STANDARD)
            .forceUpdate(false)
            .build();

        // 构建并执行处理器链
        ProductSyncHandler chain = chainBuilder.buildChain(SyncType.STANDARD);
        ProductSyncResult result = executeChain(chain, context);

        // 处理结果
        return handleSyncResult(result, platformProductId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TzProductDTO resyncProductByPlatformId(String platformProductId, boolean forceUpdate) {
        log.debug("重新同步产品数据（责任链模式）: platformProductId={}, forceUpdate={}",
            platformProductId, forceUpdate);

        // 创建同步上下文
        ProductSyncContext context = ProductSyncContext.builder()
            .platformProductId(platformProductId)
            .syncType(SyncType.FORCE_RESYNC)
            .forceUpdate(forceUpdate)
            .build();

        // 构建并执行处理器链
        ProductSyncHandler chain = chainBuilder.buildChain(SyncType.FORCE_RESYNC);
        ProductSyncResult result = executeChain(chain, context);

        // 处理结果
        return handleSyncResult(result, platformProductId);
    }

    @Override
    public TzProductDTO getOrSyncProductByPlatformId(String platformProductId) {
        log.debug("获取或同步产品数据（责任链模式）: platformProductId={}", platformProductId);

        // 创建同步上下文
        ProductSyncContext context = ProductSyncContext.builder()
            .platformProductId(platformProductId)
            .syncType(SyncType.STANDARD)
            .forceUpdate(false)
            .build();

        // 构建并执行处理器链
        ProductSyncHandler chain = chainBuilder.buildChain(SyncType.STANDARD);
        ProductSyncResult result = executeChain(chain, context);

        // 处理结果
        return handleSyncResult(result, platformProductId);
    }

    @Override
    public TzProductDTO getOrSyncProductWithRetryAndTimeout(String platformProductId) {
        log.debug("获取产品数据（超时控制，责任链模式）: platformProductId={}", platformProductId);

        long startTime = System.currentTimeMillis();
        long timeoutMs = 10000L; // 10秒超时

        try {
            TzProductDTO result = getOrSyncProductByPlatformId(platformProductId);

            long duration = System.currentTimeMillis() - startTime;
            if (duration > timeoutMs) {
                log.warn("产品数据获取超时: {}, 耗时: {}ms", platformProductId, duration);
                // 超时降级：使用降级处理器链
                return handleTimeout(platformProductId);
            }

            return result;

        } catch (Exception e) {
            log.error("获取产品数据异常: {}", platformProductId, e);
            return handleTimeout(platformProductId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean validateAndRepairProductSkus(String platformProductId) {
        log.debug("验证并修复产品SKU数据（责任链模式）: platformProductId={}", platformProductId);

        // 创建修复同步上下文
        ProductSyncContext context = ProductSyncContext.builder()
            .platformProductId(platformProductId)
            .syncType(SyncType.REPAIR)
            .forceUpdate(false)
            .build();

        // 构建并执行修复处理器链
        ProductSyncHandler chain = chainBuilder.buildChain(SyncType.REPAIR);
        ProductSyncResult result = executeChain(chain, context);

        // 返回修复是否成功
        return result.isSuccess();
    }

    @Override
    public List<TzProductSku> getSkuListBySpuId(Long spuId) {
        // 这个方法不需要责任链，保持原有实现
        log.debug("获取SKU列表: spuId={}", spuId);
        // 注入原有的Mapper或委托给原有服务
        return java.util.Collections.emptyList(); // 临时实现
    }

    @Override
    public boolean isSingleItem(String platformProductId) {
        // 使用验证链检查产品类型
        ProductSyncContext context = ProductSyncContext.builder()
            .platformProductId(platformProductId)
            .syncType(SyncType.VALIDATION)
            .build();

        ProductSyncHandler chain = chainBuilder.buildChain(SyncType.VALIDATION);
        ProductSyncResult result = executeChain(chain, context);

        if (result.isSuccess() && context.getProductDetail() != null) {
            return context.getProductDetail().isSingleItem();
        }

        return false;
    }

    @Override
    public TzProductSku getSkuByPlatformIds(String platformProductId, String platformSkuId) {
        // TODO: 实现基于责任链的SKU查询
        throw new UnsupportedOperationException("此方法需要在责任链架构中重新实现");
    }

    @Override
    public BigDecimal getSingleItemPrice(String platformProductId) {
        // 使用验证链获取价格信息
        ProductSyncContext context = ProductSyncContext.builder()
            .platformProductId(platformProductId)
            .syncType(SyncType.VALIDATION)
            .build();

        ProductSyncHandler chain = chainBuilder.buildChain(SyncType.VALIDATION);
        ProductSyncResult result = executeChain(chain, context);

        if (result.isSuccess() && context.getProductDetail() != null) {
            return context.getProductDetail().getPrice();
        }

        return null;
    }

    @Override
    public TzProductSku getSingleItemDefaultSku(Long spuId) {
        // TODO: 实现基于责任链的默认SKU查询
        throw new UnsupportedOperationException("此方法需要在责任链架构中重新实现");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TzProductSku forceCreateDefaultSkuForSingleItem(Long spuId, String platformProductId) {
        // TODO: 实现基于责任链的默认SKU创建
        throw new UnsupportedOperationException("此方法需要在责任链架构中重新实现");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TzProductDTO> autoSyncFromPdcMapping(List<String> platformProductIds) {
        log.debug("批量同步产品数据（责任链模式）: 数量={}", platformProductIds.size());

        // TODO: 实现批量同步逻辑
        throw new UnsupportedOperationException("批量同步功能需要在责任链架构中重新实现");
    }

    @Override
    public AlibabaProductDetailDTO getAlibabaProductDetail(String platformProductId, boolean forceRefresh) {
        // 使用API获取处理器
        ProductSyncContext context = ProductSyncContext.builder()
            .platformProductId(platformProductId)
            .forceUpdate(forceRefresh)
            .syncType(SyncType.VALIDATION)
            .build();

        // 只执行数据验证和API获取处理器
        ProductSyncHandler chain = chainBuilder.buildValidationChain();
        ProductSyncResult result = executeChain(chain, context);

        return context.getProductDetail();
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 执行处理器链
     */
    private ProductSyncResult executeChain(ProductSyncHandler chain, ProductSyncContext context) {
        try {
            // 验证链条
            if (!chainBuilder.validateChain(chain)) {
                log.error("处理器链验证失败");
                return ProductSyncResult.failure("CHAIN_VALIDATION_FAILED", "处理器链配置错误");
            }

            // 打印链条信息（调试模式）
            if (log.isDebugEnabled()) {
                chainBuilder.printChainInfo(chain);
            }

            // 执行链条
            return chain.handle(context);

        } catch (Exception e) {
            log.error("处理器链执行异常: platformProductId={}", context.getPlatformProductId(), e);
            return ProductSyncResult.failure("CHAIN_EXECUTION_FAILED", "处理器链执行异常: " + e.getMessage());
        }
    }

    /**
     * 处理同步结果
     */
    private TzProductDTO handleSyncResult(ProductSyncResult result, String platformProductId) {
        if (result.isSuccess()) {
            log.debug("同步成功: platformProductId={}, 耗时={}ms",
                platformProductId, result.getProcessingTimeMs());
            return result.getProductDTO();
        } else {
            log.error("同步失败: platformProductId={}, 错误={}",
                platformProductId, result.getErrorMessage());
            // 根据业务需求决定是否抛出异常或返回null
            return null;
        }
    }

    /**
     * 处理超时情况
     */
    private TzProductDTO handleTimeout(String platformProductId) {
        log.warn("处理超时，使用降级处理: platformProductId={}", platformProductId);

        // 使用降级处理器链
        ProductSyncContext fallbackContext = ProductSyncContext.builder()
            .platformProductId(platformProductId)
            .syncType(SyncType.FALLBACK)
            .useFallback(true)
            .build();

        ProductSyncHandler fallbackChain = chainBuilder.buildChain(SyncType.FALLBACK);
        ProductSyncResult result = executeChain(fallbackChain, fallbackContext);

        return handleSyncResult(result, platformProductId);
    }
}
