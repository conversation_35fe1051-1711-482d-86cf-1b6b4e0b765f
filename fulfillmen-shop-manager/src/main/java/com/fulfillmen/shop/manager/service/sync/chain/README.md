# 产品同步责任链架构

## 📋 概述

本项目使用责任链设计模式重构了产品同步处理逻辑，将复杂的同步流程分解为独立的、可测试的处理器组件。

## 🎯 设计目标

- **单一职责**：每个处理器只负责一个特定的处理步骤
- **易于扩展**：可以轻松添加新的处理器或修改处理顺序
- **高可维护性**：代码结构清晰，便于理解和维护
- **向后兼容**：保持现有接口不变，支持渐进式迁移

## 🏗️ 架构设计

### 核心组件

```
com.fulfillmen.shop.manager.service.sync.chain/
├── core/                           # 核心接口和抽象类
│   ├── ProductSyncHandler.java     # 处理器接口
│   ├── AbstractProductSyncHandler.java # 抽象基类
│   ├── ProductSyncContext.java     # 上下文对象
│   ├── ProductSyncResult.java      # 结果对象
│   └── ProductSyncChainBuilder.java # 链条构建器
├── handlers/                       # 具体处理器实现
│   ├── DataValidationHandler.java  # 数据验证处理器
│   ├── ExistingDataCheckHandler.java # 现有数据检查处理器
│   ├── ApiDataFetchHandler.java    # API数据获取处理器
│   ├── SpuProcessHandler.java      # SPU处理器（待实现）
│   ├── SkuProcessHandler.java      # SKU处理器（待实现）
│   ├── DataIntegrityHandler.java   # 数据完整性处理器（待实现）
│   ├── CacheUpdateHandler.java     # 缓存更新处理器（待实现）
│   └── FallbackHandler.java        # 降级处理器（待实现）
├── config/                         # 配置类
│   └── ProductSyncChainConfig.java # 责任链配置
├── enums/                          # 枚举类
│   └── SyncType.java              # 同步类型枚举
└── README.md                       # 本文档
```

### 处理器链流程

```
数据验证 → 现有数据检查 → API数据获取 → SPU处理 → SKU处理 → 数据完整性检查 → 缓存更新
```

## 🚀 快速开始

### 1. 启用责任链模式

**方式一：配置文件启用**
在 `application.yml` 中配置：

```yaml
product:
  sync:
    chain:
      enabled: true          # 启用责任链模式
      debug: true           # 启用调试模式
      performance-monitoring: true # 启用性能监控
```

**方式二：使用预设配置**
```yaml
spring:
  profiles:
    include: chain  # 包含 application-chain.yml 配置
```

**方式三：环境变量**
```bash
export PRODUCT_SYNC_CHAIN_ENABLED=true
```

### 2. 使用新的服务实现

```java
@Autowired
private IProductSyncService productSyncService; // 自动注入基于责任链的实现

// 标准同步
TzProductDTO result = productSyncService.syncProductByPlatformId("123456789");

// 强制重新同步
TzProductDTO result = productSyncService.resyncProductByPlatformId("123456789", true);

// 获取或同步
TzProductDTO result = productSyncService.getOrSyncProductByPlatformId("123456789");
```

### 3. 自定义处理器

```java
@Component
public class CustomHandler extends AbstractProductSyncHandler {

    @Override
    public String getHandlerName() {
        return "CustomHandler";
    }

    @Override
    public int getPriority() {
        return 50; // 设置优先级
    }

    @Override
    protected ProductSyncResult doHandle(ProductSyncContext context) {
        // 实现自定义处理逻辑
        return ProductSyncResult.success(null);
    }
}
```

## 📊 同步类型

| 同步类型 | 描述 | 使用场景 |
|---------|------|----------|
| STANDARD | 标准同步 | 常规的产品同步，会检查缓存和现有数据 |
| FORCE_RESYNC | 强制重新同步 | 忽略现有数据和缓存，强制从API重新获取 |
| REPAIR | 修复同步 | 专门用于修复数据不一致或缺失的问题 |
| BATCH | 批量同步 | 批量处理多个产品的同步 |
| FALLBACK | 降级同步 | 在异常情况下使用的降级处理 |
| INCREMENTAL | 增量同步 | 只同步变更的数据 |
| VALIDATION | 验证同步 | 验证数据完整性，不进行实际的数据修改 |

## 🔧 配置选项

### 基础配置

```yaml
product:
  sync:
    chain:
      enabled: true                    # 启用责任链
      debug: false                     # 调试模式
      handler-timeout-ms: 5000         # 处理器超时时间
      chain-timeout-ms: 30000          # 整个链条超时时间
      performance-monitoring: true     # 性能监控
      fallback-enabled: true          # 降级处理
```

### 处理器配置

```yaml
product:
  sync:
    chain:
      handlers:
        data-validation:
          enabled: true
          timeout-ms: 1000
          continue-on-failure: false
        existing-data-check:
          enabled: true
          timeout-ms: 2000
          continue-on-failure: true
        api-data-fetch:
          enabled: true
          timeout-ms: 10000
          fallback-supported: true
```

### 监控配置

```yaml
product:
  sync:
    chain:
      monitoring:
        enabled: true
        record-execution-time: true
        slow-query-threshold-ms: 3000
        error-rate-threshold: 5.0
```

## 🧪 测试

### 单元测试

```java
@Test
void testProductSync() {
    ProductSyncContext context = new ProductSyncContext()
        .setPlatformProductId("123456789")
        .setSyncType(SyncType.STANDARD);

    ProductSyncHandler chain = chainBuilder.buildStandardChain();
    ProductSyncResult result = chain.handle(context);

    assertTrue(result.isSuccess());
}
```

### 集成测试

```java
@SpringBootTest
@TestPropertySource(properties = "product.sync.chain.enabled=true")
class ProductSyncIntegrationTest {

    @Autowired
    private IProductSyncService productSyncService;

    @Test
    void testRealProductSync() {
        TzProductDTO result = productSyncService.syncProductByPlatformId("123456789");
        assertNotNull(result);
    }
}
```

## 📈 性能监控

### 监控指标

- **处理器执行时间**：每个处理器的执行耗时
- **链条总执行时间**：整个处理链的总耗时
- **成功率**：处理成功的比例
- **错误率**：处理失败的比例
- **缓存命中率**：缓存使用情况

### 日志示例

```
2025-07-05 10:30:15.123 DEBUG [ProductSyncChain] 开始执行处理器: DataValidationHandler, platformProductId: 123456789
2025-07-05 10:30:15.125 DEBUG [ProductSyncChain] 处理器执行完成: DataValidationHandler, 耗时: 2ms, 成功: true
2025-07-05 10:30:15.130 DEBUG [ProductSyncChain] 处理器链: DataValidationHandler -> ExistingDataCheckHandler -> ApiDataFetchHandler
```

## 🔄 迁移指南

### 从旧版本迁移

1. **保持兼容性**：现有代码无需修改，接口保持不变
2. **渐进式启用**：通过配置开关控制新旧实现
3. **功能验证**：在测试环境充分验证后再切换到生产环境

### 迁移步骤

1. 部署新代码（默认使用旧实现）
2. 在测试环境启用责任链模式
3. 验证功能和性能
4. 在生产环境启用责任链模式
5. 监控运行状态
6. 移除旧实现（可选）

## 🐛 故障排除

### 常见问题

1. **处理器链验证失败**
   - 检查处理器的依赖注入是否正确
   - 确认处理器的优先级设置

2. **性能下降**
   - 检查处理器超时配置
   - 启用性能监控分析瓶颈

3. **数据不一致**
   - 检查事务配置
   - 验证处理器的异常处理逻辑

### 调试技巧

1. 启用调试模式：`product.sync.chain.debug=true`
2. 查看处理器执行日志
3. 使用性能监控分析执行时间
4. 检查上下文对象的状态变化

## 🔮 未来规划

- [ ] 完成所有处理器的实现
- [ ] 添加更多的监控指标
- [ ] 支持动态处理器配置
- [ ] 实现处理器的热插拔
- [ ] 添加可视化监控界面
- [ ] 支持分布式处理器链

## 📞 支持

如有问题或建议，请联系：
- 邮箱：<EMAIL>
- 项目地址：[内部Git仓库]
