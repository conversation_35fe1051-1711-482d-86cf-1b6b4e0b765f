/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.repository;

import com.fulfillmen.support.alibaba.api.response.goods.GoodsSearchResponse.GoodsInfo;
import java.util.List;
import com.baomidou.mybatisplus.extension.repository.IRepository;
import com.fulfillmen.shop.domain.dto.PageDTO;
import com.fulfillmen.shop.domain.dto.ProductInfoDTO;
import com.fulfillmen.shop.domain.dto.ProductSearchRequestDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.domain.entity.PdcProductMapping;
import com.fulfillmen.shop.domain.req.AggregateSearchReq;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsImageSearchResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsSellerResponse;
import com.fulfillmen.support.alibaba.enums.LanguageEnum;

/**
 * 商品映射仓储服务接口
 *
 * <pre>
 * 职责：
 * 1. 集中管理所有商品数据的数据库操作
 * 2. 提供缓存→数据库→API的三级数据获取策略
 * 3. 处理批量数据同步操作
 * 4. 统一的商品数据访问入口
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/5/29 23:25
 * @since 1.0.0
 */
public interface PdcProductMappingRepository extends IRepository<PdcProductMapping> {

    /**
     * 获取商品详情 带缓存
     *
     * @param id             商品ID
     * @param isForceRefresh 是否强制刷新
     * @return 商品详情
     */
    AlibabaProductDetailDTO getProductDetailWithCache(Long id, Boolean isForceRefresh);

    /**
     * 搜索商品信息列表 并自动同步入库
     *
     * @param request 搜索请求参数
     * @return 商品信息列表
     */
    PageDTO<ProductInfoDTO> searchProductInfoListSync(ProductSearchRequestDTO request);

    /**
     * 搜索商品信息列表 并自动同步入库 - 缓存版本
     *
     * @param request        搜索请求参数
     * @param isForceRefresh 是否强制刷新缓存
     * @return 商品信息列表
     */
    PageDTO<ProductInfoDTO> searchProductInfoListSyncWithCache(ProductSearchRequestDTO request, Boolean isForceRefresh);

    // ======================== 批量同步方法 ========================

    /**
     * 批量同步搜索结果中的商品数据
     *
     * @param goodsInfoList 1688搜索结果商品列表
     * @return 受影响的行数
     */
    List<PdcProductMapping> syncSearchResultData(List<GoodsInfo> goodsInfoList);

    /**
     * 批量同步图片搜索结果中的商品数据
     *
     * @param goodsInfoList 图片搜索结果商品列表
     * @return 同步后的商品映射列表
     */
    List<PdcProductMapping> syncImageSearchResultData(List<GoodsImageSearchResponse.GoodsInfo> goodsInfoList);

    /**
     * 批量同步卖家商品数据
     *
     * @param productInfoList 卖家商品列表
     * @return 受影响的行数
     */
    int syncSellerProductData(List<GoodsSellerResponse.ProductInfo> productInfoList);

    /**
     * 搜索相似商品 并同步进库
     *
     * @param offerId   商品ID
     * @param language  语言
     * @param pageIndex 页码
     * @param pageSize  每页大小
     * @return 商品信息列表
     */
    PageDTO<ProductInfoDTO> searchSimilarProductsSync(Long offerId,
        LanguageEnum language,
        Integer pageIndex,
        Integer pageSize);

    /**
     * 推荐商品列表
     *
     * @param language  语言
     * @param pageIndex 页码
     * @param pageSize  每页大小
     * @return 商品信息列表
     */
    PageDTO<ProductInfoDTO> recommendProducts(LanguageEnum language, Integer pageIndex, Integer pageSize);

    /**
     * 推荐商品列表 - 带缓存和同步优化
     *
     * <pre>
     * 🚀 优化特性：
     * 1. 智能缓存策略 - 基于语言和分页参数缓存
     * 2. 统一同步逻辑 - 与搜索商品相同的ID处理机制
     * 3. 数据一致性保证 - 从数据库读取，确保ID正确性
     * 4. 异常降级处理 - 缓存失败时自动降级到直接API调用
     *
     * 💡 关键改进：
     * - 修复ID同步问题：现在返回的ID是数据库内部生成的ID
     * - 保持1688原始ID在platformProductId字段中
     * - 支持强制刷新缓存功能
     * - 与searchProductInfoListSyncWithCache保持一致的处理流程
     * </pre>
     *
     * @param language       语言
     * @param pageIndex      页码
     * @param pageSize       每页大小
     * @param isForceRefresh 是否强制刷新缓存
     * @return 商品信息列表
     */
    PageDTO<ProductInfoDTO> recommendProductsWithCache(LanguageEnum language,
        Integer pageIndex,
        Integer pageSize,
        Boolean isForceRefresh);

    /**
     * 聚合搜索商品 - 不带缓存
     *
     * @deprecated 请使用 {@link #unifiedAggregateSearchWithCache(AggregateSearchReq, Boolean)} 统一搜索接口
     * @param request 聚合搜索请求参数
     * @return 商品信息列表
     */
    @Deprecated
    PageDTO<ProductInfoDTO> aggregateSearchSync(AggregateSearchReq request);

    /**
     * 统一聚合搜索商品 - 带缓存优化 🚀
     *
     * <pre>
     * 🎯 这是新的统一搜索入口，替代以下废弃方法：
     * - aggregateSearchSync() - 聚合搜索不带缓存
     * - searchProductInfoListSync() - 关键词搜索
     * - searchByImageSync() - 图片搜索
     *
     * 🔥 支持多种搜索方式的统一入口：
     * 1. 关键词搜索 (searchType = 1)
     * 2. 图片搜索 (searchType = 2)
     * 3. 混合搜索 (同时提供关键词和图片)
     *
     * ⚡ 统一的处理逻辑：
     * - API调用 → 数据同步 → 缓存存储 → 结果返回
     * - 智能缓存管理和失效策略
     * - 统一的错误处理和日志记录
     * - 性能监控和统计分析
     *
     * 💡 使用建议：
     * - 生产环境建议 isForceRefresh = false，利用缓存提升性能
     * - 数据更新后可设置 isForceRefresh = true，强制刷新缓存
     * - 支持所有原有搜索参数，无需修改业务逻辑
     * </pre>
     *
     * @param request        聚合搜索请求参数
     * @param isForceRefresh 是否强制刷新缓存，false=使用缓存，true=跳过缓存
     * @return 商品信息列表
     */
    PageDTO<ProductInfoDTO> unifiedAggregateSearchWithCache(AggregateSearchReq request, Boolean isForceRefresh);

}
