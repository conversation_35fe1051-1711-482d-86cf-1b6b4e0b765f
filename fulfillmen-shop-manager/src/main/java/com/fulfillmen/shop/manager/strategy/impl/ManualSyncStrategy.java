/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.strategy.impl;

import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.entity.PdcProductMapping;
import com.fulfillmen.shop.manager.core.repository.PdcProductMappingRepository;
import com.fulfillmen.shop.manager.strategy.ISyncStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 手动同步策略
 * <p>
 * 仅在明确请求时同步，不进行自动判断
 * </p>
 *
 * <AUTHOR>
 * @date 2025/06/19
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ManualSyncStrategy implements ISyncStrategy {

    private final PdcProductMappingRepository pdcProductMappingRepository;

    @Override
    public boolean shouldSync(String platformProductId, PdcProductMapping existingMapping) {
        // 手动同步策略：只有在明确调用时才同步
        // 这里总是返回false，实际同步由executeSync方法控制
        log.debug("手动同步策略：需要明确调用同步方法: {}", platformProductId);
        return false;
    }

    @Override
    public TzProductDTO executeSync(String platformProductId, boolean forceRefresh) {
        log.info("执行手动同步策略: platformProductId={}, forceRefresh={}", platformProductId, forceRefresh);

        try {
            // 手动同步总是强制刷新数据
            var productDetail = pdcProductMappingRepository.getProductDetailWithCache(Long
                .valueOf(platformProductId), true);

            if (productDetail == null) {
                log.warn("手动同步失败，未找到产品详情: {}", platformProductId);
                return null;
            }

            // TODO: 这里应该调用TzProduct的同步逻辑
            log.info("手动同步完成: {}", platformProductId);

            return buildBasicProductDTO(productDetail, platformProductId);

        } catch (Exception e) {
            log.error("手动同步异常: platformProductId={}", platformProductId, e);
            return null;
        }
    }

    @Override
    public List<TzProductDTO> batchSync(List<String> platformProductIds) {
        log.info("执行批量手动同步，数量: {}", platformProductIds.size());

        return platformProductIds.stream()
            .map(id -> executeSync(id, true))  // 手动同步强制刷新
            .filter(dto -> dto != null)
            .collect(Collectors.toList());
    }

    @Override
    public String getStrategyName() {
        return "ManualSyncStrategy";
    }

    /**
     * 构建基础产品DTO（临时方法，后续会被真正的转换逻辑替代）
     */
    private TzProductDTO buildBasicProductDTO(Object productDetail, String platformProductId) {
        return TzProductDTO.builder()
            .pdcPlatformProductId(platformProductId)
            .title("Manual Product " + platformProductId)
            .build();
    }
}
