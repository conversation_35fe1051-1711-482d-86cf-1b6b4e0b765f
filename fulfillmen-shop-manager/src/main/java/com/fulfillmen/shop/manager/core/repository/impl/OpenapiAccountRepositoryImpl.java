/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.repository.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.fulfillmen.shop.common.util.CacheConstants;
import com.fulfillmen.shop.dao.mapper.OpenapiAccountMapper;
import com.fulfillmen.shop.dao.mapper.OpenapiAccountPermissionMapper;
import com.fulfillmen.shop.dao.mapper.OpenapiInterfaceMapper;
import com.fulfillmen.shop.domain.convert.OpenapiAccountConvertMapping;
import com.fulfillmen.shop.domain.dto.openapi.OpenapiAccountInfoDTO;
import com.fulfillmen.shop.domain.dto.openapi.OpenapiAccountInfoDTO.OpenapiAccountPermissionDTO;
import com.fulfillmen.shop.domain.dto.openapi.OpenapiAccountInfoDTO.OpenapiInterfaceInfoDTO;
import com.fulfillmen.shop.domain.entity.OpenapiAccount;
import com.fulfillmen.shop.domain.entity.OpenapiAccountPermission;
import com.fulfillmen.shop.domain.entity.OpenapiInterface;
import com.fulfillmen.shop.domain.entity.enums.EnabledStatusEnum;
import com.fulfillmen.shop.manager.core.repository.OpenapiAccountRepository;
import com.fulfillmen.shop.common.exception.OpenapiExceptionI18n;
import com.fulfillmen.shop.common.enums.OpenapiErrorCodeEnum;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * openapi 账号仓储实现
 *
 * <AUTHOR>
 * @date 2025/5/30 22:18
 * @description: todo
 * @since 1.0.0
 */
@Slf4j
@Repository
public class OpenapiAccountRepositoryImpl extends CrudRepository<OpenapiAccountMapper, OpenapiAccount> implements OpenapiAccountRepository {

    private final OpenapiInterfaceMapper openapiInterfaceMapper;
    private final OpenapiAccountPermissionMapper openapiAccountPermissionMapper;

    public OpenapiAccountRepositoryImpl(OpenapiInterfaceMapper openapiInterfaceMapper,
        OpenapiAccountPermissionMapper openapiAccountPermissionMapper) {
        this.openapiInterfaceMapper = openapiInterfaceMapper;
        this.openapiAccountPermissionMapper = openapiAccountPermissionMapper;
    }

    @Override
    @Cached(name = CacheConstants.OPENAPI_ACCOUNT_ACCESSKEY, expire = 21_600, localLimit = 50, cacheType = CacheType.REMOTE, key = "#accessKey")
    public OpenapiAccountInfoDTO getOpenApiAccountInfoDTOByAccessKey(String accessKey) {
        // accesskey 是全局唯一的
        OpenapiAccount openapiAccount = this.baseMapper.selectOne(lambdaQuery().getWrapper()
            .eq(OpenapiAccount::getAccessKey, accessKey));
        // 账户不存在
        if (openapiAccount == null) {
            log.warn("accessKey is invalid: {}", accessKey);
            throw OpenapiExceptionI18n.of(OpenapiErrorCodeEnum.OPENAPI_ACCESS_KEY_INVALID);
        }
        // 账户状态校验
        if (openapiAccount.getStatus() == EnabledStatusEnum.DISABLED) {
            log.warn("account is disabled: {}", openapiAccount.getId());
            throw OpenapiExceptionI18n.of(OpenapiErrorCodeEnum.OPENAPI_ACCOUNT_BLOCKED);
        }
        OpenapiAccountInfoDTO openapiAccountInfoDTO = OpenapiAccountInfoDTO.builder()
            .id(openapiAccount.getId())
            .accessKey(openapiAccount.getAccessKey())
            .secretKey(openapiAccount.getSecretKey())
            .accountName(openapiAccount.getAccountName())
            .description(openapiAccount.getDescription())
            .qpsLimit(openapiAccount.getQpsLimit())
            .dailyLimit(openapiAccount.getDailyLimit())
            .signatureType(openapiAccount.getSignatureType())
            .rsaPublicKey(openapiAccount.getRsaPublicKey())
            .status(openapiAccount.getStatus())
            .build();
        // 获取账户的接口权限, 包含已禁用的接口权限
        LambdaQueryWrapper<OpenapiAccountPermission> openapiAccountPermissionLambdaQueryWrapper = new LambdaQueryWrapper<>();
        openapiAccountPermissionLambdaQueryWrapper.eq(OpenapiAccountPermission::getAccountId, openapiAccount.getId());
        List<OpenapiAccountPermission> openapiAccountPermissions = openapiAccountPermissionMapper
            .selectList(openapiAccountPermissionLambdaQueryWrapper);
        if (CollectionUtil.isNotEmpty(openapiAccountPermissions)) {
            // 收集账户的所有权限信息
            Map<Long, OpenapiAccountPermissionDTO> openapiAccountPermissionMap = openapiAccountPermissions.stream()
                .map(OpenapiAccountConvertMapping.INSTANCE::toOpenapiAccountPermissionDTO)
                .collect(Collectors.toMap(OpenapiAccountPermissionDTO::getInterfaceId, Function.identity()));
            // 获取接口信息, 只获取已启用的接口。 如果接口禁用代表，该接口权限已禁止了。
            List<OpenapiInterface> openapiInterfaces = openapiInterfaceMapper
                .selectList(new LambdaQueryWrapper<OpenapiInterface>()
                    .in(OpenapiInterface::getId, openapiAccountPermissionMap.keySet())
                    .eq(OpenapiInterface::getStatus, EnabledStatusEnum.ENABLED));
            // 收集接口信息 key 接口编码，value 接口信息
            Map<String, OpenapiInterfaceInfoDTO> interfaceMap = openapiInterfaces.stream()
                .map(OpenapiAccountConvertMapping.INSTANCE::toOpenapiInterfaceInfoDTO)
                .collect(Collectors.toMap(OpenapiInterfaceInfoDTO::getInterfaceCode, Function.identity()));
            openapiAccountInfoDTO.setPermissionMap(openapiAccountPermissionMap);
            openapiAccountInfoDTO.setInterfaceMap(interfaceMap);
        }
        return openapiAccountInfoDTO;
    }
}
