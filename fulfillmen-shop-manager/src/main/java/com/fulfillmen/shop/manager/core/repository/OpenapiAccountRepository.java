/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.repository;

import com.baomidou.mybatisplus.extension.repository.IRepository;
import com.fulfillmen.shop.domain.dto.openapi.OpenapiAccountInfoDTO;
import com.fulfillmen.shop.domain.entity.OpenapiAccount;

/**
 * openapi 账户仓储
 *
 * <AUTHOR>
 * @date 2025/5/30 22:17
 * @description: todo
 * @since 1.0.0
 */
public interface OpenapiAccountRepository extends IRepository<OpenapiAccount> {

    /**
     * 获取 openapi 账户信息
     * <pre>
     * 包含拥有的接口权限列表
     * </pre>
     *
     * @param accessKey 访问秘钥
     */
    OpenapiAccountInfoDTO getOpenApiAccountInfoDTOByAccessKey(String accessKey);

}
