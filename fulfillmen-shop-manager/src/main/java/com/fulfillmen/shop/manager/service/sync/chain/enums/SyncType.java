/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.service.sync.chain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 同步类型枚举
 * 
 * <pre>
 * 定义不同的同步场景：
 * 1. 标准同步 - 常规的产品同步
 * 2. 强制重新同步 - 忽略缓存，强制从API获取
 * 3. 修复同步 - 修复数据不一致问题
 * 4. 批量同步 - 批量处理多个产品
 * 5. 降级同步 - 异常情况下的降级处理
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/07/05
 * @since 2.0.0
 */
@Getter
@AllArgsConstructor
public enum SyncType {

    /**
     * 标准同步
     * 正常的产品同步流程，会检查缓存和现有数据
     */
    STANDARD("standard", "标准同步"),

    /**
     * 强制重新同步
     * 忽略现有数据和缓存，强制从API重新获取
     */
    FORCE_RESYNC("force_resync", "强制重新同步"),

    /**
     * 修复同步
     * 专门用于修复数据不一致或缺失的问题
     */
    REPAIR("repair", "修复同步"),

    /**
     * 批量同步
     * 批量处理多个产品的同步
     */
    BATCH("batch", "批量同步"),

    /**
     * 降级同步
     * 在异常情况下使用的降级处理
     */
    FALLBACK("fallback", "降级同步"),

    /**
     * 增量同步
     * 只同步变更的数据
     */
    INCREMENTAL("incremental", "增量同步"),

    /**
     * 验证同步
     * 验证数据完整性，不进行实际的数据修改
     */
    VALIDATION("validation", "验证同步");

    /**
     * 同步类型代码
     */
    private final String code;

    /**
     * 同步类型描述
     */
    private final String description;

    /**
     * 根据代码获取同步类型
     */
    public static SyncType fromCode(String code) {
        for (SyncType syncType : values()) {
            if (syncType.getCode().equals(code)) {
                return syncType;
            }
        }
        return STANDARD; // 默认返回标准同步
    }

    /**
     * 是否需要强制刷新缓存
     */
    public boolean shouldForceRefresh() {
        return this == FORCE_RESYNC || this == REPAIR;
    }

    /**
     * 是否允许使用缓存
     */
    public boolean allowCache() {
        return this != FORCE_RESYNC && this != REPAIR;
    }

    /**
     * 是否需要完整的数据验证
     */
    public boolean needFullValidation() {
        return this == REPAIR || this == VALIDATION;
    }

    /**
     * 是否支持降级处理
     */
    public boolean supportFallback() {
        return this != VALIDATION; // 验证同步不支持降级
    }
}
