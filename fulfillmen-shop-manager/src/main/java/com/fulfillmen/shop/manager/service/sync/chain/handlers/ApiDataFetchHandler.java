/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.service.sync.chain.handlers;

import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.manager.core.repository.PdcProductMappingRepository;
import com.fulfillmen.shop.manager.service.sync.chain.core.AbstractProductSyncHandler;
import com.fulfillmen.shop.manager.service.sync.chain.core.ProductSyncContext;
import com.fulfillmen.shop.manager.service.sync.chain.core.ProductSyncResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * API数据获取处理器
 *
 * <pre>
 * 处理器职责：
 * 1. 从阿里巴巴API获取产品详情
 * 2. 处理API调用异常
 * 3. 数据格式转换和验证
 * 4. 缓存策略处理
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/07/05
 * @since 2.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ApiDataFetchHandler extends AbstractProductSyncHandler {

    private final PdcProductMappingRepository pdcProductMappingRepository;

    @Override
    public String getHandlerName() {
        return "ApiDataFetchHandler";
    }

    @Override
    public int getPriority() {
        return 30; // 第三个执行
    }

    @Override
    protected ProductSyncResult doHandle(ProductSyncContext context) {
        log.debug("开始获取API数据: platformProductId={}", context.getPlatformProductId());

        // 1. 检查是否可以跳过API调用
        if (shouldSkipApiCall(context)) {
            log.debug("跳过API调用，使用现有数据: platformProductId={}", context.getPlatformProductId());
            return ProductSyncResult.success(null);
        }

        // 2. 确定是否强制刷新缓存
        boolean forceRefresh = shouldForceRefresh(context);
        addHandlerMetadata(context, "force_refresh", forceRefresh);

        try {
            // 3. 调用API获取产品详情
            Long productId = Long.valueOf(context.getPlatformProductId());
            AlibabaProductDetailDTO productDetail = pdcProductMappingRepository
                .getProductDetailWithCache(productId, forceRefresh);

            // 4. 验证API返回数据
            if (productDetail == null) {
                String errorMsg = "API返回空数据: platformProductId=" + context.getPlatformProductId();
                log.warn(errorMsg);
                context.addError(errorMsg);
                return handleApiFailure(context, "API_NO_DATA", errorMsg);
            }

            // 5. 验证产品详情数据完整性
            if (!validateProductDetail(context, productDetail)) {
                String errorMsg = "产品详情数据不完整: platformProductId=" + context.getPlatformProductId();
                log.warn(errorMsg);
                context.addWarning(errorMsg);
                // 数据不完整但继续处理，可能部分数据仍然有用
            }

            // 6. 设置到上下文
            context.setProductDetail(productDetail);

            // 7. 记录API调用成功
            recordApiSuccess(context, productDetail);

            log.debug("API数据获取成功: platformProductId={}, title={}",
                context.getPlatformProductId(), productDetail.getTitle());

            return ProductSyncResult.success(null);

        } catch (NumberFormatException e) {
            String errorMsg = "平台产品ID格式错误: " + context.getPlatformProductId();
            log.error(errorMsg, e);
            context.addError(errorMsg);
            return ProductSyncResult.failure("INVALID_PRODUCT_ID", errorMsg);

        } catch (Exception e) {
            String errorMsg = "API调用异常: " + e.getMessage();
            log.error("API数据获取失败: platformProductId={}", context.getPlatformProductId(), e);
            context.addError(errorMsg);
            return handleApiFailure(context, "API_CALL_FAILED", errorMsg);
        }
    }

    /**
     * 判断是否应该跳过API调用
     */
    private boolean shouldSkipApiCall(ProductSyncContext context) {
        // 如果上一个处理器标记跳过同步，则跳过API调用
        Boolean skipSync = context.getAttribute("skip_sync");
        if (skipSync != null && skipSync) {
            return true;
        }

        // 如果已经有产品详情数据，且不需要强制刷新，则跳过
        if (context.getProductDetail() != null && !shouldForceRefresh(context)) {
            return true;
        }

        return false;
    }

    /**
     * 判断是否应该强制刷新缓存
     */
    private boolean shouldForceRefresh(ProductSyncContext context) {
        // 强制更新标志
        if (context.isForceUpdate()) {
            return true;
        }

        // 同步类型要求强制刷新
        if (context.getSyncType().shouldForceRefresh()) {
            return true;
        }

        // 现有数据过期
        Boolean dataExpired = context.getAttribute("ExistingDataCheckHandler.data_expired");
        if (dataExpired != null && dataExpired) {
            return true;
        }

        return false;
    }

    /**
     * 验证产品详情数据
     */
    private boolean validateProductDetail(ProductSyncContext context, AlibabaProductDetailDTO productDetail) {
        boolean isValid = true;

        // 检查基本信息
        if (productDetail.getTitle() == null || productDetail.getTitle().trim().isEmpty()) {
            context.addWarning("产品标题为空");
            isValid = false;
        }

        if (productDetail.getPlatformProductId() == null) {
            context.addWarning("产品ID为空");
            isValid = false;
        }

        // 检查价格信息
        if (productDetail.getPrice() == null) {
            context.addWarning("代发价格为空");
            isValid = false;
        }

        // 检查图片信息
        if ((productDetail.getImages() == null || productDetail.getImages().isEmpty())
            && productDetail.getWhiteImage() == null) {
            context.addWarning("产品图片为空");
            isValid = false;
        }

        // 记录验证结果
        addHandlerMetadata(context, "product_detail_valid", isValid);

        return isValid;
    }

    /**
     * 处理API调用失败
     */
    private ProductSyncResult handleApiFailure(ProductSyncContext context, String errorCode, String errorMsg) {
        addHandlerMetadata(context, "api_failed", true);
        addHandlerMetadata(context, "api_error_code", errorCode);
        addHandlerMetadata(context, "api_error_message", errorMsg);

        // 如果支持降级处理，启用降级
        if (context.getSyncType().supportFallback()) {
            context.enableFallback();
            log.warn("API调用失败，启用降级处理: platformProductId={}", context.getPlatformProductId());
            return ProductSyncResult.success(null); // 继续处理链，使用降级逻辑
        } else {
            // 不支持降级，直接失败
            context.stopProcessing();
            return ProductSyncResult.failure(errorCode, errorMsg);
        }
    }

    /**
     * 记录API调用成功信息
     */
    private void recordApiSuccess(ProductSyncContext context, AlibabaProductDetailDTO productDetail) {
        addHandlerMetadata(context, "api_success", true);
        addHandlerMetadata(context, "api_call_time", System.currentTimeMillis());
        addHandlerMetadata(context, "product_title", productDetail.getTitle());
        addHandlerMetadata(context, "is_single_item", productDetail.isSingleItem());

        if (productDetail.getProductSkuList() != null) {
            addHandlerMetadata(context, "sku_count_from_api", productDetail.getProductSkuList().size());
        }
    }

    @Override
    public boolean canHandle(ProductSyncContext context) {
        // 除了验证类型的同步，都需要获取API数据
        return context.getSyncType() != com.fulfillmen.shop.manager.service.sync.chain.enums.SyncType.VALIDATION;
    }

    @Override
    protected boolean shouldContinueOnFailure() {
        return true; // API失败时可以尝试降级处理
    }

    @Override
    protected boolean supportFallback() {
        return true; // 支持降级处理
    }
}
