/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.service.impl;

import java.math.BigDecimal;
import java.util.List;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.dao.mapper.TzProductSkuMapper;
import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.domain.entity.PdcProductMapping;
import com.fulfillmen.shop.domain.entity.TzProductSku;
import com.fulfillmen.shop.domain.entity.enums.PlatformCodeEnum;
import com.fulfillmen.shop.domain.enums.ProductSyncStrategyEnum;
import com.fulfillmen.shop.manager.core.repository.PdcProductMappingRepository;
import com.fulfillmen.shop.manager.service.IProductSyncService;
import com.fulfillmen.shop.manager.strategy.ISyncStrategy;
import com.fulfillmen.shop.manager.strategy.SyncStrategyFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 产品同步服务实现 V2 - 门面模式架构
 *
 * <p>
 * 🎯 架构设计理念：
 * 1. 门面模式：对外提供统一简洁的接口
 * 2. 策略模式：根据配置动态选择同步策略
 * 3. 职责分离：数据访问交给Repository，同步逻辑交给Strategy
 * 4. 扩展性：支持多平台和多策略无缝扩展
 *
 * 🚀 调用流程：
 * OpenAPI/业务 -> IProductSyncService -> 策略选择 -> PdcProductMappingRepository -> 数据源
 * </p>
 *
 * <AUTHOR>
 * @date 2025/06/19
 * @since 2.0.0
 */
@Slf4j
@Service("productSyncServiceV2")
@RequiredArgsConstructor
public class ProductSyncServiceV2Impl implements IProductSyncService {

    private final PdcProductMappingRepository pdcProductMappingRepository;
    private final TzProductSkuMapper tzProductSkuMapper;
    private final SyncStrategyFactory syncStrategyFactory;

    /**
     * 默认同步策略
     */
    private static final ProductSyncStrategyEnum DEFAULT_SYNC_STRATEGY = ProductSyncStrategyEnum.AUTO;

    // ==================== 核心同步方法 ====================

    @Override
    public TzProductDTO syncProductByPlatformId(String platformProductId) {
        log.debug("同步产品数据: {}", platformProductId);

        ISyncStrategy strategy = getSyncStrategy(DEFAULT_SYNC_STRATEGY);
        return strategy.executeSync(platformProductId, false);
    }

    @Override
    public TzProductDTO resyncProductByPlatformId(String platformProductId, boolean forceUpdate) {
        log.debug("重新同步产品数据: {}, forceUpdate: {}", platformProductId, forceUpdate);

        // 强制重新同步使用手动策略
        ISyncStrategy strategy = getSyncStrategy(ProductSyncStrategyEnum.MANUAL);
        return strategy.executeSync(platformProductId, forceUpdate);
    }

    // ==================== 统一数据获取方法 ====================

    @Override
    public TzProductDTO getOrSyncProductByPlatformId(String platformProductId) {
        log.debug("获取或同步产品数据: {}", platformProductId);

        try {
            // 1. 先检查是否已存在PdcProductMapping数据
            PdcProductMapping existingMapping = getExistingPdcMapping(platformProductId);

            // 2. 根据策略判断是否需要同步
            ISyncStrategy strategy = getSyncStrategy(DEFAULT_SYNC_STRATEGY);

            if (strategy.shouldSync(platformProductId, existingMapping)) {
                log.debug("根据策略判断需要同步: {}", platformProductId);
                return strategy.executeSync(platformProductId, false);
            } else {
                log.debug("根据策略判断无需同步，返回现有数据: {}", platformProductId);
                // TODO: 从现有数据构建TzProductDTO
                return buildFromExistingData(existingMapping);
            }

        } catch (Exception e) {
            log.error("获取或同步产品数据失败: {}", platformProductId, e);
            return null;
        }
    }

    @Override
    public TzProductDTO getOrSyncProductWithRetryAndTimeout(String platformProductId) {
        log.debug("获取产品数据（超时控制）: {}", platformProductId);

        long timeoutMs = 10000L; // 默认10秒超时
        long startTime = System.currentTimeMillis();

        try {
            TzProductDTO result = getOrSyncProductByPlatformId(platformProductId);

            long duration = System.currentTimeMillis() - startTime;
            if (duration > timeoutMs) {
                log.warn("产品数据获取超时: {}, 耗时: {}ms", platformProductId, duration);
                // 超时降级：直接从阿里巴巴获取基础数据
                return buildFromAlibabaData(platformProductId);
            }

            return result;

        } catch (Exception e) {
            log.error("获取产品数据异常: {}", platformProductId, e);
            return buildFromAlibabaData(platformProductId);
        }
    }

    @Override
    public AlibabaProductDetailDTO getAlibabaProductDetail(String platformProductId, boolean forceRefresh) {
        log.debug("获取阿里巴巴产品详情: {}, forceRefresh: {}", platformProductId, forceRefresh);

        try {
            Long productId = Long.valueOf(platformProductId);
            return pdcProductMappingRepository.getProductDetailWithCache(productId, forceRefresh);
        } catch (Exception e) {
            log.error("获取阿里巴巴产品详情失败: {}", platformProductId, e);
            return null;
        }
    }

    // ==================== SKU相关方法 ====================

    @Override
    public List<TzProductSku> getSkuListBySpuId(Long spuId) {
        log.debug("获取SKU列表: spuId={}", spuId);

        LambdaQueryWrapper<TzProductSku> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TzProductSku::getSpuId, spuId);
        return tzProductSkuMapper.selectList(queryWrapper);
    }

    @Override
    public TzProductSku getSkuByPlatformIds(String platformProductId, String platformSkuId) {
        log.debug("获取SKU: platformProductId={}, platformSkuId={}", platformProductId, platformSkuId);

        LambdaQueryWrapper<TzProductSku> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TzProductSku::getPlatformProductId, platformProductId)
            .eq(TzProductSku::getPlatformSku, platformSkuId);
        return tzProductSkuMapper.selectOne(queryWrapper);
    }

    @Override
    public TzProductSku getSingleItemDefaultSku(Long spuId) {
        log.debug("获取单品默认SKU: spuId={}", spuId);

        LambdaQueryWrapper<TzProductSku> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TzProductSku::getSpuId, spuId).last("LIMIT 1");
        return tzProductSkuMapper.selectOne(queryWrapper);
    }

    @Override
    public TzProductSku forceCreateDefaultSkuForSingleItem(Long spuId, String platformProductId) {
        log.warn("强制创建单品默认SKU: spuId={}, platformProductId={}", spuId, platformProductId);

        // TODO: 实现强制创建逻辑
        return null;
    }

    // ==================== 数据验证和修复 ====================

    @Override
    public boolean validateAndRepairProductSkus(String platformProductId) {
        log.debug("验证并修复产品SKU: {}", platformProductId);

        // TODO: 实现验证和修复逻辑
        return true;
    }

    // ==================== 价格相关 ====================

    @Override
    public boolean isSingleItem(String platformProductId) {
        log.debug("检查是否单品: {}", platformProductId);

        try {
            AlibabaProductDetailDTO productDetail = getAlibabaProductDetail(platformProductId, false);
            return productDetail != null && productDetail.isSingleItem();
        } catch (Exception e) {
            log.warn("检查单品状态失败: {}", platformProductId, e);
            return false;
        }
    }

    @Override
    public BigDecimal getSingleItemPrice(String platformProductId) {
        log.debug("获取单品价格: {}", platformProductId);

        try {
            AlibabaProductDetailDTO productDetail = getAlibabaProductDetail(platformProductId, false);
            if (productDetail != null) {
                // TODO: 提取价格逻辑
                return productDetail.getPrice();
            }
        } catch (Exception e) {
            log.error("获取单品价格失败: {}", platformProductId, e);
        }
        return null;
    }

    // ==================== 批量同步方法 ====================

    @Override
    public List<TzProductDTO> autoSyncFromPdcMapping(List<String> platformProductIds) {
        log.info("批量自动同步: 数量={}", platformProductIds.size());

        ISyncStrategy strategy = getSyncStrategy(DEFAULT_SYNC_STRATEGY);
        return strategy.batchSync(platformProductIds);
    }

    // ==================== 私有工具方法 ====================

    /**
     * 获取同步策略
     */
    private ISyncStrategy getSyncStrategy(ProductSyncStrategyEnum strategyEnum) {
        return syncStrategyFactory.getStrategy(strategyEnum);
    }

    /**
     * 获取现有的PdcProductMapping数据
     */
    private PdcProductMapping getExistingPdcMapping(String platformProductId) {
        try {
            LambdaQueryWrapper<PdcProductMapping> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(PdcProductMapping::getPlatformCode, PlatformCodeEnum.PLATFORM_CODE_1688)
                .eq(PdcProductMapping::getPlatformProductId, platformProductId);

            return pdcProductMappingRepository.getOne(queryWrapper);
        } catch (Exception e) {
            log.warn("查询现有PdcProductMapping失败: {}", platformProductId, e);
            return null;
        }
    }

    /**
     * 从现有数据构建TzProductDTO
     */
    private TzProductDTO buildFromExistingData(PdcProductMapping mapping) {
        if (mapping == null) {
            return null;
        }

        // TODO: 实现从PdcProductMapping到TzProductDTO的转换
        return TzProductDTO.builder()
            .pdcPlatformProductId(mapping.getPlatformProductId())
            .title(mapping.getPlatformProductName())
            .build();
    }

    /**
     * 从阿里巴巴数据构建基础TzProductDTO（降级方案）
     */
    private TzProductDTO buildFromAlibabaData(String platformProductId) {
        try {
            AlibabaProductDetailDTO alibabaData = getAlibabaProductDetail(platformProductId, false);
            if (alibabaData != null) {
                return TzProductDTO.builder()
                    .pdcPlatformProductId(platformProductId)
                    .title(alibabaData.getTitle())
                    .titleTrans(alibabaData.getTitleTrans())
                    .build();
            }
        } catch (Exception e) {
            log.error("构建阿里巴巴降级数据失败: {}", platformProductId, e);
        }
        return null;
    }
}
