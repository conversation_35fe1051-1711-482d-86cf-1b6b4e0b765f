/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.service.sync.chain.core;

import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.domain.entity.PdcProductMapping;
import com.fulfillmen.shop.domain.entity.TzProductSku;
import com.fulfillmen.shop.domain.entity.TzProductSpu;
import com.fulfillmen.shop.manager.service.sync.chain.enums.SyncType;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 产品同步上下文对象
 *
 * <pre>
 * 上下文对象职责：
 * 1. 携带输入参数和配置
 * 2. 存储处理过程中的中间数据
 * 3. 记录处理结果和状态
 * 4. 提供扩展属性支持
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/07/05
 * @since 2.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductSyncContext {

    // ==================== 输入参数 ====================

    /**
     * 平台产品ID
     */
    private String platformProductId;

    /**
     * 是否强制更新
     */
    @Builder.Default
    private boolean forceUpdate = false;

    /**
     * 同步类型
     */
    @Builder.Default
    private SyncType syncType = SyncType.STANDARD;

    /**
     * 请求开始时间
     */
    @Builder.Default
    private LocalDateTime requestStartTime = LocalDateTime.now();

    // ==================== 处理过程中的数据 ====================

    /**
     * 现有的SPU数据
     */
    private TzProductSpu existingSpu;

    /**
     * 现有的SKU列表
     */
    private List<TzProductSku> existingSkus;

    /**
     * 从API获取的产品详情
     */
    private AlibabaProductDetailDTO productDetail;

    /**
     * PdcProductMapping数据
     */
    private PdcProductMapping pdcMapping;

    /**
     * 新创建的SPU
     */
    private TzProductSpu newSpu;

    /**
     * 新创建的SKU列表
     */
    private List<TzProductSku> newSkus;

    // ==================== 结果数据 ====================

    /**
     * 最终结果
     */
    private TzProductDTO result;

    /**
     * 警告信息列表
     */
    @Builder.Default
    private List<String> warnings = new ArrayList<>();

    /**
     * 错误信息列表
     */
    @Builder.Default
    private List<String> errors = new ArrayList<>();

    // ==================== 控制标志 ====================

    /**
     * 是否应该继续处理
     */
    @Builder.Default
    private boolean shouldContinue = true;

    /**
     * 是否有错误
     */
    @Builder.Default
    private boolean hasErrors = false;

    /**
     * 是否需要回滚
     */
    @Builder.Default
    private boolean needRollback = false;

    /**
     * 是否使用降级处理
     */
    @Builder.Default
    private boolean useFallback = false;

    // ==================== 扩展属性 ====================

    /**
     * 扩展属性Map
     */
    @Builder.Default
    private Map<String, Object> attributes = new HashMap<>();

    /**
     * 处理器执行记录
     */
    @Builder.Default
    private List<String> handlerExecutionLog = new ArrayList<>();

    // ==================== 便捷方法 ====================

    /**
     * 添加警告信息
     */
    public ProductSyncContext addWarning(String warning) {
        this.warnings.add(warning);
        return this;
    }

    /**
     * 添加错误信息
     */
    public ProductSyncContext addError(String error) {
        this.errors.add(error);
        this.hasErrors = true;
        return this;
    }

    /**
     * 设置扩展属性
     */
    public ProductSyncContext setAttribute(String key, Object value) {
        this.attributes.put(key, value);
        return this;
    }

    /**
     * 获取扩展属性
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key) {
        return (T) this.attributes.get(key);
    }

    /**
     * 记录处理器执行
     */
    public ProductSyncContext logHandlerExecution(String handlerName) {
        this.handlerExecutionLog.add(handlerName + " - " + LocalDateTime.now());
        return this;
    }

    /**
     * 停止处理链
     */
    public ProductSyncContext stopProcessing() {
        this.shouldContinue = false;
        return this;
    }

    /**
     * 标记需要回滚
     */
    public ProductSyncContext markForRollback() {
        this.needRollback = true;
        return this;
    }

    /**
     * 启用降级处理
     */
    public ProductSyncContext enableFallback() {
        this.useFallback = true;
        return this;
    }
}
