/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.event;

import com.fulfillmen.shop.domain.entity.TzUser;
import com.fulfillmen.shop.manager.core.common.impl.CaptchaManager;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/4/25 17:27
 * @description: todo
 * @since 1.0.0
 */
@Slf4j
@Component
public class UserEventListener {

    private final CaptchaManager captchaManager;

    public UserEventListener(CaptchaManager captchaManager) {
        this.captchaManager = captchaManager;
    }

    /**
     * 用户注册事件监听 异步发送
     *
     * @param userEvent 用户事件
     */
    @Async
    @EventListener(condition = "#userEvent.userEventEnum == T(com.fulfillmen.shop.manager.event.UserEventEnum).ON_REGISTER " + "and #userEvent.tzUser != null " + "and #userEvent.tzUser.id != null")
    public void onUserRegisterEvent(UserEvent userEvent) {
        TzUser tzUser = userEvent.getTzUser();
        log.info("用户注册事件监听器 --- 异步发送邮件 ---，用户ID: {}", tzUser.getId());
        // 发送邮件
        try {
            this.captchaManager.getCaptchaByActivationMail(tzUser.getId(), tzUser.getUsername(), tzUser.getEmail());
        } catch (Exception e) {
            log.warn("发送注册邮件失败 : [{}] ", e.getMessage(), e);
        }
    }

    /**
     * 用户重置密码事件监听 异步发送
     *
     * @param userEvent 用户事件
     */
    @Async
    @EventListener(condition = "#userEvent.userEventEnum == T(com.fulfillmen.shop.manager.event.UserEventEnum).ON_RESET_PASSWORD " + "and #userEvent.tzUser != null " + "and #userEvent.tzUser.id != null")
    public void onUserResetPasswordEvent(UserEvent userEvent) {
        TzUser tzUser = userEvent.getTzUser();
        log.info("用户重置密码事件监听器 --- 异步发送邮件 ---，用户ID: {}", tzUser.getId());
        try {
            this.captchaManager.sendEmailCaptcha(tzUser.getEmail());
        } catch (Exception e) {
            log.warn("发送重置密码邮件失败 : [{}] ", e.getMessage(), e);
        }
    }

}
