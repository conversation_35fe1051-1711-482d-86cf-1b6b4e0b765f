/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.wms.api;

import com.fulfillmen.shop.common.enums.FulfillmenErrorCodeEnum;
import com.fulfillmen.shop.common.enums.FulfillmenValidationCodeEnum;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.common.util.SecureUtils;
import com.fulfillmen.shop.manager.support.wms.configure.WmsProperties;
import com.fulfillmen.shop.manager.support.wms.model.*;
import cn.hutool.json.JSONUtil;
import cn.hutool.json.JSONConfig;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.List;
import java.util.Optional;

/**
 * wms api 相关接口
 *
 * <AUTHOR>
 * @date 2025/4/27 10:46
 * @description: todo
 * @since 1.0.0
 */
@Slf4j
@Component
public class WmsApi {

    private final WebClient fmWebClient;
    private final WmsProperties wmsProperties;

    public WmsApi(WebClient fmWebClient, WmsProperties wmsProperties) {
        this.fmWebClient = fmWebClient;
        this.wmsProperties = wmsProperties;
    }

    private static final String ACCOUNT_INFO_API = "/Alibaba/OpenApi/1688ShopLogin.ashx?code={0}";
    private static final String CREATE_PURCHASE_ORDER_API = "/Alibaba/OpenApi/1688ShopOrderCreate.ashx";
    private static final String CREATE_PRODUCT_API = "/api-json/CreateGoods.aspx?Key={0}";
    private static final String PRODUCT_INQUIRIES_API = "/api-json/GetGoodsList.aspx?Key={0}&page={0}&Barcode={0}";

    /**
     * 通过 authCode 获取 wms 账户信息
     *
     * @param authCode 授权码
     * @return wms 账户信息
     */
    public Optional<WmsAccountInfo> getAccountInfo(String authCode) {
        String getAccountInfoUrl = wmsProperties.getBaseUrl() + ACCOUNT_INFO_API;

        // 定义响应的泛型类型
        ParameterizedTypeReference<WmsApiResponse<WmsDataDto>> responseType = new ParameterizedTypeReference<>() {
        };

        try {
            return fmWebClient.get()
                .uri(getAccountInfoUrl, authCode)
                .retrieve()
                .bodyToMono(responseType)
                .map(this::transformApiResponse)
                .blockOptional();
        } catch (Exception e) {
            log.error("获取WMS账户信息失败: authCode=[{}], error=[{}]", authCode, e.getMessage(), e);
            return Optional.empty();
        }
    }

    /**
     * WMS 响应 信息解密
     * 
     * @param apiResponse
     * @return
     */
    @NotNull
    private WmsAccountInfo transformApiResponse(WmsApiResponse<WmsDataDto> apiResponse) {
        log.info("WMS 响应 DTO: {}", apiResponse);

        if (!apiResponse.isSuccess()) {
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.API_CALL_FAILED, apiResponse.getMessage());
        }

        WmsDataDto data = apiResponse.getData();
        if (data == null) {
            throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PARAM_MISSING, "data");
        }

        String encryptedUserInfo = data.getEncryptedUserInfo();
        if (encryptedUserInfo == null || encryptedUserInfo.isBlank()) {
            log.error("获取encryptedUserInfo 字段失败，[{}]", encryptedUserInfo);
            throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PARAM_MISSING, "EncryptedUserInfo");
        }

        String decryptedUserInfo = SecureUtils.decryptByRsaPrivateKey(encryptedUserInfo, WmsProperties.RSA_SECRET_KEY);

        // 配置 Hutool JSON 解析器忽略大小写，这是解决问题的关键
        JSONConfig config = JSONConfig.create().setIgnoreCase(true);
        ExtUserInfo userInfo = JSONUtil.toBean(decryptedUserInfo, config, ExtUserInfo.class);

        WmsAccountInfo wmsAccountInfo = new WmsAccountInfo();
        wmsAccountInfo.setUserinfo(userInfo);

        // cusCode 可以从解密后的用户信息中获取。
        // 其他字段在API响应中不存在，所以为null
        if (userInfo != null) {
            wmsAccountInfo.setCusCode(userInfo.getCusCode());
        }

        return wmsAccountInfo;
    }

    /**
     * WMS 创建采购单请求
     *
     * @param request
     * @return
     */
    public Optional<WmsApiResponse<Object>> createPurchaseOrder(WmsCreateOrder request) {
        String createOrderUrl = wmsProperties.getBaseUrl() + CREATE_PURCHASE_ORDER_API;

        ParameterizedTypeReference<WmsApiResponse<Object>> responseType = new ParameterizedTypeReference<>() {
        };

        try {
            log.info("向WMS创建采购订单请求: {}", request);
            return fmWebClient.post()
                .uri(createOrderUrl)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(responseType)
                .blockOptional();
        } catch (Exception e) {
            log.error("创建采购订单失败: request=[{}], error=[{}]", request, e.getMessage(), e);
            return Optional.empty();
        }
    }

    /**
     * WMS 创建产品请求
     *
     * @param request
     * @param apiKey
     * @return
     */
    public Optional<WmsApiResponse<Object>> createProduct(WmsProduct request, String apiKey) {
        String createProductUrl = wmsProperties.getBaseUrl() + CREATE_PRODUCT_API;

        ParameterizedTypeReference<WmsApiResponse<Object>> responType = new ParameterizedTypeReference<>() {
        };

        try {
            log.info("向WMS创建产品请求: {}", request);
            return fmWebClient.post()
                .uri(createProductUrl, apiKey)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(responType)
                .blockOptional();
        } catch (Exception e) {
            log.error("创建产品失败: request=[{}], error=[{}]", request, e.getMessage(), e);
            return Optional.empty();
        }
    }

    /**
     * WMS 获取产品信息请求
     *
     * @param apiKey
     * @param page
     * @param barcode
     * @return
     */
    public Optional<WmsApiResponse<List<WmsProduct>>> getProductList(String apiKey, Integer page, String barcode) {
        String getProductListUrl = wmsProperties.getBaseUrl() + PRODUCT_INQUIRIES_API;

        ParameterizedTypeReference<WmsApiResponse<List<WmsProduct>>> responseType = new ParameterizedTypeReference<>() {
        };

        try {
            log.info("向WMS获取产品列表: apiKey=[{}], page=[{}], barcode=[{}]", apiKey, page, barcode);
            // 将响应体作为字符串接收，以绕过Content-Type检查
            return fmWebClient.get()
                .uri(getProductListUrl, apiKey, page, barcode)
                .retrieve()
                .bodyToMono(responseType)
                .blockOptional();
        } catch (Exception e) {
            log.error("获取产品列表失败: 解析响应或执行请求时发生错误, error={}", e.getMessage(), e);
            return Optional.empty();
        }
    }
}
