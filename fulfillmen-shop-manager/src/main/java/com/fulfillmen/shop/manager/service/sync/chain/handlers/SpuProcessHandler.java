/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.service.sync.chain.handlers;

import com.fulfillmen.shop.dao.mapper.TzProductSpuMapper;
import com.fulfillmen.shop.domain.convert.TzProductMapping;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.domain.entity.TzProductSpu;
import com.fulfillmen.shop.manager.service.sync.chain.core.AbstractProductSyncHandler;
import com.fulfillmen.shop.manager.service.sync.chain.core.ProductSyncContext;
import com.fulfillmen.shop.manager.service.sync.chain.core.ProductSyncResult;
import com.fulfillmen.shop.manager.service.sync.chain.enums.SyncType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * SPU处理器
 *
 * <pre>
 * 处理器职责：
 * 1. 创建或更新SPU数据
 * 2. SPU数据映射和转换
 * 3. SPU持久化操作
 * 4. SPU数据验证
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/07/05
 * @since 2.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SpuProcessHandler extends AbstractProductSyncHandler {

    private final TzProductSpuMapper tzProductSpuMapper;

    @Override
    public String getHandlerName() {
        return "SpuProcessHandler";
    }

    @Override
    public int getPriority() {
        return 40; // 在API数据获取之后执行
    }

    @Override
    protected ProductSyncResult doHandle(ProductSyncContext context) {
        log.debug("开始处理SPU数据: platformProductId={}", context.getPlatformProductId());

        // 1. 检查是否跳过SPU处理
        if (shouldSkipSpuProcess(context)) {
            log.debug("跳过SPU处理: platformProductId={}", context.getPlatformProductId());
            return ProductSyncResult.success(null);
        }

        // 2. 验证必要数据
        if (!validateRequiredData(context)) {
            String errorMsg = "SPU处理所需数据不完整";
            context.addError(errorMsg);
            return ProductSyncResult.failure("SPU_DATA_INCOMPLETE", errorMsg);
        }

        try {
            // 3. 判断是创建还是更新
            TzProductSpu existingSpu = context.getExistingSpu();
            TzProductSpu processedSpu;

            if (existingSpu != null) {
                // 更新现有SPU
                processedSpu = updateExistingSpu(context, existingSpu);
                addHandlerMetadata(context, "operation", "update");
            } else {
                // 创建新SPU
                processedSpu = createNewSpu(context);
                addHandlerMetadata(context, "operation", "create");
            }

            if (processedSpu == null) {
                String errorMsg = "SPU处理失败";
                context.addError(errorMsg);
                return ProductSyncResult.failure("SPU_PROCESS_FAILED", errorMsg);
            }

            // 4. 设置处理结果到上下文
            context.setNewSpu(processedSpu);

            // 5. 记录处理成功信息
            recordSpuProcessSuccess(context, processedSpu);

            log.debug("SPU处理完成: platformProductId={}, spuId={}, operation={}",
                context.getPlatformProductId(), processedSpu.getId(),
                getHandlerMetadata(context, "operation"));

            return ProductSyncResult.success(null);

        } catch (Exception e) {
            String errorMsg = "SPU处理异常: " + e.getMessage();
            log.error("SPU处理失败: platformProductId={}", context.getPlatformProductId(), e);
            context.addError(errorMsg);
            return ProductSyncResult.failure("SPU_PROCESS_EXCEPTION", errorMsg);
        }
    }

    /**
     * 判断是否应该跳过SPU处理
     */
    private boolean shouldSkipSpuProcess(ProductSyncContext context) {
        // 如果标记跳过同步，则跳过SPU处理
        Boolean skipSync = context.getAttribute("skip_sync");
        if (skipSync != null && skipSync) {
            return true;
        }

        // 如果使用降级处理，可能跳过SPU处理
        if (context.isUseFallback()) {
            Boolean apiSuccess = context.getAttribute("ApiDataFetchHandler.api_success");
            if (apiSuccess == null || !apiSuccess) {
                return true;
            }
        }

        return false;
    }

    /**
     * 验证处理SPU所需的数据
     */
    private boolean validateRequiredData(ProductSyncContext context) {
        // 检查产品详情数据
        AlibabaProductDetailDTO productDetail = context.getProductDetail();
        if (productDetail == null) {
            context.addError("缺少产品详情数据");
            return false;
        }

        // 检查基本字段
        if (productDetail.getTitle() == null || productDetail.getTitle().trim().isEmpty()) {
            context.addWarning("产品标题为空");
        }

        if (productDetail.getPlatformProductId() == null) {
            context.addError("产品ID为空");
            return false;
        }

        return true;
    }

    /**
     * 创建新SPU
     */
    private TzProductSpu createNewSpu(ProductSyncContext context) {
        AlibabaProductDetailDTO productDetail = context.getProductDetail();

        try {
            // 使用Mapstruct转换
            TzProductSpu newSpu = TzProductMapping.INSTANCE.toTzProductSpu(productDetail);

            if (newSpu == null) {
                log.error("Mapstruct转换返回null: platformProductId={}", context.getPlatformProductId());
                return null;
            }

            // 插入数据库
            int insertResult = tzProductSpuMapper.insert(newSpu);
            if (insertResult <= 0) {
                log.error("SPU插入数据库失败: platformProductId={}", context.getPlatformProductId());
                return null;
            }

            log.debug("新SPU创建成功: spuId={}, platformProductId={}",
                newSpu.getId(), context.getPlatformProductId());

            return newSpu;

        } catch (Exception e) {
            log.error("创建新SPU异常: platformProductId={}", context.getPlatformProductId(), e);
            return null;
        }
    }

    /**
     * 更新现有SPU
     */
    private TzProductSpu updateExistingSpu(ProductSyncContext context, TzProductSpu existingSpu) {
        AlibabaProductDetailDTO productDetail = context.getProductDetail();

        try {
            // 判断是否需要更新
            if (!needsUpdate(context, existingSpu, productDetail)) {
                log.debug("SPU数据无需更新: spuId={}", existingSpu.getId());
                addHandlerMetadata(context, "update_skipped", true);
                return existingSpu;
            }

            // 使用Mapstruct转换新数据
            TzProductSpu updatedSpu = TzProductMapping.INSTANCE.toTzProductSpu(productDetail);

            if (updatedSpu == null) {
                log.error("Mapstruct转换返回null: platformProductId={}", context.getPlatformProductId());
                return null;
            }

            // 更新现有SPU的字段
            updateSpuFields(existingSpu, updatedSpu);

            // 更新数据库
            int updateResult = tzProductSpuMapper.updateById(existingSpu);
            if (updateResult <= 0) {
                log.error("SPU更新数据库失败: spuId={}", existingSpu.getId());
                return null;
            }

            log.debug("SPU更新成功: spuId={}, platformProductId={}",
                existingSpu.getId(), context.getPlatformProductId());

            return existingSpu;

        } catch (Exception e) {
            log.error("更新SPU异常: spuId={}, platformProductId={}",
                existingSpu.getId(), context.getPlatformProductId(), e);
            return null;
        }
    }

    /**
     * 判断SPU是否需要更新
     */
    private boolean needsUpdate(ProductSyncContext context, TzProductSpu existingSpu,
        AlibabaProductDetailDTO productDetail) {
        // 强制更新模式
        if (context.isForceUpdate() || context.getSyncType() == SyncType.FORCE_RESYNC) {
            return true;
        }

        // 修复模式
        if (context.getSyncType() == SyncType.REPAIR) {
            return true;
        }

        // 检查关键字段是否有变化
        if (!java.util.Objects.equals(existingSpu.getTitle(), productDetail.getTitle())) {
            return true;
        }

        if (!java.util.Objects.equals(existingSpu.getMainImage(), productDetail.getImages().get(0))) {
            return true;
        }

        // 检查数据是否过期（通过元数据判断）
        Boolean dataExpired = context.getAttribute("ExistingDataCheckHandler.data_expired");
        if (dataExpired != null && dataExpired) {
            return true;
        }

        return false;
    }

    /**
     * 更新SPU字段
     */
    private void updateSpuFields(TzProductSpu existingSpu, TzProductSpu updatedSpu) {
        // 更新主要字段
        existingSpu.setTitle(updatedSpu.getTitle());
        existingSpu.setTitleTrans(updatedSpu.getTitleTrans());
        existingSpu.setDescription(updatedSpu.getDescription());
        existingSpu.setName(updatedSpu.getName());
        existingSpu.setNameTrans(updatedSpu.getNameTrans());
        existingSpu.setCategoryId(updatedSpu.getCategoryId());
        existingSpu.setCategoryName(updatedSpu.getCategoryName());
        existingSpu.setCategoryNameTrans(updatedSpu.getCategoryNameTrans());
        existingSpu.setMainImage(updatedSpu.getMainImage());
        existingSpu.setWhiteImage(updatedSpu.getWhiteImage());
        existingSpu.setImages(updatedSpu.getImages());
        existingSpu.setUnit(updatedSpu.getUnit());
        existingSpu.setUnitTrans(updatedSpu.getUnitTrans());
        existingSpu.setMainVideo(updatedSpu.getMainVideo());
        existingSpu.setDetailVideo(updatedSpu.getDetailVideo());
        existingSpu.setAttributeCpvs(updatedSpu.getAttributeCpvs());
        existingSpu.setShippingInfo(updatedSpu.getShippingInfo());
        existingSpu.setSkuShippingDetails(updatedSpu.getSkuShippingDetails());
        existingSpu.setMinOrderQuantity(updatedSpu.getMinOrderQuantity());
        existingSpu.setIsSingleItem(updatedSpu.getIsSingleItem());
        existingSpu.setPdcProductMappingId(updatedSpu.getPdcProductMappingId());
        existingSpu.setSourcePlatformSellerOpenId(updatedSpu.getSourcePlatformSellerOpenId());
        existingSpu.setSourcePlatformSellerName(updatedSpu.getSourcePlatformSellerName());
    }

    /**
     * 记录SPU处理成功信息
     */
    private void recordSpuProcessSuccess(ProductSyncContext context, TzProductSpu spu) {
        addHandlerMetadata(context, "spu_id", spu.getId());
        addHandlerMetadata(context, "spu_title", spu.getTitle());
        addHandlerMetadata(context, "is_single_item", spu.getIsSingleItem());
        addHandlerMetadata(context, "process_time", System.currentTimeMillis());
        addHandlerMetadata(context, "success", true);
    }

    @Override
    public boolean canHandle(ProductSyncContext context) {
        // 验证类型的同步不处理SPU
        return context.getSyncType() != SyncType.VALIDATION;
    }

    @Override
    protected boolean shouldContinueOnFailure() {
        return false; // SPU处理失败不继续
    }

    @Override
    protected boolean supportFallback() {
        return true; // 支持降级处理
    }
}
