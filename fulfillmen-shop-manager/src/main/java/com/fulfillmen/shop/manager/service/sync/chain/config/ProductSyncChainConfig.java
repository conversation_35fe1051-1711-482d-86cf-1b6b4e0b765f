/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.service.sync.chain.config;

import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 产品同步责任链配置
 *
 * <pre>
 * 配置功能：
 * 1. 控制责任链模式的启用/禁用
 * 2. 配置处理器的行为参数
 * 3. 提供性能调优选项
 * 4. 支持动态配置更新
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/07/05
 * @since 2.0.0
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(ProductSyncChainConfig.ProductSyncProperties.class)
public class ProductSyncChainConfig {

    @PostConstruct
    public void init() {
        log.info("产品同步责任链配置初始化完成");
    }

    /**
     * 产品同步配置属性
     */
    @Data
    @ConfigurationProperties(prefix = "product.sync.chain")
    public static class ProductSyncProperties {

        /**
         * 是否启用责任链模式
         * 默认为false，保持向后兼容
         */
        private boolean enabled = false;

        /**
         * 是否启用调试模式
         * 调试模式会输出详细的处理器执行信息
         */
        private boolean debug = false;

        /**
         * 处理器超时时间（毫秒）
         */
        private long handlerTimeoutMs = 5000L;

        /**
         * 整个链条的超时时间（毫秒）
         */
        private long chainTimeoutMs = 30000L;

        /**
         * 是否启用性能监控
         */
        private boolean performanceMonitoring = true;

        /**
         * 是否启用降级处理
         */
        private boolean fallbackEnabled = true;

        /**
         * 数据验证配置
         */
        private ValidationConfig validation = new ValidationConfig();

        /**
         * API调用配置
         */
        private ApiConfig api = new ApiConfig();

        /**
         * 缓存配置
         */
        private CacheConfig cache = new CacheConfig();

        /**
         * 重试配置
         */
        private RetryConfig retry = new RetryConfig();
    }

    /**
     * 数据验证配置
     */
    @Data
    public static class ValidationConfig {

        /**
         * 是否启用严格验证模式
         */
        private boolean strictMode = false;

        /**
         * 平台产品ID最大长度
         */
        private int maxProductIdLength = 20;

        /**
         * 是否允许空的产品标题
         */
        private boolean allowEmptyTitle = false;

        /**
         * 是否允许空的产品图片
         */
        private boolean allowEmptyImages = false;
    }

    /**
     * API调用配置
     */
    @Data
    public static class ApiConfig {

        /**
         * API调用超时时间（毫秒）
         */
        private long timeoutMs = 10000L;

        /**
         * 是否启用API调用缓存
         */
        private boolean cacheEnabled = true;

        /**
         * 缓存过期时间（秒）
         */
        private int cacheExpirySeconds = 300;

        /**
         * 是否在API失败时启用降级
         */
        private boolean fallbackOnFailure = true;

        /**
         * API调用重试次数
         */
        private int maxRetries = 2;

        /**
         * 重试间隔（毫秒）
         */
        private long retryIntervalMs = 1000L;
    }

    /**
     * 缓存配置
     */
    @Data
    public static class CacheConfig {

        /**
         * 是否启用多级缓存
         */
        private boolean multiLevelEnabled = true;

        /**
         * L1缓存大小
         */
        private int l1CacheSize = 1000;

        /**
         * L1缓存过期时间（秒）
         */
        private int l1ExpirySeconds = 300;

        /**
         * L2缓存过期时间（秒）
         */
        private int l2ExpirySeconds = 3600;

        /**
         * 是否启用缓存预热
         */
        private boolean preloadEnabled = false;
    }

    /**
     * 重试配置
     */
    @Data
    public static class RetryConfig {

        /**
         * 是否启用重试机制
         */
        private boolean enabled = true;

        /**
         * 最大重试次数
         */
        private int maxAttempts = 3;

        /**
         * 重试间隔（毫秒）
         */
        private long intervalMs = 1000L;

        /**
         * 重试间隔递增因子
         */
        private double backoffMultiplier = 2.0;

        /**
         * 最大重试间隔（毫秒）
         */
        private long maxIntervalMs = 10000L;

        /**
         * 需要重试的异常类型
         */
        private String[] retryableExceptions = {
            "java.net.SocketTimeoutException",
            "java.net.ConnectException",
            "org.springframework.dao.DataAccessException"
        };
    }

    /**
     * 处理器特定配置
     */
    @Data
    @ConfigurationProperties(prefix = "product.sync.chain.handlers")
    public static class HandlerConfig {

        /**
         * 数据验证处理器配置
         */
        private HandlerSettings dataValidation = new HandlerSettings();

        /**
         * 现有数据检查处理器配置
         */
        private HandlerSettings existingDataCheck = new HandlerSettings();

        /**
         * API数据获取处理器配置
         */
        private HandlerSettings apiDataFetch = new HandlerSettings();

        /**
         * SPU处理器配置
         */
        private HandlerSettings spuProcess = new HandlerSettings();

        /**
         * SKU处理器配置
         */
        private HandlerSettings skuProcess = new HandlerSettings();

        /**
         * 数据完整性处理器配置
         */
        private HandlerSettings dataIntegrity = new HandlerSettings();

        /**
         * 缓存更新处理器配置
         */
        private HandlerSettings cacheUpdate = new HandlerSettings();

        /**
         * 降级处理器配置
         */
        private HandlerSettings fallback = new HandlerSettings();
    }

    /**
     * 单个处理器设置
     */
    @Data
    public static class HandlerSettings {

        /**
         * 是否启用该处理器
         */
        private boolean enabled = true;

        /**
         * 处理器超时时间（毫秒）
         */
        private long timeoutMs = 5000L;

        /**
         * 是否在失败时继续处理
         */
        private boolean continueOnFailure = false;

        /**
         * 是否支持降级处理
         */
        private boolean fallbackSupported = true;

        /**
         * 处理器优先级
         */
        private int priority = 100;

        /**
         * 是否启用性能监控
         */
        private boolean performanceMonitoring = true;
    }

    /**
     * 监控配置
     */
    @Data
    @ConfigurationProperties(prefix = "product.sync.chain.monitoring")
    public static class MonitoringConfig {

        /**
         * 是否启用监控
         */
        private boolean enabled = true;

        /**
         * 是否记录处理器执行时间
         */
        private boolean recordExecutionTime = true;

        /**
         * 是否记录处理器成功率
         */
        private boolean recordSuccessRate = true;

        /**
         * 是否启用慢查询监控
         */
        private boolean slowQueryMonitoring = true;

        /**
         * 慢查询阈值（毫秒）
         */
        private long slowQueryThresholdMs = 3000L;

        /**
         * 是否启用错误率监控
         */
        private boolean errorRateMonitoring = true;

        /**
         * 错误率告警阈值（百分比）
         */
        private double errorRateThreshold = 5.0;

        /**
         * 监控数据保留天数
         */
        private int dataRetentionDays = 7;
    }
}
