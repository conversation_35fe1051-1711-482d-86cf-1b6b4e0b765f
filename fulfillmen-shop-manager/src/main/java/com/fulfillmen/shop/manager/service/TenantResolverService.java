/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.dao.mapper.TenantDomainsMapper;
import com.fulfillmen.shop.domain.entity.TenantDomains;
import com.fulfillmen.starter.cache.redisson.util.RedisUtils;
import jakarta.servlet.http.HttpServletRequest;
import java.time.Duration;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 租户解析服务 统一处理租户ID的获取、验证和解析逻辑
 *
 * <AUTHOR>
 * @date 2025/1/3 14:00
 * @description: 租户解析服务，负责从各种来源获取和验证租户ID
 * @since 1.0.0
 */
@Slf4j
@Service
public class TenantResolverService {

    /**
     * 忽略的子域名（不作为租户标识）
     */
    private static final Set<String> IGNORED_SUBDOMAINS = new HashSet<>(Arrays
        .asList("www", "api", "admin", "static", "assets", "cdn", "mail", "ftp"));

    /**
     * REDIS 分布式锁
     */
    private static final String REDIS_DOMAIN_LOCK = "tenant:domains:lock";
    /**
     * REDIS 缓存租户域名
     */
    private static final String REDIS_DOMAIN = "tenant:domains";

    @Autowired
    private TenantDomainsMapper tenantDomainsMapper;
    @Autowired
    private Environment environment;

    /**
     * 从HTTP请求中解析租户ID
     *
     * @param request HTTP请求
     * @return 租户ID，如果无法解析则返回null
     */
    public String resolveTenantId(HttpServletRequest request) {

        // 1. 开发环境特殊处理
//        if (isDevEnvironment() && isLocalhostRequest(request)) {
//            log.debug("开发环境localhost请求，使用默认租户ID: 10000");
//            return "10000";
//        }

        // 2. 优先从自定义域名解析（精确匹配）
        String tenantIdFromDomain = resolveTenantFromCustomDomain(request);
        if (tenantIdFromDomain != null) {
            // 如果有请求头中的租户ID，验证是否匹配
            String headerTenantId = request.getHeader("X-Tenant-Id");
            if (StringUtils.hasText(headerTenantId) && !headerTenantId.equals(tenantIdFromDomain)) {
                log.warn("请求头租户ID({})与域名解析租户ID({})不匹配", headerTenantId, tenantIdFromDomain);
                // 优先使用域名解析的结果
            }
            return tenantIdFromDomain;
        }

        // 3. 从子域名解析
        String tenantIdFromSubdomain = resolveTenantFromSubdomain(request);
        if (tenantIdFromSubdomain != null) {
            // 如果有请求头中的租户ID，验证是否匹配
            String headerTenantId = request.getHeader("X-Tenant-Id");
            if (StringUtils.hasText(headerTenantId) && !headerTenantId.equals(tenantIdFromSubdomain)) {
                log.warn("请求头租户ID({})与子域名解析租户ID({})不匹配", headerTenantId, tenantIdFromSubdomain);
            }
            return tenantIdFromSubdomain;
        }

        // 4. 从请求头获取
//        String headerTenantId = request.getHeader("X-Tenant-Id");
//        if (StringUtils.hasText(headerTenantId)) {
//            if (isValidTenantId(headerTenantId)) {
//                log.debug("从请求头获取租户ID: {}", headerTenantId);
//                return headerTenantId;
//            } else {
//                log.warn("请求头中的租户ID格式无效: {}", headerTenantId);
//            }
//        }

        // 5. 从请求参数获取
//        String paramTenantId = request.getParameter("tenantId");
//        if (StringUtils.hasText(paramTenantId) && isValidTenantId(paramTenantId)) {
//            log.debug("从请求参数获取租户ID: {}", paramTenantId);
//            return paramTenantId;
//        }

        // 6. 从URL路径解析
//        String pathTenantId = resolveTenantFromPath(request);
//        if (pathTenantId != null) {
//            log.debug("从URL路径获取租户ID: {}", pathTenantId);
//            return pathTenantId;
//        }

        log.debug("未找到有效的租户ID");
        return null;
    }

    /**
     * 从自定义域名解析租户ID 查询 tenant_domains 表获取对应的租户ID
     */
    private String resolveTenantFromCustomDomain(HttpServletRequest request) {
        String serverName = request.getServerName();
        if (!StringUtils.hasText(serverName)) {
            return null;
        }
        // 1. 缓存中获取租户域名
        RMap<String, Long> domains = RedisUtils.getRMap(REDIS_DOMAIN);
        if (!domains.isEmpty()) {
            Long tenantId = domains.get(serverName);
            if (tenantId != null) {
                log.debug("从缓存中获取租户ID: {}", tenantId);
                return String.valueOf(tenantId);
            }
        }
        // 2. 没有缓存则，查询数据库
        try {
            // 分布式锁
            RedisUtils.tryLock(REDIS_DOMAIN_LOCK, 3, 5, TimeUnit.SECONDS);
            // 查询tenant_domains表
            LambdaQueryWrapper<TenantDomains> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TenantDomains::getDomainName, serverName)
                // 只查询已验证的域名
                .eq(TenantDomains::getVerified, true);
            TenantDomains tenantDomain = tenantDomainsMapper.selectOne(queryWrapper);
            if (tenantDomain != null) {
                String tenantId = String.valueOf(tenantDomain.getTenantId());
                log.debug("从自定义域名({})解析到租户ID: {}", serverName, tenantId);
                // 设置增加缓存 并缓存 12h
                domains.put(serverName, tenantDomain.getTenantId());
                domains.expire(Duration.ofHours(12));
                return tenantId;
            }
        } catch (Exception e) {
            log.error("查询自定义域名失败: {}", serverName, e);
        } finally {
            RedisUtils.unlock(REDIS_DOMAIN_LOCK);
        }

        return null;
    }

    /**
     * 从子域名解析租户ID 如 tenant1.fulfillmen.com -> tenant1
     */
    private String resolveTenantFromSubdomain(HttpServletRequest request) {
        String serverName = request.getServerName();
        if (!StringUtils.hasText(serverName) || !serverName.contains(".")) {
            return null;
        }

        String[] parts = serverName.split("\\.");
        // 至少要有 subdomain.domain.com 的格式
        if (parts.length >= 3) {
            String subdomain = parts[0];
            if (!IGNORED_SUBDOMAINS.contains(subdomain.toLowerCase())) {
                // 检查子域名是否为有效的租户标识
                if (isValidTenantId(subdomain)) {
                    log.debug("从子域名({})解析到租户标识: {}", serverName, subdomain);
                    return subdomain;
                }
            }
        }

        return null;
    }

    /**
     * 从URL路径解析租户ID 如 /tenant/10001/api/products -> 10001
     */
    @Deprecated
    private String resolveTenantFromPath(HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        if (StringUtils.hasText(requestURI) && requestURI.startsWith("/tenant/")) {
            String[] pathParts = requestURI.split("/");
            if (pathParts.length >= 3) {
                String tenantId = pathParts[2];
                if (isValidTenantId(tenantId)) {
                    return tenantId;
                }
            }
        }
        return null;
    }

    /**
     * 验证租户ID格式是否正确
     */
    private boolean isValidTenantId(String tenantId) {
        if (!StringUtils.hasText(tenantId)) {
            return false;
        }

        // 长度限制
        if (tenantId.length() > 50) {
            return false;
        }

        try {
            // 检查是否为数字格式的租户ID
            Long.parseLong(tenantId);
            return true;
        } catch (NumberFormatException e) {
            // 如果不是数字，检查是否为合法的字符串格式
            return tenantId.matches("^[a-zA-Z0-9_-]+$");
        }
    }

    /**
     * 检查是否为开发环境
     */
    private boolean isDevEnvironment() {
        String activeProfile = environment.getProperty("spring.profiles.active");
        return "dev".equals(activeProfile) || "development".equals(activeProfile) || "local".equals(activeProfile);
    }

    /**
     * 检查是否为localhost请求
     */
    private boolean isLocalhostRequest(HttpServletRequest request) {
        String serverName = request.getServerName();
        return "localhost".equals(serverName) || "127.0.0.1".equals(serverName) || serverName.endsWith(".local");
    }

}
