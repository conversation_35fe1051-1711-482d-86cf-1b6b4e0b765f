/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.wms.api;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import com.fulfillmen.shop.common.util.SecureUtils;
import com.fulfillmen.shop.manager.support.wms.configure.WmsProperties;
import com.fulfillmen.shop.manager.support.wms.model.ExtUserInfo;
import com.fulfillmen.shop.manager.support.wms.model.WmsAccountInfo;
import com.fulfillmen.shop.manager.support.wms.model.WmsApiResponse;
import com.fulfillmen.shop.manager.support.wms.model.WmsDataDto;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

/**
 *
 *
 * <AUTHOR>
 * @date 2025/6/25
 * @description: todo
 * @since 1.0.0
 */

@ExtendWith(MockitoExtension.class)
@DisplayName("WMS API 单元测试")
class WmsApiTest {

    @Mock
    private WebClient fmWebClient;
    @Mock
    private WebClient.RequestHeadersUriSpec requestHeadersUriSpec;
    @Mock
    private WebClient.RequestHeadersSpec requestHeadersSpec;
    @Mock
    private WebClient.ResponseSpec responseSpec;
    @Mock
    private WmsProperties wmsProperties;

    @InjectMocks
    private WmsApi wmsApi;

    private final String FAKE_AUTH_CODE = "fakeAuthCode123";
    private final String FAKE_ENCRYPTED_INFO = "encrypted-string-of-user-info";
    private final String FAKE_DECRYPTED_JSON = "{\"UserID\":6656,\"Username\":\"billytang\",\"CusCode\":\"10002\",\"ApiKey\":\"some-api-key\",\"AccountMoney\":12345.67,\"Email\":\"<EMAIL>\"}";

    @BeforeEach
    void setUp() {
        // 模拟 WebClient 的链式调用
        when(fmWebClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri(anyString(), anyString())).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
    }

    @Test
    @DisplayName("当API成功返回并解密成功时，应返回正确的WmsAccountInfo")
    void getAccountInfo_Success() {
        // 1. 准备数据
        WmsDataDto wmsDataDto = new WmsDataDto();
        wmsDataDto.setEncryptedUserInfo(FAKE_ENCRYPTED_INFO);

        WmsApiResponse<WmsDataDto> apiResponse = new WmsApiResponse<>();
        apiResponse.setSuccess("true");
        apiResponse.setData(wmsDataDto);

        // 2. Mock 行为
        when(wmsProperties.getBaseUrl()).thenReturn("http://fake-wms.com");
        when(responseSpec.bodyToMono(any(ParameterizedTypeReference.class))).thenReturn(Mono.just(apiResponse));

        try (MockedStatic<SecureUtils> mockedSecureUtils = mockStatic(SecureUtils.class)) {
            mockedSecureUtils.when(() -> SecureUtils.decryptByRsaPrivateKey(eq(FAKE_ENCRYPTED_INFO), anyString()))
                .thenReturn(FAKE_DECRYPTED_JSON);

            // 3. 调用被测试方法
            Optional<WmsAccountInfo> result = wmsApi.getAccountInfo(FAKE_AUTH_CODE);

            // 4. 断言结果
            assertTrue(result.isPresent(), "结果不应为空");
            WmsAccountInfo accountInfo = result.get();
            assertNotNull(accountInfo.getUserinfo(), "用户信息不应为null");

            ExtUserInfo userInfo = accountInfo.getUserinfo();
            assertEquals("10002", userInfo.getCusCode());
            assertEquals("billytang", userInfo.getUsername());
            assertEquals("<EMAIL>", userInfo.getEmail());
        }
    }

    @Test
    @DisplayName("当API返回失败时，应返回空的Optional")
    void getAccountInfo_ApiReturnsFailure() {
        // 1. 准备数据
        WmsApiResponse<WmsDataDto> apiResponse = new WmsApiResponse<>();
        apiResponse.setSuccess("false");
        apiResponse.setMessage("WMS服务器错误");

        // 2. Mock 行为
        when(wmsProperties.getBaseUrl()).thenReturn("http://fake-wms.com");
        when(responseSpec.bodyToMono(any(ParameterizedTypeReference.class))).thenReturn(Mono.just(apiResponse));

        // 3. 调用被测试方法
        Optional<WmsAccountInfo> result = wmsApi.getAccountInfo(FAKE_AUTH_CODE);

        // 4. 断言结果
        assertFalse(result.isPresent(), "当API调用失败时不应有返回结果");
    }

    @Test
    @DisplayName("当解密返回null时，应返回空的Optional")
    void getAccountInfo_DecryptionFails() {
        // 1. 准备数据
        WmsDataDto wmsDataDto = new WmsDataDto();
        wmsDataDto.setEncryptedUserInfo(FAKE_ENCRYPTED_INFO);

        WmsApiResponse<WmsDataDto> apiResponse = new WmsApiResponse<>();
        apiResponse.setSuccess("true");
        apiResponse.setData(wmsDataDto);

        // 2. Mock 行为
        when(wmsProperties.getBaseUrl()).thenReturn("http://fake-wms.com");
        when(responseSpec.bodyToMono(any(ParameterizedTypeReference.class))).thenReturn(Mono.just(apiResponse));

        try (MockedStatic<SecureUtils> mockedSecureUtils = mockStatic(SecureUtils.class)) {
            mockedSecureUtils.when(() -> SecureUtils.decryptByRsaPrivateKey(anyString(), anyString()))
                .thenReturn(null); // 模拟解密失败返回 null

            // 3. 调用被测试方法
            Optional<WmsAccountInfo> result = wmsApi.getAccountInfo(FAKE_AUTH_CODE);

            // 4. 断言
            assertFalse(result.isPresent(), "当解密失败时不应有返回结果");
        }
    }
//
//    @Test
//    @DisplayName("创建采购订单 - 成功")
//    void createPurchaseOrder_Success() {
//        // 1. 准备数据
//        WmsCreateShop createShopRequest = new WmsCreateShop();
//        createShopRequest.setOrder(new WmsOrder()); // a dummy order
//
//        WmsApiResponse<Object> successResponse = new WmsApiResponse<>();
//        successResponse.setSuccess(true);
//        successResponse.setCode(200);
//        successResponse.setMessage("订单创建成功");
//
//        // 2. Mock 行为
//        WebClient.RequestBodyUriSpec requestBodyUriSpec = mock(WebClient.RequestBodyUriSpec.class);
//        WebClient.RequestBodySpec requestBodySpec = mock(WebClient.RequestBodySpec.class);
//        WebClient.RequestHeadersSpec requestHeadersSpecPost = mock(WebClient.RequestHeadersSpec.class);
//        WebClient.ResponseSpec responseSpecPost = mock(WebClient.ResponseSpec.class);
//
//        when(wmsProperties.getBaseUrl()).thenReturn("https://wms.fulfillmen.com");
//        when(fmWebClient.post()).thenReturn(requestBodyUriSpec);
//        when(requestBodyUriSpec.uri(anyString())).thenReturn(requestBodySpec);
//        when(requestBodySpec.bodyValue(any(WmsCreateShop.class))).thenReturn(requestHeadersSpecPost);
//        when(requestHeadersSpecPost.retrieve()).thenReturn(responseSpecPost);
//        when(responseSpecPost.bodyToMono(any(ParameterizedTypeReference.class))).thenReturn(Mono.just(successResponse));
//
//        // 3. 调用被测试方法
//        Optional<WmsApiResponse<Object>> result = wmsApi.createPurchaseOrder(createShopRequest);
//
//        // 4. 断言
//        assertTrue(result.isPresent());
//        assertEquals(200, result.get().getCode());
//        assertTrue(result.get().isSuccess());
//        verify(fmWebClient, times(1)).post();
//    }
//
//    @Test
//    @DisplayName("创建采购订单 - API调用时发生异常")
//    void createPurchaseOrder_ApiThrowsException() {
//        // 1. 准备数据
//        WmsCreateShop createShopRequest = new WmsCreateShop();
//
//        // 2. Mock 行为
//        when(wmsProperties.getBaseUrl()).thenReturn("http://fake-wms.com");
//        when(fmWebClient.post()).thenThrow(new RuntimeException("网络连接超时"));
//
//        // 3. 调用被测试方法
//        Optional<WmsApiResponse<Object>> result = wmsApi.createPurchaseOrder(createShopRequest);
//
//        // 4. 断言
//        assertFalse(result.isPresent(), "当API调用抛出异常时不应有返回结果");
//    }
}
