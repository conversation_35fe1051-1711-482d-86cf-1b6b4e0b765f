/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.service.sync.chain;

import com.fulfillmen.shop.manager.service.sync.chain.core.ProductSyncContext;
import com.fulfillmen.shop.manager.service.sync.chain.core.ProductSyncResult;
import com.fulfillmen.shop.manager.service.sync.chain.enums.SyncType;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 产品同步责任链集成测试
 * 
 * <pre>
 * 测试内容：
 * 1. 验证责任链的基本功能
 * 2. 测试不同同步类型的处理
 * 3. 验证Builder模式的正确性
 * 4. 测试编译时的类型安全
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/07/05
 * @since 2.0.0
 */
@Slf4j
@SpringBootTest
@TestPropertySource(properties = {
    "product.sync.chain.enabled=true",
    "product.sync.chain.debug=true"
})
class ProductSyncChainIntegrationTest {

    @Test
    void testProductSyncContextBuilder() {
        // 测试Builder模式的正确性
        ProductSyncContext context = ProductSyncContext.builder()
            .platformProductId("123456789")
            .syncType(SyncType.STANDARD)
            .forceUpdate(false)
            .build();

        assertNotNull(context);
        assertEquals("123456789", context.getPlatformProductId());
        assertEquals(SyncType.STANDARD, context.getSyncType());
        assertFalse(context.isForceUpdate());
        assertTrue(context.isShouldContinue());
        assertFalse(context.isHasErrors());

        // 测试默认值
        assertNotNull(context.getWarnings());
        assertNotNull(context.getErrors());
        assertNotNull(context.getAttributes());
        assertNotNull(context.getHandlerExecutionLog());
        assertNotNull(context.getRequestStartTime());
    }

    @Test
    void testProductSyncContextMethods() {
        // 测试上下文方法的正确性
        ProductSyncContext context = ProductSyncContext.builder()
            .platformProductId("123456789")
            .build();

        // 测试属性设置和获取
        context.setAttribute("test_key", "test_value");
        assertEquals("test_value", context.getAttribute("test_key"));

        // 测试警告和错误管理
        context.addWarning("测试警告");
        context.addError("测试错误");

        assertEquals(1, context.getWarnings().size());
        assertEquals(1, context.getErrors().size());
        assertTrue(context.isHasErrors());

        // 测试处理器执行日志
        context.logHandlerExecution("TestHandler");
        assertEquals(1, context.getHandlerExecutionLog().size());

        // 测试控制方法
        context.stopProcessing();
        assertFalse(context.isShouldContinue());

        context.markForRollback();
        assertTrue(context.isNeedRollback());

        context.enableFallback();
        assertTrue(context.isUseFallback());
    }

    @Test
    void testSyncTypeEnumMethods() {
        // 测试同步类型枚举的方法
        assertTrue(SyncType.FORCE_RESYNC.shouldForceRefresh());
        assertTrue(SyncType.REPAIR.shouldForceRefresh());
        assertFalse(SyncType.STANDARD.shouldForceRefresh());

        assertTrue(SyncType.STANDARD.allowCache());
        assertFalse(SyncType.FORCE_RESYNC.allowCache());

        assertTrue(SyncType.REPAIR.needFullValidation());
        assertTrue(SyncType.VALIDATION.needFullValidation());
        assertFalse(SyncType.STANDARD.needFullValidation());

        assertTrue(SyncType.STANDARD.supportFallback());
        assertFalse(SyncType.VALIDATION.supportFallback());
    }

    @Test
    void testProductSyncResultBuilder() {
        // 测试结果构建器方法
        ProductSyncResult successResult = ProductSyncResult.success(null);
        assertTrue(successResult.isSuccess());
        assertNull(successResult.getErrorMessage());

        ProductSyncResult failureResult = ProductSyncResult.failure("测试错误");
        assertFalse(failureResult.isSuccess());
        assertEquals("测试错误", failureResult.getErrorMessage());

        ProductSyncResult failureWithCodeResult = ProductSyncResult.failure("ERROR_CODE", "错误消息");
        assertFalse(failureWithCodeResult.isSuccess());
        assertEquals("ERROR_CODE", failureWithCodeResult.getErrorCode());
        assertEquals("错误消息", failureWithCodeResult.getErrorMessage());
    }

    @Test
    void testProductSyncResultFromContext() {
        // 测试从上下文创建结果
        ProductSyncContext context = ProductSyncContext.builder()
            .platformProductId("123456789")
            .build();

        context.addWarning("测试警告");

        ProductSyncResult result = ProductSyncResult.fromContext(context);

        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals("123456789", result.getPlatformProductId());
        assertEquals(1, result.getWarnings().size());
        assertEquals("测试警告", result.getWarnings().get(0));
    }

    @Test
    void testCompilationSuccess() {
        // 这个测试主要是验证代码能够成功编译
        // 如果有编译错误，这个测试就不会运行

        log.info("责任链架构编译成功！");
        log.info("支持的同步类型: {}", java.util.Arrays.toString(SyncType.values()));

        // 验证所有关键类都能正常实例化
        assertDoesNotThrow(() -> {
            ProductSyncContext.builder().build();
            ProductSyncResult.success(null);
            SyncType.fromCode("standard");
        });

        assertTrue(true, "所有核心组件编译和实例化成功");
    }
}
