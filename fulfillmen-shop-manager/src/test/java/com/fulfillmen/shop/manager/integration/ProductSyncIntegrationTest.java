/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.integration;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.Transactional;
import com.fulfillmen.shop.dao.mapper.PdcProductMappingMapper;
import com.fulfillmen.shop.dao.mapper.TzProductSkuMapper;
import com.fulfillmen.shop.dao.mapper.TzProductSpuMapper;
import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.domain.entity.PdcProductMapping;
import com.fulfillmen.shop.domain.entity.TzProductSku;
import com.fulfillmen.shop.domain.entity.TzProductSpu;
import com.fulfillmen.shop.manager.TestConfiguration;
import com.fulfillmen.shop.manager.config.ProductSyncConfig;
import com.fulfillmen.shop.manager.core.repository.PdcProductMappingRepository;
import com.fulfillmen.shop.manager.service.IProductSyncService;
import com.fulfillmen.shop.manager.support.alibaba.IProductManager;
import com.fulfillmen.shop.manager.support.alibaba.IToolsManager;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsDetailResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsDetailResponse.ProductDetail;

/**
 * ProductSyncServiceImpl 与 PdcProductMappingRepositoryImpl 集成测试
 *
 * <pre>
 * 测试覆盖范围：
 * 1. 数据同步流程的完整性
 * 2. DAO 层数据库操作
 * 3. 阿里巴巴 API 接口调用模拟
 * 4. 缓存机制
 * 5. 事务处理
 * 6. 异常处理
 * 7. 数据一致性验证
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/1/3
 * @since 1.0.0
 */
@SpringBootTest(classes = TestConfiguration.class)
@ActiveProfiles("test")
@TestPropertySource(properties = {"fulfillmen.product.sync.auto-sync-enabled=true",
    "fulfillmen.product.sync.batch-size=10", "fulfillmen.product.sync.check-interval=30"})
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@DisplayName("产品同步集成测试")
class ProductSyncIntegrationTest {

    // ==================== 依赖注入 ====================

    @Autowired
    @Qualifier("productSyncServiceImpl")
    private IProductSyncService productSyncService;

    @Autowired
    private PdcProductMappingRepository pdcProductMappingRepository;

    @Autowired
    private TzProductSpuMapper tzProductSpuMapper;

    @Autowired
    private TzProductSkuMapper tzProductSkuMapper;

    @Autowired
    private PdcProductMappingMapper pdcProductMappingMapper;

    @Autowired
    private ProductSyncConfig productSyncConfig;

    // ==================== Mock 依赖 ====================

    @MockBean
    private IProductManager productManager;

    @MockBean
    private IToolsManager toolsManager;

    // ==================== 测试数据常量 ====================

    private static final String TEST_PLATFORM_PRODUCT_ID = "851558761905";
    private static final String TEST_BATCH_PRODUCT_ID_1 = "851828848507";
    private static final String TEST_BATCH_PRODUCT_ID_2 = "882372003002";
    private static final String TEST_SELLER_OPEN_ID = "test-seller-123";
    private static final String TEST_WANGWANG_NICKNAME = "测试旺旺";

    // ==================== 测试数据准备 ====================

    @BeforeEach
    void setUp() {
        // 设置默认 Mock 行为
        setupDefaultMockBehavior();
    }

    private void setupDefaultMockBehavior() {
        // Mock 旺旺昵称获取
        when(toolsManager.getWangWangNickname(anyString()))
            .thenReturn(TEST_WANGWANG_NICKNAME);
    }

    // ==================== 核心同步流程测试 ====================

    @Test
    @Order(1)
    @Transactional
    @Rollback
    @DisplayName("测试单个产品同步流程 - 新产品")
    void testSyncProductByPlatformId_NewProduct() {
        // Given: 准备测试数据
        ProductDetail mockProductDetail = createMockProductDetail(TEST_PLATFORM_PRODUCT_ID, false);
        when(productManager.getProductDetail(Long.valueOf(TEST_PLATFORM_PRODUCT_ID))).thenReturn(mockProductDetail);

        // When: 执行同步
        TzProductDTO result = productSyncService.syncProductByPlatformId(TEST_PLATFORM_PRODUCT_ID);

        // Then: 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getPdcPlatformProductId()).isEqualTo(TEST_PLATFORM_PRODUCT_ID);
        assertThat(result.getTitle()).contains("测试商品标题");

        // 验证数据库中的数据
        verifyDatabaseData(TEST_PLATFORM_PRODUCT_ID, true);

        // 验证 API 调用
        verify(productManager, atLeastOnce()).getProductDetail(Long.valueOf(TEST_PLATFORM_PRODUCT_ID));
    }

    @Test
    @Order(2)
    @Transactional
    @Rollback
    @DisplayName("测试单个产品同步流程 - 已存在产品")
    void testSyncProductByPlatformId_ExistingProduct() {
        // Given: 先创建一个产品
        ProductDetail mockProductDetail = createMockProductDetail(TEST_PLATFORM_PRODUCT_ID, false);
        when(productManager.getProductDetail(Long.valueOf(TEST_PLATFORM_PRODUCT_ID))).thenReturn(mockProductDetail);

        // 先执行一次同步
        productSyncService.syncProductByPlatformId(TEST_PLATFORM_PRODUCT_ID);

        // When: 再次同步相同产品
        TzProductDTO result = productSyncService.syncProductByPlatformId(TEST_PLATFORM_PRODUCT_ID);

        // Then: 验证返回已存在的产品数据
        assertThat(result).isNotNull();
        assertThat(result.getPdcPlatformProductId()).isEqualTo(TEST_PLATFORM_PRODUCT_ID);

        // 验证没有重复创建
        List<TzProductSpu> spuList = tzProductSpuMapper.selectList(null);
        assertThat(spuList).hasSize(1);
    }

    @Test
    @Order(3)
    @Transactional
    @Rollback
    @DisplayName("测试单品 SKU 创建")
    void testSyncProductByPlatformId_SingleItem() {
        // Given: 准备单品数据
        ProductDetail mockProductDetail = createMockProductDetail(TEST_PLATFORM_PRODUCT_ID, true);
        when(productManager.getProductDetail(Long.valueOf(TEST_PLATFORM_PRODUCT_ID))).thenReturn(mockProductDetail);

        // When: 执行同步
        TzProductDTO result = productSyncService.syncProductByPlatformId(TEST_PLATFORM_PRODUCT_ID);

        // Then: 验证单品 SKU
        assertThat(result).isNotNull();
        assertThat(result.getSkuList()).hasSize(1);

        if (result.getSkuList() != null && !result.getSkuList().isEmpty()) {
            assertThat(result.getSkuList().get(0).getPlatformProductId()).isEqualTo(TEST_PLATFORM_PRODUCT_ID);
            assertThat(result.getSkuList().get(0).getPlatformSku()).isEqualTo(TEST_PLATFORM_PRODUCT_ID);
        }
    }

    @Test
    @Order(4)
    @Transactional
    @Rollback
    @DisplayName("测试多规格产品 SKU 创建")
    void testSyncProductByPlatformId_MultipleSkus() {
        // Given: 准备多规格产品数据
        ProductDetail mockProductDetail = createMockProductDetailWithSkus(TEST_PLATFORM_PRODUCT_ID);
        when(productManager.getProductDetail(Long.valueOf(TEST_PLATFORM_PRODUCT_ID))).thenReturn(mockProductDetail);

        // When: 执行同步
        TzProductDTO result = productSyncService.syncProductByPlatformId(TEST_PLATFORM_PRODUCT_ID);

        // Then: 验证多规格 SKU
        assertThat(result).isNotNull();
        assertThat(result.getSkuList()).hasSize(2);

        result.getSkuList().forEach(sku -> {
            assertThat(sku.getPlatformProductId()).isEqualTo(TEST_PLATFORM_PRODUCT_ID);
            assertThat(sku.getPlatformSku()).isNotBlank();
        });
    }

    // ==================== 批量同步测试 ====================

    @Test
    @Order(5)
    @Transactional
    @Rollback
    @DisplayName("测试批量自动同步")
    void testAutoSyncFromPdcMapping() {
        // Given: 准备批量测试数据
        List<String> productIds = Arrays.asList(TEST_BATCH_PRODUCT_ID_1, TEST_BATCH_PRODUCT_ID_2);

        // Mock 批量 API 调用
        when(productManager.getProductDetail(Long.valueOf(TEST_BATCH_PRODUCT_ID_1)))
            .thenReturn(createMockProductDetail(TEST_BATCH_PRODUCT_ID_1, false));
        when(productManager.getProductDetail(Long.valueOf(TEST_BATCH_PRODUCT_ID_2)))
            .thenReturn(createMockProductDetail(TEST_BATCH_PRODUCT_ID_2, true));

        // When: 执行批量同步
        List<TzProductDTO> results = productSyncService.autoSyncFromPdcMapping(productIds);

        // Then: 验证批量结果
        assertThat(results).hasSize(2);
        assertThat(results).extracting(TzProductDTO::getPdcPlatformProductId)
            .containsExactlyInAnyOrder(TEST_BATCH_PRODUCT_ID_1, TEST_BATCH_PRODUCT_ID_2);

        // 验证数据库数据
        List<TzProductSpu> spuList = tzProductSpuMapper.selectList(null);
        assertThat(spuList).hasSize(2);

        // 验证 API 调用次数
        verify(productManager, atLeastOnce()).getProductDetail(anyLong());
    }

    // ==================== 缓存机制测试 ====================

    @Test
    @Order(6)
    @Transactional
    @Rollback
    @DisplayName("测试产品详情缓存机制")
    void testProductDetailCache() {
        // Given: 准备测试数据
        ProductDetail mockProductDetail = createMockProductDetail(TEST_PLATFORM_PRODUCT_ID, false);
        when(productManager.getProductDetail(Long.valueOf(TEST_PLATFORM_PRODUCT_ID))).thenReturn(mockProductDetail);

        // When: 第一次获取产品详情
        AlibabaProductDetailDTO firstResult = pdcProductMappingRepository.getProductDetailWithCache(Long
            .valueOf(TEST_PLATFORM_PRODUCT_ID), false);

        // When: 第二次获取产品详情（应该从缓存获取）
        AlibabaProductDetailDTO secondResult = pdcProductMappingRepository.getProductDetailWithCache(Long
            .valueOf(TEST_PLATFORM_PRODUCT_ID), false);

        // Then: 验证缓存效果
        assertThat(firstResult).isNotNull();
        assertThat(secondResult).isNotNull();
        assertThat(firstResult.getPlatformProductId()).isEqualTo(secondResult.getPlatformProductId());

        // 验证 API 调用
        verify(productManager, atLeastOnce()).getProductDetail(Long.valueOf(TEST_PLATFORM_PRODUCT_ID));
    }

    @Test
    @Order(7)
    @Transactional
    @Rollback
    @DisplayName("测试强制刷新缓存")
    void testForceRefreshCache() {
        // Given: 准备测试数据
        ProductDetail mockProductDetail = createMockProductDetail(TEST_PLATFORM_PRODUCT_ID, false);
        when(productManager.getProductDetail(Long.valueOf(TEST_PLATFORM_PRODUCT_ID))).thenReturn(mockProductDetail);

        // 先获取一次产品详情
        pdcProductMappingRepository.getProductDetailWithCache(Long.valueOf(TEST_PLATFORM_PRODUCT_ID), false);

        // When: 强制刷新缓存
        AlibabaProductDetailDTO refreshResult = pdcProductMappingRepository.getProductDetailWithCache(Long
            .valueOf(TEST_PLATFORM_PRODUCT_ID), true);

        // Then: 验证强制刷新效果
        assertThat(refreshResult).isNotNull();

        // 验证 API 调用了多次
        verify(productManager, atLeast(1)).getProductDetail(Long.valueOf(TEST_PLATFORM_PRODUCT_ID));
    }

    // ==================== 异常处理测试 ====================

    @Test
    @Order(8)
    @Transactional
    @Rollback
    @DisplayName("测试 API 调用异常处理")
    void testApiCallException() {
        // Given: Mock API 异常
        when(productManager.getProductDetail(Long.valueOf(TEST_PLATFORM_PRODUCT_ID)))
            .thenThrow(new RuntimeException("API 调用失败"));

        // When & Then: 验证异常处理
        assertThatThrownBy(() -> productSyncService.syncProductByPlatformId(TEST_PLATFORM_PRODUCT_ID))
            .isInstanceOf(Exception.class);

        // 验证数据库没有脏数据
        List<TzProductSpu> spuList = tzProductSpuMapper.selectList(null);
        assertThat(spuList).isEmpty();
    }

    @Test
    @Order(9)
    @Transactional
    @Rollback
    @DisplayName("测试产品不存在的情况")
    void testProductNotFound() {
        // Given: Mock 产品不存在
        when(productManager.getProductDetail(Long.valueOf(TEST_PLATFORM_PRODUCT_ID)))
            .thenReturn(null);

        // When: 执行同步
        TzProductDTO result = productSyncService.syncProductByPlatformId(TEST_PLATFORM_PRODUCT_ID);

        // Then: 验证结果为 null
        assertThat(result).isNull();

        // 验证数据库没有数据
        List<TzProductSpu> spuList = tzProductSpuMapper.selectList(null);
        assertThat(spuList).isEmpty();
    }

    // ==================== 数据一致性测试 ====================

    @Test
    @Order(10)
    @Transactional
    @Rollback
    @DisplayName("测试数据同步的一致性")
    void testDataConsistency() {
        // Given: 准备测试数据
        ProductDetail mockProductDetail = createMockProductDetail(TEST_PLATFORM_PRODUCT_ID, false);
        when(productManager.getProductDetail(Long.valueOf(TEST_PLATFORM_PRODUCT_ID))).thenReturn(mockProductDetail);

        // When: 执行同步
        TzProductDTO result = productSyncService.syncProductByPlatformId(TEST_PLATFORM_PRODUCT_ID);

        // Then: 验证数据一致性
        assertThat(result).isNotNull();

        // 验证 PdcProductMapping
        List<PdcProductMapping> mappings = pdcProductMappingMapper.selectList(null);
        assertThat(mappings).isNotEmpty();
        PdcProductMapping mapping = mappings.get(0);
        assertThat(mapping.getPlatformProductId()).isEqualTo(TEST_PLATFORM_PRODUCT_ID);

        // 验证 TzProductSpu
        List<TzProductSpu> spuList = tzProductSpuMapper.selectList(null);
        assertThat(spuList).isNotEmpty();
        TzProductSpu spu = spuList.get(0);
        assertThat(spu.getPdcPlatformProductId()).isEqualTo(TEST_PLATFORM_PRODUCT_ID);

        // 验证 TzProductSku
        List<TzProductSku> skuList = tzProductSkuMapper.selectList(null);
        assertThat(skuList).hasSize(result.getSkuList().size());
    }

    // ==================== 性能测试 ====================

    @Test
    @Order(11)
    @Transactional
    @Rollback
    @DisplayName("测试批量同步性能")
    void testBatchSyncPerformance() {
        // Given: 准备大量测试数据
        List<String> productIds = new ArrayList<>();
        for (int i = 1; i <= 5; i++) {
            String productId = "85155876190" + i;
            productIds.add(productId);

            // Mock API 调用
            when(productManager.getProductDetail(Long.valueOf(productId)))
                .thenReturn(createMockProductDetail(productId, i % 2 == 0));
        }

        // When: 执行批量同步并测量时间
        long startTime = System.currentTimeMillis();
        List<TzProductDTO> results = productSyncService.autoSyncFromPdcMapping(productIds);
        long endTime = System.currentTimeMillis();

        // Then: 验证性能
        assertThat(results).hasSize(5);
        assertThat(endTime - startTime).isLessThan(10000L); // 10秒内完成

        // 验证数据完整性
        List<TzProductSpu> spuList = tzProductSpuMapper.selectList(null);
        assertThat(spuList).hasSize(5);
    }

    // ==================== 获取或同步产品测试 ====================

    @Test
    @Order(12)
    @Transactional
    @Rollback
    @DisplayName("测试获取或同步产品 - 产品不存在时自动同步")
    void testGetOrSyncProductByPlatformId_AutoSync() {
        // Given: 准备测试数据
        ProductDetail mockProductDetail = createMockProductDetail(TEST_PLATFORM_PRODUCT_ID, false);
        when(productManager.getProductDetail(Long.valueOf(TEST_PLATFORM_PRODUCT_ID))).thenReturn(mockProductDetail);

        // When: 使用 getOrSyncProductByPlatformId
        TzProductDTO result = productSyncService.getOrSyncProductByPlatformId(TEST_PLATFORM_PRODUCT_ID);

        // Then: 验证自动同步效果
        assertThat(result).isNotNull();
        assertThat(result.getPdcPlatformProductId()).isEqualTo(TEST_PLATFORM_PRODUCT_ID);

        // 验证数据已同步到数据库
        List<TzProductSpu> spuList = tzProductSpuMapper.selectList(null);
        assertThat(spuList).hasSize(1);
    }

    // ==================== 工具方法 ====================

    /**
     * 创建 Mock 产品详情数据
     */
    private ProductDetail createMockProductDetail(String platformProductId, boolean isSingleItem) {
        ProductDetail productDetail = new ProductDetail();
        productDetail.setOfferId(Long.valueOf(platformProductId));
        productDetail.setSubject("测试商品标题 - " + platformProductId);
        productDetail.setSubjectTrans("Test Product Title - " + platformProductId);
        productDetail.setDescription("测试商品描述");
        productDetail.setSellerOpenId(TEST_SELLER_OPEN_ID);

        // 设置分类信息
        productDetail.setCategoryId(12345L);
        productDetail.setCategoryName("测试分类");
        productDetail.setMinOrderQuantity(1);

        // 设置图片信息
        GoodsDetailResponse.ProductImage productImage = new GoodsDetailResponse.ProductImage();
        productImage.setImages(Arrays.asList("https://test.com/1.jpg", "https://test.com/2.jpg"));
        productDetail.setProductImage(productImage);

        // 设置销售信息
        GoodsDetailResponse.ProductSaleInfo saleInfo = new GoodsDetailResponse.ProductSaleInfo();
        saleInfo.setAmountOnSale(1000);

        // 设置单位信息
        GoodsDetailResponse.UnitInfo unitInfo = new GoodsDetailResponse.UnitInfo();
        unitInfo.setUnit("件");
        unitInfo.setTransUnit("piece");
        saleInfo.setUnitInfo(unitInfo);

        productDetail.setProductSaleInfo(saleInfo);

        // 根据是否单品创建SKU信息
        if (!isSingleItem) {
            List<GoodsDetailResponse.ProductSkuInfo> skuInfos = new ArrayList<>();
            // 这里会在createMockProductDetailWithSkus方法中设置
            productDetail.setProductSkuInfos(skuInfos);
        }

        return productDetail;
    }

    /**
     * 创建带多个 SKU 的 Mock 产品详情数据
     */
    private ProductDetail createMockProductDetailWithSkus(String platformProductId) {
        ProductDetail productDetail = createMockProductDetail(platformProductId, false);

        // 添加 SKU 信息
        List<GoodsDetailResponse.ProductSkuInfo> skuList = new ArrayList<>();

        GoodsDetailResponse.ProductSkuInfo sku1 = new GoodsDetailResponse.ProductSkuInfo();
        sku1.setSkuId(Long.valueOf("1001"));
        sku1.setPrice("89.99");
        sku1.setConsignPrice("79.99");
        sku1.setAmountOnSale(500);
        skuList.add(sku1);

        GoodsDetailResponse.ProductSkuInfo sku2 = new GoodsDetailResponse.ProductSkuInfo();
        sku2.setSkuId(Long.valueOf("1002"));
        sku2.setPrice("99.99");
        sku2.setConsignPrice("89.99");
        sku2.setAmountOnSale(300);
        skuList.add(sku2);

        productDetail.setProductSkuInfos(skuList);
        return productDetail;
    }

    /**
     * 验证数据库中的数据
     */
    private void verifyDatabaseData(String platformProductId, boolean shouldExist) {
        if (shouldExist) {
            // 验证 PdcProductMapping
            List<PdcProductMapping> mappings = pdcProductMappingMapper.selectList(null);
            assertThat(mappings).isNotEmpty();

            // 验证 TzProductSpu
            List<TzProductSpu> spuList = tzProductSpuMapper.selectList(null);
            assertThat(spuList).isNotEmpty();

            // 验证 TzProductSku
            List<TzProductSku> skuList = tzProductSkuMapper.selectList(null);
            assertThat(skuList).isNotEmpty();
        } else {
            // 验证数据不存在
            List<PdcProductMapping> mappings = pdcProductMappingMapper.selectList(null);
            assertThat(mappings).isEmpty();

            List<TzProductSpu> spuList = tzProductSpuMapper.selectList(null);
            assertThat(spuList).isEmpty();

            List<TzProductSku> skuList = tzProductSkuMapper.selectList(null);
            assertThat(skuList).isEmpty();
        }
    }
}
