/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.strategy;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import java.time.LocalDateTime;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.TestPropertySource;
import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.entity.PdcProductMapping;
import com.fulfillmen.shop.domain.enums.ProductSyncStrategyEnum;
import com.fulfillmen.shop.manager.core.repository.PdcProductMappingRepository;
import com.fulfillmen.shop.manager.strategy.impl.AutoSyncStrategy;
import com.fulfillmen.shop.manager.strategy.impl.DisabledSyncStrategy;
import com.fulfillmen.shop.manager.strategy.impl.ManualSyncStrategy;

/**
 * 同步策略集成测试
 *
 * <AUTHOR>
 * @date 2025/01/03
 * @since 1.0.0
 */
@SpringBootTest
@TestPropertySource(properties = {"fulfillmen.product.sync.auto-sync-enabled=true",
    "fulfillmen.product.sync.sync-strategy.default-strategy=AUTO"})
@DisplayName("产品同步策略集成测试")
class SyncStrategyIntegrationTest {

    @MockBean
    private PdcProductMappingRepository pdcProductMappingRepository;

    @MockBean
    private AutoSyncStrategy autoSyncStrategy;

    @MockBean
    private ManualSyncStrategy manualSyncStrategy;

    @MockBean
    private DisabledSyncStrategy disabledSyncStrategy;

    private SyncStrategyFactory syncStrategyFactory;

    @BeforeEach
    void setUp() {
        syncStrategyFactory = new SyncStrategyFactory(autoSyncStrategy, manualSyncStrategy, disabledSyncStrategy);
        syncStrategyFactory.init();
    }

    @Test
    @DisplayName("自动同步策略 - 应该同步过期数据")
    void autoSyncStrategy_shouldSyncExpiredData() {
        // Given
        String platformProductId = "123456789";
        PdcProductMapping expiredMapping = createExpiredMapping(platformProductId);

        when(autoSyncStrategy.shouldSync(platformProductId, expiredMapping)).thenReturn(true);
        when(autoSyncStrategy.executeSync(platformProductId, false))
            .thenReturn(createMockProductDTO(platformProductId));

        // When
        ISyncStrategy strategy = syncStrategyFactory.getStrategy(ProductSyncStrategyEnum.AUTO);
        boolean shouldSync = strategy.shouldSync(platformProductId, expiredMapping);
        TzProductDTO result = strategy.executeSync(platformProductId, false);

        // Then
        assertThat(shouldSync).isTrue();
        assertThat(result).isNotNull();
        assertThat(result.getPdcPlatformProductId()).isEqualTo(platformProductId);
    }

    @Test
    @DisplayName("手动同步策略 - 始终执行同步")
    void manualSyncStrategy_shouldAlwaysSync() {
        // Given
        String platformProductId = "123456789";
        PdcProductMapping freshMapping = createFreshMapping(platformProductId);

        when(manualSyncStrategy.shouldSync(platformProductId, freshMapping)).thenReturn(true);
        when(manualSyncStrategy.executeSync(platformProductId, true))
            .thenReturn(createMockProductDTO(platformProductId));

        // When
        ISyncStrategy strategy = syncStrategyFactory.getStrategy(ProductSyncStrategyEnum.MANUAL);
        boolean shouldSync = strategy.shouldSync(platformProductId, freshMapping);
        TzProductDTO result = strategy.executeSync(platformProductId, true);

        // Then
        assertThat(shouldSync).isTrue();
        assertThat(result).isNotNull();
    }

    @Test
    @DisplayName("禁用同步策略 - 不执行同步")
    void disabledSyncStrategy_shouldNeverSync() {
        // Given
        String platformProductId = "123456789";
        PdcProductMapping anyMapping = createExpiredMapping(platformProductId);

        when(disabledSyncStrategy.shouldSync(platformProductId, anyMapping)).thenReturn(false);
        when(disabledSyncStrategy.executeSync(platformProductId, false)).thenReturn(null);

        // When
        ISyncStrategy strategy = syncStrategyFactory.getStrategy(ProductSyncStrategyEnum.DISABLED);
        boolean shouldSync = strategy.shouldSync(platformProductId, anyMapping);
        TzProductDTO result = strategy.executeSync(platformProductId, false);

        // Then
        assertThat(shouldSync).isFalse();
        assertThat(result).isNull();
    }

    @Test
    @DisplayName("批量同步测试")
    void batchSyncTest() {
        // Given
        List<String> platformProductIds = List.of("123", "456", "789");
        List<TzProductDTO> mockResults = platformProductIds.stream().map(this::createMockProductDTO).toList();

        when(autoSyncStrategy.batchSync(platformProductIds)).thenReturn(mockResults);

        // When
        ISyncStrategy strategy = syncStrategyFactory.getStrategy(ProductSyncStrategyEnum.AUTO);
        List<TzProductDTO> results = strategy.batchSync(platformProductIds);

        // Then
        assertThat(results).hasSize(3);
        assertThat(results).extracting(TzProductDTO::getPdcPlatformProductId)
            .containsExactlyInAnyOrder("123", "456", "789");
    }

    @Test
    @DisplayName("策略工厂测试 - 获取所有策略")
    void strategyFactory_shouldReturnAllStrategies() {
        // When
        var allStrategies = syncStrategyFactory.getAllStrategies();

        // Then
        assertThat(allStrategies).hasSize(3);
        assertThat(allStrategies)
            .containsKeys(ProductSyncStrategyEnum.AUTO, ProductSyncStrategyEnum.MANUAL, ProductSyncStrategyEnum.DISABLED);
    }

    private PdcProductMapping createExpiredMapping(String platformProductId) {
        return PdcProductMapping.builder()
            .platformProductId(platformProductId)
            .gmtModified(LocalDateTime.now().minusDays(5)) // 5天前，过期
            .build();
    }

    private PdcProductMapping createFreshMapping(String platformProductId) {
        return PdcProductMapping.builder()
            .platformProductId(platformProductId)
            .gmtModified(LocalDateTime.now().minusHours(1)) // 1小时前，新鲜
            .build();
    }

    private TzProductDTO createMockProductDTO(String platformProductId) {
        return TzProductDTO.builder()
            .pdcPlatformProductId(platformProductId)
            .title("Mock Product " + platformProductId)
            .titleTrans("Mock Product Translation " + platformProductId)
            .build();
    }
}
