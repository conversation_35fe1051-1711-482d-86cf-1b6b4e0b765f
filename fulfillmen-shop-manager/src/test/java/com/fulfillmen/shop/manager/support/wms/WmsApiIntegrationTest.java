/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.wms;

import com.fulfillmen.shop.dao.mapper.TzUserMapper;
import com.fulfillmen.shop.domain.entity.enums.PayTypeEnum;
import com.fulfillmen.shop.manager.support.wms.api.WmsApi;
import com.fulfillmen.shop.manager.support.wms.configure.WmsProperties;
import com.fulfillmen.shop.manager.support.wms.model.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import com.fulfillmen.shop.manager.event.ProductSyncEventListener;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * WMS API 集成测试
 * <p>
 * 用于通过构造真实数据结构，真实地调用WMS外部接口，以验证接口的连通性和请求的正确性。
 * </p>
 */
@Slf4j
@SpringBootTest
// @ActiveProfiles("dev") // 不再需要，因为我们模拟了数据库依赖
class WmsApiIntegrationTest {

    @Autowired
    private WmsApi wmsApi;

    @Autowired
    private WmsProperties wmsProperties;

    // 不再从数据库获取，因此移除
    // @Autowired
    // private TzUserMapper userMapper;

    // 使用 @MockBean "欺骗" Spring，让它提供一个假的实例，
    // 从而切断对数据库的依赖链。
    @MockBean
    private ProductSyncEventListener productSyncEventListener;
    @MockBean
    private TzUserMapper userMapper;

    @Test
    @DisplayName("【集成测试】使用真实数据结构调用WMS创建采购订单接口")
    void createPurchaseOrder_WithRealDataStructure_ShouldMakeRealApiCall() {
        // --- 0. 调试：打印实际使用的配置 ---
        log.info("当前 WMS 配置 - baseUrl: {}", wmsProperties.getBaseUrl());
        log.info("预期的完整 URL: {}/Alibaba/OpenApi/1688ShopOrderCreate.ashx", wmsProperties.getBaseUrl());

        // --- 1. 构建：使用修正后的DTO构建请求 ---
        WmsCreateOrder request = buildRealWmsCreateOrderRequest();
        log.info("构造的WMS请求: {}", request);

        // --- 2. 执行：真实调用WMS API接口 ---
        Optional<WmsApiResponse<Object>> responseOptional = wmsApi.createPurchaseOrder(request);

        // --- 3. 断言：验证来自WMS服务器的真实响应 ---
        if (responseOptional.isEmpty()) {
            log.warn("WMS API 未返回任何响应，可能是网络问题或服务不可用");
            // 对于集成测试，如果外部服务不可用，我们可以选择跳过测试而不是失败
            // 这里我们记录警告但不让测试失败，因为这可能是环境问题
            org.junit.jupiter.api.Assumptions.assumeTrue(false, "WMS API 服务当前不可用，跳过集成测试");
            return;
        }

        WmsApiResponse<Object> response = responseOptional.get();
        log.info("从WMS服务器收到的真实响应: {}", response);

        // 验证基本响应状态
        assertTrue(response.isSuccess(), "调用WMS API失败，返回消息: " + response.getMessage());

    }

    /**
     * 构建一个精确匹配WMS API真实JSON结构的请求对象
     */
    private WmsCreateOrder buildRealWmsCreateOrderRequest() {
        // --- 核心认证信息 ---
        String apiKey = "MZL68790A5B3C2D1E9F8G7H6I5J4K3L2";
        long timestamp = Instant.now().getEpochSecond();

        // --- 构建订单详情 ---
        WmsOrderDetails orderDetail = WmsOrderDetails.builder()
            .skuId("5763334258678")
            .enName("Puzzle Piece Carpet Bedroom Full Cover Home Use Square Block Fluffy Surface Eva Foam Floor Mat Tatami Mat")
            .cnName("拼接地毯卧室满铺家用方块拼图地垫毛绒面eva泡沫地板垫榻榻米垫")
            .weight(BigDecimal.ZERO)
            .quantity(2)
            .variantId(String.valueOf(729013576713L))
            .productId(String.valueOf(************L))
            .unitPrice(new BigDecimal("1.7"))
            .originUnitPrice(new BigDecimal("1.7"))
            .finalUnitPrice(new BigDecimal("1.7"))
            .subTotal(new BigDecimal("3.4"))
            .originSubTotalAmount(new BigDecimal("3.4"))
            .finalSubTotalAmount(new BigDecimal("3.4"))
            .imageUrl("https://cbu01.alicdn.com/img/ibank/O1CN01xUvnH72NMF3WVRzVp_!!*************-0-cib.jpg")
            .skuAttrib("颜色:深灰色;尺寸:30x30x1厘米【送边条】;")
            .skuAttribEn("Color:Dark gray;Dimensions:30x 30x 1cm \"send edge strip];")
            .build();

        // --- 构建订单主体 ---
        WmsOrder order = WmsOrder.builder()
            .purchaseNo("C100022025080216494000001")
            .gffCustomerId(6656L)
            .orderId(3021517159853908000L)
            .sellerOpenId("BBBa6yt-gVTXcYenT1_XsZwwg")
            .storeId("536")
            .createUser("10002")
            .createTime("2025-01-27T14:30:00.000Z")
            .remark("naya.shop.com")
            .platform("1688")
            .payType(PayTypeEnum.BALANCE)
            .outTradeNo("TXN" + System.currentTimeMillis())
            .shippingFee(new BigDecimal("5.00"))
            .originalShippingFee(new BigDecimal("5.00"))
            .finalShoppingFee(new BigDecimal("5.00"))
            .serviceFee(BigDecimal.ZERO)
            .total(new BigDecimal("8.4"))
            .productOriginalTotalAmount(new BigDecimal("3.4"))
            .productSalesTotalAmount(new BigDecimal("3.4"))
            .productFinalTotalAmount(new BigDecimal("3.4"))
            .originalTotalPrice(new BigDecimal("8.4"))
            .discount(BigDecimal.ZERO)
            .plusDiscount(BigDecimal.ZERO)
            .couponDiscount(BigDecimal.ZERO)
            .totalAmount(new BigDecimal("8.652"))
            .status(1)
            .isRequestQuote(false)
            .link("https://detail.1688.com/offer/************.html")
            .platformStatus("WAIT_SELLER_SEND_GOODS")
            .platformRemark("买家已付款，等待卖家发货")
            .orderDetails(Collections.singletonList(orderDetail))
            .build();

        // --- 构建最终请求体 ---
        WmsCreateOrder request = new WmsCreateOrder();
        request.setCustomerCode(String.valueOf(10002L));
        request.setAccessToken("666");
        request.setApiKey(apiKey);
        request.setTimestamp(timestamp);
        request.setSignature("4646df4sg2s1564");
        request.setOrders(Collections.singletonList(order));

        return request;
    }

    @Test
    @DisplayName("【真实数据测试】使用完全匹配的WMS数据格式")
    void testWmsApiWithExactRealFormat() {
        log.info("开始测试完全匹配的 WMS 数据格式");

        // 构建完全匹配真实格式的订单详情
        WmsOrderDetails orderDetail = WmsOrderDetails.builder()
            .skuId("5763334258678")
            .enName("Puzzle Piece Carpet Bedroom Full Cover Home Use Square Block Fluffy Surface Eva Foam Floor Mat Tatami Mat")
            .cnName("拼接地毯卧室满铺家用方块拼图地垫毛绒面eva泡沫地板垫榻榻米垫")
            .weight(new BigDecimal("0.35"))
            .quantity(2)
            .variantId("dd6b29dc1e664173f13f15cced77c261")
            .productId("************")
            .unitPrice(new BigDecimal("1.7"))
            .originUnitPrice(new BigDecimal("1.7"))
            .finalUnitPrice(new BigDecimal("1.7"))
            .subTotal(new BigDecimal("3.4"))
            .originSubTotalAmount(new BigDecimal("3.4"))
            .finalSubTotalAmount(new BigDecimal("3.4"))
            .imageUrl("https://cbu01.alicdn.com/img/ibank/O1CN01xUvnH72NMF3WVRzVp_!!*************-0-cib.jpg")
            .skuAttrib("颜色:深灰色;尺寸:30x30x1厘米【送边条】;")
            .skuAttribEn("Color:Dark gray;Dimensions:30x 30x 1cm \"send edge strip];")
            .build();

        // 构建完全匹配真实格式的订单
        WmsOrder order = WmsOrder.builder()
            .purchaseNo("C100022025070211194000001")
            .gffCustomerId(6656L)
            .orderId(3021517159853907000L)
            .sellerOpenId("BBBa6yt-gVTXcYenT1_XsZwwg")
            .storeId("536")
            .createUser("10002")
            .createTime("2025-01-27T14:30:00.000Z")
            .remark("naya.shop.com")
            .platform("1688")
            .payType(PayTypeEnum.BALANCE) // payType: 0
            .outTradeNo("TXN202501270001")
            .payUrl("")
            .payBody("")
            .platformPayType("alipay")
            .shippingFee(new BigDecimal("5"))
            .originalShippingFee(new BigDecimal("5"))
            .finalShoppingFee(new BigDecimal("5"))
            .serviceFee(new BigDecimal("0.252"))
            .total(new BigDecimal("8.4"))
            .productOriginalTotalAmount(new BigDecimal("3.4"))
            .productSalesTotalAmount(new BigDecimal("3.4"))
            .productFinalTotalAmount(new BigDecimal("3.4"))
            .originalTotalPrice(new BigDecimal("8.652"))
            .discount(new BigDecimal("0"))
            .plusDiscount(new BigDecimal("0"))
            .couponDiscount(new BigDecimal("0"))
            .totalAmount(new BigDecimal("8.652"))
            .status(0)
            .isRequestQuote(false)
            .paymentTime("2025-01-27T14:35:00.000Z")
            .shippingTime("0001-01-01T00:00:00.000Z")
            .completeTime("0001-01-01T00:00:00.000Z")
            .link("https://detail.1688.com/offer/************.html")
            .trackingNo("")
            .platformStatus("WAIT_SELLER_SEND_GOODS")
            .platformRemark("买家已付款，等待卖家发货")
            .orderDetails(Collections.singletonList(orderDetail))
            .build();

        // 构建完全匹配真实格式的请求
        WmsCreateOrder request = new WmsCreateOrder();
        request.setCustomerCode("10002");
        request.setAccessToken("666");
        request.setApiKey("MZL68790A5B3C2D1E9F8G7H6I5J4K3L2");
        request.setTimestamp(1743573590L);
        request.setSignature("306f5eb6275c0a19cb84080781f6cd15bee22f6fff006e1fef12874045c43e90");
        request.setOrders(Collections.singletonList(order));

        log.info("真实格式测试请求: {}", request);

        // 调用API
        Optional<WmsApiResponse<Object>> responseOptional = wmsApi.createPurchaseOrder(request);

        assertTrue(responseOptional.isPresent(), "应该收到响应");

        WmsApiResponse<Object> response = responseOptional.get();
        log.info("真实格式测试响应: {}", response);

        // 详细分析响应
        log.info("响应成功状态: {}", response.isSuccess());

    }

    @Test
    @DisplayName("【调试测试】使用标准化数据格式测试WMS API")
    void testWmsApiWithStandardizedData() {
        log.info("开始测试 WMS API 标准化数据格式");

        // 构建标准化的订单详情
        WmsOrderDetails orderDetail = WmsOrderDetails.builder()
            .skuId("TEST001")
            .enName("Test Product")
            .cnName("测试商品")
            .weight(new BigDecimal("0.5"))
            .quantity(1)
            .variantId(String.valueOf(100001L))
            .productId(String.valueOf(200001L))
            .unitPrice(new BigDecimal("10.00"))
            .originUnitPrice(new BigDecimal("10.00"))
            .finalUnitPrice(new BigDecimal("10.00"))
            .subTotal(new BigDecimal("10.00"))
            .originSubTotalAmount(new BigDecimal("10.00"))
            .finalSubTotalAmount(new BigDecimal("10.00"))
            .imageUrl("https://example.com/test.jpg")
            .skuAttrib("颜色:蓝色")
            .skuAttribEn("Color:Blue")
            .build();

        // 构建标准化的订单
        WmsOrder order = WmsOrder.builder()
            .purchaseNo("C100022025070213594000001")
            .gffCustomerId(10002L)
            .orderId(System.currentTimeMillis())
            .sellerOpenId("SELLER001")
            .storeId("536")
            .createUser("10002")
            .createTime("2025-07-02T10:00:00.000Z")
            .remark("标准测试订单")
            .platform("1688")
            .payType(PayTypeEnum.ALIPAY)
            .outTradeNo("TXN202507020001")
            .shippingFee(new BigDecimal("5.00"))
            .originalShippingFee(new BigDecimal("5.00"))
            .finalShoppingFee(new BigDecimal("5.00"))
            .serviceFee(new BigDecimal("1.50"))
            .total(new BigDecimal("16.50"))
            .productOriginalTotalAmount(new BigDecimal("10.00"))
            .productSalesTotalAmount(new BigDecimal("10.00"))
            .productFinalTotalAmount(new BigDecimal("10.00"))
            .originalTotalPrice(new BigDecimal("15.00"))
            .discount(new BigDecimal("0.00"))
            .plusDiscount(new BigDecimal("0.00"))
            .couponDiscount(new BigDecimal("0.00"))
            .totalAmount(new BigDecimal("16.50"))
            .status(1)
            .isRequestQuote(false)
            .paymentTime("2025-07-2T10:35:00.000Z")
            .shippingTime("0001-01-01T00:00:00.000Z")
            .completeTime("0001-01-01T00:00:00.000Z")
            .link("https://detail.1688.com/offer/test.html")
            .platformStatus("WAIT_SELLER_SEND_GOODS")
            .platformRemark("等待发货")
            .orderDetails(Collections.singletonList(orderDetail))
            .build();

        // 构建请求
        WmsCreateOrder request = new WmsCreateOrder();
        request.setCustomerCode(String.valueOf(10002L));
        request.setAccessToken("666");
        request.setApiKey("MZL68790A5B3C2D1E9F8G7H6I5J4K3L2");
        request.setTimestamp(System.currentTimeMillis() / 1000);
        request.setSignature("test-signature");
        request.setOrders(Collections.singletonList(order));

        log.info("标准化测试请求: {}", request);

        // 调用API
        Optional<WmsApiResponse<Object>> responseOptional = wmsApi.createPurchaseOrder(request);

        assertTrue(responseOptional.isPresent(), "应该收到响应");

        WmsApiResponse<Object> response = responseOptional.get();
        log.info("标准化测试响应: {}", response);

        // 详细分析响应
        log.info("响应成功状态: {}", response.isSuccess());
    }

    @Test
    @DisplayName("【调试测试】使用最简化数据调试WMS API格式要求")
    void debugWmsApiFormat_WithMinimalData() {
        log.info("开始调试 WMS API 格式要求");

        // 构建最简化的订单详情
        WmsOrderDetails orderDetail = WmsOrderDetails.builder()
            .skuId("TEST_SKU_001")
            .enName("Test Product")
            .cnName("测试商品")
            .weight(new BigDecimal("0.1"))
            .quantity(1)
            .variantId(String.valueOf(123456L))
            .productId(String.valueOf(789012L))
            .unitPrice(new BigDecimal("10.00"))
            .originUnitPrice(new BigDecimal("10.00"))
            .finalUnitPrice(new BigDecimal("10.00"))
            .subTotal(new BigDecimal("10.00"))
            .originSubTotalAmount(new BigDecimal("10.00"))
            .finalSubTotalAmount(new BigDecimal("10.00"))
            .imageUrl("https://example.com/image.jpg")
            .skuAttrib("颜色:红色")
            .skuAttribEn("Color:Red")
            .build();

        // 构建最简化的订单
        WmsOrder order = WmsOrder.builder()
            .purchaseNo("TEST_ORDER_001")
            .gffCustomerId(10002L)
            .orderId(System.currentTimeMillis())
            .sellerOpenId("TEST_SELLER")
            .storeId("536")
            .createUser("10002")
            .createTime("2025-07-01T10:00:00.000Z")
            .remark("测试订单")
            .platform("1688")
            .payType(PayTypeEnum.ALIPAY)
            .outTradeNo("TXN" + System.currentTimeMillis())
            .shippingFee(new BigDecimal("5.00"))
            .originalShippingFee(new BigDecimal("5.00"))
            .finalShoppingFee(new BigDecimal("5.00"))
            .serviceFee(new BigDecimal("0.00"))
            .total(new BigDecimal("15.00"))
            .productOriginalTotalAmount(new BigDecimal("10.00"))
            .productSalesTotalAmount(new BigDecimal("10.00"))
            .productFinalTotalAmount(new BigDecimal("10.00"))
            .originalTotalPrice(new BigDecimal("15.00"))
            .discount(new BigDecimal("0.00"))
            .plusDiscount(new BigDecimal("0.00"))
            .couponDiscount(new BigDecimal("0.00"))
            .totalAmount(new BigDecimal("15.00"))
            .status(1)
            .isRequestQuote(false)
            .link("https://detail.1688.com/offer/123456.html")
            .platformStatus("WAIT_SELLER_SEND_GOODS")
            .platformRemark("等待发货")
            .orderDetails(Collections.singletonList(orderDetail))
            .build();

        // 构建请求
        WmsCreateOrder request = new WmsCreateOrder();
        request.setCustomerCode(String.valueOf(10002L));
        request.setAccessToken("666");
        request.setApiKey("MZL68790A5B3C2D1E9F8G7H6I5J4K3L2");
        request.setTimestamp(System.currentTimeMillis() / 1000);
        request.setSignature("debug-signature");
        request.setOrders(Collections.singletonList(order));

        log.info("调试请求数据: {}", request);

        // 调用API
        Optional<WmsApiResponse<Object>> responseOptional = wmsApi.createPurchaseOrder(request);

        if (responseOptional.isPresent()) {
            WmsApiResponse<Object> response = responseOptional.get();
            log.info("调试响应数据: {}", response);
        } else {
            log.error("调试失败 - 无响应数据");
        }
    }

    @Test
    @DisplayName("【集成测试】验证WMS API连接性 - 简化版本")
    void testWmsApiConnectivity_Simplified() {
        // 1. 构建最简单的请求
        WmsCreateOrder request = new WmsCreateOrder();
        request.setCustomerCode(String.valueOf(10002L));
        request.setAccessToken("test");
        request.setApiKey("test-api-key");
        request.setTimestamp(System.currentTimeMillis() / 1000);
        request.setSignature("test-signature");
        request.setOrders(Collections.emptyList());

        log.info("测试WMS API连接性，请求: {}", request);

        // 2. 调用API
        Optional<WmsApiResponse<Object>> responseOptional = wmsApi.createPurchaseOrder(request);

        // 3. 验证连接性（不要求具体的业务逻辑成功）
        if (responseOptional.isPresent()) {
            log.info("WMS API连接成功，响应: {}", responseOptional.get());
        } else {
            log.warn("WMS API连接失败或返回空响应");
            // 使用 Assumptions 来跳过测试而不是失败
            org.junit.jupiter.api.Assumptions.assumeTrue(false, "WMS Mock 服务器不可用，跳过连接性测试");
        }
    }

    @Test
    @DisplayName("【集成测试】使用有效数据调用WMS创建产品接口")
    void createProduct_WithValidData_ShouldSucceed() {
        // --- 1. 构建请求数据 ---
        WmsProduct request = WmsProduct.builder()
            .customerId("10002")
            .sku("MZL01140702")
            .barcode("19674814")
            .enName("Test Product - High Quality Bluetooth Headset")
            .cnName("测试商品-高品质蓝牙耳机")
            .remark("made in china is so good")
            .price(new BigDecimal("99.99"))
            .weight(new BigDecimal("0.25"))
            .length(new BigDecimal("5.00"))
            .width(new BigDecimal("2.00"))
            .height(new BigDecimal("3.00"))
            .hsCode("11111")
            .origin("CN")
            .brand("品牌")
            .battery("0")
            .expectNum(5)
            .build();

        // 替换为真实的测试API密钥
        String apiKey = "b7e2e0ee-469e-4243-b631-4d5ea4c2fb81";

        log.info("构造的WMS创建产品请求: {}", request);

        // --- 2. 执行 API 调用 ---
        Optional<WmsApiResponse<Object>> responseOptional = wmsApi.createProduct(request, apiKey);

        // --- 3. 断言响应 ---
        assertTrue(responseOptional.isPresent(), "WMS API应返回响应");

        WmsApiResponse<Object> response = responseOptional.get();
        log.info("从WMS收到的创建产品响应: {}", response);

    }

    @Test
    @DisplayName("【集成测试】获取产品列表 - 使用有效API Key")
    void getProductList_WithValidApiKey_ShouldReturnData() {
        // --- 1. 准备测试参数 ---
        // 重要：请将 "YOUR_VALID_API_KEY" 替换为真实有效的 WMS API 密钥
        String apiKey = "b7e2e0ee-469e-4243-b631-4d5ea4c2fb81";
        Integer page = 1;
        String barcode = null;

        log.info("开始测试WMS产品列表获取接口: apiKey=[{}], page=[{}], barcode=[{}]", apiKey, page, barcode);

        // --- 2. 执行API调用 ---
        Optional<WmsApiResponse<List<WmsProduct>>> responseOptional = wmsApi.getProductList(apiKey, page, barcode);

        // --- 3. 断言和验证 ---
        assertTrue(responseOptional.isPresent(), "WMS API 应返回响应，不能是空的 Optional");

        WmsApiResponse<List<WmsProduct>> response = responseOptional.get();
        log.info("从WMS收到的产品列表响应: {}", response);

        // 断言业务逻辑是否成功
        if (!response.isSuccess()) {
            // 如果调用不成功，提供详细的错误信息并使测试失败
            String errorMessage = String.format("WMS API返回业务失败: Code=[%s], Message=[%s], EnMessage=[%s]",
                response.getMessage());
            fail(errorMessage);
        }

        // 断言成功时的数据
        assertNotNull(response.getData(), "成功时，响应的 data 字段不应为 null");
        assertFalse(response.getData().isEmpty(), "成功时，产品列表不应为空");

        // 打印部分结果以供检查
        WmsProduct firstProduct = response.getData().get(0);
        log.info("成功获取到 {} 条产品数据。第一条产品信息: SKU=[{}], CnName=[{}]",
            response.getData().size(),
            firstProduct.getSku(),
            firstProduct.getCnName());

        // 验证第一条数据的关键字段不为空
        assertNotNull(firstProduct.getSku(), "产品的SKU不应为null");
        assertNotNull(firstProduct.getCnName(), "产品的中文名不应为null");
    }
}
