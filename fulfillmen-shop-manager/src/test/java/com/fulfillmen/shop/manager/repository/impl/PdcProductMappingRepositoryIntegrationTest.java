/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.repository.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.domain.entity.enums.PlatformCodeEnum;
import com.fulfillmen.shop.manager.TestConfiguration;
import com.fulfillmen.shop.manager.core.repository.impl.PdcProductMappingRepositoryImpl;

import java.math.BigDecimal;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import me.ahoo.cosid.IdGenerator;
import me.ahoo.cosid.provider.IdGeneratorProvider;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 商品映射仓储集成测试 - 真实API调用
 *
 * <pre>
 * 此测试类专门用于集成测试，会调用真实的1688 API接口
 * 测试重点：
 * 1. 完整的缓存→数据库→API三级数据获取策略
 * 2. 真实的数据转换和存储流程
 * 3. 缓存机制的实际效果
 * 4. 数据完整性验证
 * </pre>
 */
@Slf4j
@SpringBootTest(classes = TestConfiguration.class)
@ActiveProfiles("test")
//@Transactional
class PdcProductMappingRepositoryIntegrationTest {

    @Autowired
    private PdcProductMappingRepositoryImpl pdcProductMappingRepository;

    @Autowired(required = false)
    private IdGeneratorProvider idGeneratorProvider;

    @BeforeEach
    void setUp() {
        log.info("准备执行集成测试用例");
    }

    // ======================== 配置验证测试 ========================

    @Test
    @DisplayName("配置验证 - CosID生成器是否正常工作")
    void testCosIdGeneratorConfiguration() {
        log.info("=== 开始CosID配置验证 ===");

        if (idGeneratorProvider == null) {
            log.warn("⚠️ IdGeneratorProvider未注入，跳过CosID测试");
            return;
        }

        try {
            // 直接测试CosID生成器
            IdGenerator idGenerator = idGeneratorProvider.getRequired("safe-js");
            assertNotNull(idGenerator, "safe-js ID生成器应该存在");

            // 生成几个ID测试
            Long id1 = idGenerator.generate();
            Long id2 = idGenerator.generate();
            Long id3 = idGenerator.generate();

            assertNotNull(id1, "生成的ID不应为空");
            assertNotNull(id2, "生成的ID不应为空");
            assertNotNull(id3, "生成的ID不应为空");

            // 验证ID的唯一性
            assertNotEquals(id1, id2, "生成的ID应该唯一");
            assertNotEquals(id2, id3, "生成的ID应该唯一");
            assertNotEquals(id1, id3, "生成的ID应该唯一");

            log.info("✅ CosID配置正常，生成的ID: {}, {}, {}", id1, id2, id3);

        } catch (Exception e) {
            log.error("❌ CosID配置异常", e);
            fail("CosID配置异常: " + e.getMessage());
        }

        log.info("=== CosID配置验证完成 ===");
    }

    // ======================== 集成测试 - 真实API调用 ========================

    @Test
    @DisplayName("集成测试 - 获取商品详情完整流程（真实API调用）")
    void testGetProductDetailWithCache_RealApiIntegration() {
        // 使用真实的1688商品ID进行测试
        // 注意：这个商品ID需要是一个确实存在的1688商品，你可以根据实际情况替换
        Long realProductId = 702121458401282L;

        log.info("=== 开始集成测试：完整的商品详情获取流程 ===");
        log.info("测试商品ID: {}", realProductId);

        // ========== 第1步：首次调用 - 期望从API获取并同步到数据库和缓存 ==========
        log.info("第1步：首次调用 getProductDetailWithCache - 期望从API获取");
        long startTime1 = System.currentTimeMillis();

        AlibabaProductDetailDTO result1 = pdcProductMappingRepository.getProductDetailWithCache(realProductId, true);

        long endTime1 = System.currentTimeMillis();
        log.info("第1次调用耗时: {}ms", (endTime1 - startTime1));

        // 验证第一次调用结果
        assertNotNull(result1, "第一次调用应该返回商品详情");
        assertNotNull(result1.getPlatformProductId(), "平台商品ID不应为空");
        assertNotNull(result1.getTitle(), "商品标题不应为空");
        assertEquals(PlatformCodeEnum.PLATFORM_CODE_1688, result1.getPlatformCode(), "平台代码应为1688");

        log.info("第1次调用结果 - 商品ID: {}, 标题: {}", result1.getPlatformProductId(), result1.getTitle());

        // ========== 第2步：第二次调用 - 期望从缓存获取（速度更快） ==========
        log.info("第2步：第二次调用 getProductDetailWithCache - 期望从缓存获取");
        long startTime2 = System.currentTimeMillis();

        AlibabaProductDetailDTO result2 = pdcProductMappingRepository.getProductDetailWithCache(realProductId, false);

        long endTime2 = System.currentTimeMillis();
        log.info("第2次调用耗时: {}ms", (endTime2 - startTime2));

        // 验证第二次调用结果（应该与第一次相同，但速度更快）
        assertNotNull(result2, "第二次调用应该返回商品详情");
        assertEquals(result1.getPlatformProductId(), result2.getPlatformProductId(), "两次调用的商品ID应该相同");
        assertEquals(result1.getTitle(), result2.getTitle(), "两次调用的商品标题应该相同");
        assertEquals(result1.getPlatformCode(), result2.getPlatformCode(), "两次调用的平台代码应该相同");

        // 验证缓存效果：第二次调用应该明显更快
        assertTrue((endTime2 - startTime2) < (endTime1 - startTime1), "第二次调用应该从缓存获取，速度更快");

        log.info("第2次调用结果 - 商品ID: {}, 标题: {}", result2.getPlatformProductId(), result2.getTitle());

        // ========== 第3步：强制刷新 - 期望重新从API获取最新数据 ==========
        log.info("第3步：强制刷新调用 getProductDetailWithCache - 期望重新从API获取");
        long startTime3 = System.currentTimeMillis();

        AlibabaProductDetailDTO result3 = pdcProductMappingRepository.getProductDetailWithCache(realProductId, true);

        long endTime3 = System.currentTimeMillis();
        log.info("第3次调用（强制刷新）耗时: {}ms", (endTime3 - startTime3));

        // 验证强制刷新结果
        assertNotNull(result3, "强制刷新调用应该返回商品详情");
        assertEquals(result1.getPlatformProductId(), result3.getPlatformProductId(), "强制刷新的商品ID应该相同");
        assertEquals(result1.getTitle(), result3.getTitle(), "强制刷新的商品标题应该相同");

        // 验证强制刷新效果：耗时应该与第一次类似（都是API调用）
        assertTrue((endTime3 - startTime3) > (endTime2 - startTime2), "强制刷新应该重新调用API，耗时较长");

        log.info("第3次调用结果 - 商品ID: {}, 标题: {}", result3.getPlatformProductId(), result3.getTitle());

        // ========== 第4步：验证数据完整性 ==========
        log.info("第4步：验证返回数据的完整性");

        // 验证基本字段
        assertNotNull(result3.getId(), "商品内部ID不应为空");
        assertNotNull(result3.getPlatformProductId(), "平台商品ID不应为空");
        assertTrue(result3.getPlatformProductId().length() > 0, "平台商品ID不应为空字符串");

        // 验证价格信息
        if (result3.getProductSaleInfo() != null) {
            log.info("商品销售信息存在，验证价格数据");
            assertNotNull(result3.getProductSaleInfo(), "销售信息不应为空");

            if (result3.getProductSaleInfo().getPriceRangeList() != null && !result3.getProductSaleInfo()
                .getPriceRangeList()
                .isEmpty()) {
                log.info("价格区间数量: {}", result3.getProductSaleInfo().getPriceRangeList().size());

                result3.getProductSaleInfo().getPriceRangeList().forEach(priceRange -> {
                    assertNotNull(priceRange.getPrice(), "价格不应为空");
                    assertTrue(priceRange.getPrice().compareTo(BigDecimal.ZERO) > 0, "价格应大于0");
                    log.info("价格区间: 起批量={}, 价格={}", priceRange.getStartQuantity(), priceRange.getPrice());
                });
            }

            // 验证最小起批量
            if (result3.getProductSaleInfo().getMinOrderQuantity() != null) {
                assertTrue(result3.getProductSaleInfo().getMinOrderQuantity() > 0, "最小起批量应大于0");
                log.info("最小起批量: {}", result3.getProductSaleInfo().getMinOrderQuantity());
            }
        }

        // 验证图片信息
        if (result3.getImages() != null && !result3.getImages().isEmpty()) {
            log.info("商品图片数量: {}", result3.getImages().size());
            result3.getImages().forEach(imageUrl -> {
                assertNotNull(imageUrl, "图片URL不应为空");
                assertTrue(imageUrl.startsWith("http"), "图片URL应为有效的HTTP地址");
            });
            log.info("主图URL: {}", result3.getImages().get(0));
        }

        // 验证白底图
        if (result3.getWhiteImage() != null) {
            assertTrue(result3.getWhiteImage().startsWith("http"), "白底图URL应为有效的HTTP地址");
            log.info("白底图URL: {}", result3.getWhiteImage());
        }

        // 验证SKU信息
        if (result3.getProductSkuList() != null && !result3.getProductSkuList().isEmpty()) {
            log.info("商品SKU数量: {}", result3.getProductSkuList().size());
            result3.getProductSkuList().forEach(sku -> {
                assertNotNull(sku.getSkuId(), "SKU ID不应为空");
                if (sku.getPrice() != null) {
                    assertTrue(sku.getPrice().compareTo(BigDecimal.ZERO) > 0, "SKU价格应大于0");
                }
                log.info("SKU ID: {}, 价格: {}, 库存: {}", sku.getSkuId(), Objects.isNull(sku.getPrice())
                    ? sku.getOfferPrice()
                    : sku.getPrice(), sku.getAmountOnSale());
            });
        } else {
            log.info("商品为单件商品，无SKU信息");
            assertTrue(result3.isSingleItem(), "如果没有SKU，应该标记为单件商品");
        }

        // 验证类目信息
        if (result3.getCategoryId() != null) {
            assertTrue(result3.getCategoryId() > 0, "类目ID应大于0");
            log.info("类目ID: {}, 类目名称: {}", result3.getCategoryId(), result3.getCategoryName());
        }

        // 验证商品属性
        if (result3.getProductAttributeList() != null && !result3.getProductAttributeList().isEmpty()) {
            log.info("商品属性数量: {}", result3.getProductAttributeList().size());
            result3.getProductAttributeList().forEach(attr -> {
                assertNotNull(attr.getAttributeName(), "属性名称不应为空");
                assertNotNull(attr.getValue(), "属性值不应为空");
                log.info("属性: {} = {}", attr.getAttributeName(), attr.getValue());
            });
        }

        // ========== 第5步：验证数据库存储 ==========
        log.info("第5步：验证数据是否正确存储到数据库");

        // 再次调用，确保数据库中确实有数据
        AlibabaProductDetailDTO result4 = pdcProductMappingRepository.getProductDetailWithCache(realProductId, false);
        assertNotNull(result4, "从数据库应该能够获取商品详情");
        assertEquals(result3.getPlatformProductId(), result4.getPlatformProductId(), "数据库中的数据应该一致");

        log.info("=== 集成测试完成：所有验证通过 ===");
        log.info("测试总结:");
        log.info("- 第1次调用（API）耗时: {}ms", (endTime1 - startTime1));
        log.info("- 第2次调用（缓存）耗时: {}ms", (endTime2 - startTime2));
        log.info("- 第3次调用（强制刷新）耗时: {}ms", (endTime3 - startTime3));
        log.info("- 缓存加速比: {}倍", String.format("%.2f", (double) (endTime1 - startTime1) / (endTime2 - startTime2)));
        log.info("- 商品标题: {}", result3.getTitle());
        log.info("- 商品描述长度: {}", result3.getDescription() != null ? result3.getDescription().length() : 0);
        log.info("- 商品图片数量: {}", result3.getImages() != null ? result3.getImages().size() : 0);
        log.info("- 商品SKU数量: {}", result3.getProductSkuList() != null ? result3.getProductSkuList().size() : 0);
    }

    @Test
    @DisplayName("集成测试 - 多个不同商品的缓存隔离验证")
    void testGetProductDetailWithCache_MultipleDifferentProducts() {
        // 测试多个不同商品的缓存隔离
        Long productId1 = 1600469482742L; // 第一个商品
        Long productId2 = 1600469482743L; // 第二个商品（如果存在的话）

        log.info("=== 开始多商品缓存隔离测试 ===");

        // 获取第一个商品
        log.info("获取第一个商品: {}", productId1);
        AlibabaProductDetailDTO product1 = pdcProductMappingRepository.getProductDetailWithCache(productId1, false);

        if (product1 != null) {
            log.info("第一个商品获取成功 - ID: {}, 标题: {}", product1.getPlatformProductId(), product1.getTitle());

            // 再次获取第一个商品，验证缓存
            AlibabaProductDetailDTO product1Cache = pdcProductMappingRepository
                .getProductDetailWithCache(productId1, false);
            assertNotNull(product1Cache, "缓存的商品应该能够获取");
            assertEquals(product1.getPlatformProductId(), product1Cache.getPlatformProductId(), "缓存的商品ID应该一致");

            log.info("第一个商品缓存验证成功");
        } else {
            log.warn("第一个商品获取失败，可能商品不存在或API调用失败");
        }

        // 尝试获取第二个商品（可能不存在）
        log.info("尝试获取第二个商品: {}", productId2);
        AlibabaProductDetailDTO product2 = pdcProductMappingRepository.getProductDetailWithCache(productId2, false);

        if (product2 != null) {
            log.info("第二个商品获取成功 - ID: {}, 标题: {}", product2.getPlatformProductId(), product2.getTitle());

            // 验证两个商品的数据隔离
            if (product1 != null) {
                assertNotEquals(product1.getPlatformProductId(), product2.getPlatformProductId(), "两个商品的ID应该不同");
                log.info("两个商品的缓存隔离验证成功");
            }
        } else {
            log.info("第二个商品不存在或获取失败，这是正常的");
        }

        log.info("=== 多商品缓存隔离测试完成 ===");
    }
}
