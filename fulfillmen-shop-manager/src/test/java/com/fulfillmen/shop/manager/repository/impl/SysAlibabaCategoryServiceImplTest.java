/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.repository.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.fulfillmen.shop.manager.TestConfiguration;
import com.fulfillmen.shop.manager.core.repository.impl.SysAlibabaCategoryRepositoryImpl;
import com.fulfillmen.shop.domain.dto.AlibabaCategoryTreeDTO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

/**
 * 阿里巴巴商品类目服务集成测试
 */
@Slf4j
@SpringBootTest(classes = TestConfiguration.class)
@ActiveProfiles("test")
@Transactional
class SysAlibabaCategoryServiceImplTest {

    @Autowired
    private SysAlibabaCategoryRepositoryImpl sysAlibabaCategoryService;

    @Test
    @DisplayName("测试获取所有类目 - 集成测试")
    void testGetAlibabaCategoryAll() {
        // 执行测试
        List<AlibabaCategoryTreeDTO> categories = sysAlibabaCategoryService.getAlibabaCategoryAll();

        // 基本验证
        assertNotNull(categories);
        assertFalse(categories.isEmpty());
    }

    @Test
    @DisplayName("测试带缓存的获取类目")
    void testGetAlibabaCategoryAllWithCache() {

        // 第一次调用，应该从数据库获取
        log.info("第一次调用 getAlibabaCategoryAllWithCache");
        List<AlibabaCategoryTreeDTO> result1 = sysAlibabaCategoryService.getAlibabaCategoryAllWithCache();
        assertNotNull(result1);
        assertFalse(result1.isEmpty());

        // 第二次调用(应该走缓存)
        log.info("第二次调用 getAlibabaCategoryAllWithCache");
        List<AlibabaCategoryTreeDTO> result2 = sysAlibabaCategoryService.getAlibabaCategoryAllWithCache();
        assertNotNull(result2);

        // 验证两次结果一致
        assertEquals(result1, result2);
        assertEquals(result1.get(0).getCategoryId(), result2.get(0).getCategoryId());
        assertEquals(result1.get(0).getChineseName(), result2.get(0).getChineseName());
        assertEquals(result1.get(result1.size() - 1).getChineseName(), result2.get(result1.size() - 1)
            .getChineseName());
        assertEquals(result1.get(result1.size() - 1).getChineseName(), result2.get(result1.size() - 1)
            .getChineseName());

        // 验证数据结构完整性
        result2.forEach(category -> {
            assertNotNull(category.getCategoryId());
            assertNotNull(category.getChineseName());
            assertNotNull(category.getTranslatedName());
        });
    }
}
