/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.repository.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.domain.entity.PdcProductMapping;
import com.fulfillmen.shop.domain.entity.enums.PdcProductMappingSyncStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.PlatformCodeEnum;
import com.fulfillmen.shop.manager.TestConfiguration;
import com.fulfillmen.shop.manager.core.repository.impl.PdcProductMappingRepositoryImpl;
import com.fulfillmen.shop.manager.support.alibaba.IProductManager;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsDetailResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsImageSearchResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsSearchResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsSellerResponse;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

/**
 * 商品映射仓储实现类测试
 *
 * <pre>
 * 重点测试：
 * 1. 缓存机制的正确性
 * 2. 批量数据同步逻辑
 * 3. 不同来源数据的转换和同步
 * 4. 异常情况处理
 * </pre>
 */
@Slf4j
@SpringBootTest(classes = TestConfiguration.class)
@ActiveProfiles("test")
@Transactional
class PdcProductMappingRepositoryImplTest {

    @Autowired
    private PdcProductMappingRepositoryImpl pdcProductMappingRepository;

    @MockBean
    private IProductManager productManager;

    private Long testProductId = 12345L;
    private String testPlatformProductId = "12345";

    @BeforeEach
    void setUp() {
        log.info("开始执行测试用例");
    }

    // ======================== 缓存功能测试 ========================

    @Test
    @DisplayName("测试获取商品详情 - 缓存机制")
    void testGetProductDetailWithCache() {
        // 准备测试数据 - 模拟API返回
        GoodsDetailResponse.ProductDetail mockProductDetail = createMockProductDetail();
        when(productManager.getProductDetail(testProductId)).thenReturn(mockProductDetail);

        // 第一次调用 - 应该从API获取并缓存
        log.info("第一次调用 getProductDetailWithCache，预期从API获取");
        AlibabaProductDetailDTO result1 = pdcProductMappingRepository.getProductDetailWithCache(testProductId, false);

        assertNotNull(result1);
        assertEquals(testPlatformProductId, result1.getPlatformProductId());
        assertEquals("测试商品", result1.getTitle());

        // 验证API被调用了一次
        verify(productManager, times(1)).getProductDetail(testProductId);

        // 第二次调用 - 应该从缓存获取
        log.info("第二次调用 getProductDetailWithCache，预期从缓存获取");
        AlibabaProductDetailDTO result2 = pdcProductMappingRepository.getProductDetailWithCache(testProductId, false);

        assertNotNull(result2);
        assertEquals(result1.getPlatformProductId(), result2.getPlatformProductId());
        assertEquals(result1.getTitle(), result2.getTitle());

        // 验证API没有再次被调用
        verify(productManager, times(1)).getProductDetail(testProductId);
    }

    @Test
    @DisplayName("测试强制刷新缓存")
    void testGetProductDetailWithForceRefresh() {
        // 准备测试数据
        GoodsDetailResponse.ProductDetail mockProductDetail = createMockProductDetail();
        when(productManager.getProductDetail(testProductId)).thenReturn(mockProductDetail);

        // 正常获取一次
        pdcProductMappingRepository.getProductDetailWithCache(testProductId, false);

        // 强制刷新
        log.info("强制刷新缓存测试");
        AlibabaProductDetailDTO result = pdcProductMappingRepository.getProductDetailWithCache(testProductId, true);

        assertNotNull(result);
        // 验证API被调用了两次（第一次正常调用 + 强制刷新调用）
        verify(productManager, times(2)).getProductDetail(testProductId);
    }

    // ======================== 批量操作核心测试 ========================

    /*@Test
    @DisplayName("测试批量插入新商品数据")
    void testBatchInsertOrUpdateDataSync_NewProducts() {
        // 准备测试数据 - 全新商品
        List<PdcProductMapping> newProducts = createTestProductMappings(3, false);
    
        log.info("测试批量插入新商品，数量: {}", newProducts.size());
    
        // 执行批量操作
        int affectedRows = pdcProductMappingRepository.batchInsertOrUpdateDataSync(newProducts);
    
        // 验证结果
        assertEquals(3, affectedRows);
        log.info("批量插入完成，受影响行数: {}", affectedRows);
    }
    
    @Test
    @DisplayName("测试批量更新已存在商品数据")
    void testBatchInsertOrUpdateDataSync_ExistingProducts() {
        // 1. 先插入一些商品数据
        List<PdcProductMapping> initialProducts = createTestProductMappings(2, false);
        pdcProductMappingRepository.batchInsertOrUpdateDataSync(initialProducts);
    
        // 2. 准备更新数据（相同的platformProductId，但不同的商品信息）
        List<PdcProductMapping> updatedProducts = createTestProductMappings(2, true);
    
        log.info("测试批量更新已存在商品，数量: {}", updatedProducts.size());
    
        // 执行批量更新
        int affectedRows = pdcProductMappingRepository.batchInsertOrUpdateDataSync(updatedProducts);
    
        // 验证结果
        assertEquals(2, affectedRows);
        log.info("批量更新完成，受影响行数: {}", affectedRows);
    }
    
    @Test
    @DisplayName("测试批量操作 - 混合新增和更新")
    void testBatchInsertOrUpdateDataSync_MixedOperations() {
        // 1. 先插入一个商品
        List<PdcProductMapping> initialProduct = createTestProductMappings(1, false);
        pdcProductMappingRepository.batchInsertOrUpdateDataSync(initialProduct);
    
        // 2. 准备混合数据：1个更新 + 2个新增
        List<PdcProductMapping> mixedProducts = Arrays.asList(createUpdatedProductMapping(initialProduct.get(0)
            .getPlatformProductId()), createNewProductMapping("99998"), createNewProductMapping("99999"));
    
        log.info("测试混合批量操作，数量: {}", mixedProducts.size());
    
        // 执行混合操作
        int affectedRows = pdcProductMappingRepository.batchInsertOrUpdateDataSync(mixedProducts);
    
        // 验证结果
        assertEquals(3, affectedRows);
        log.info("混合批量操作完成，受影响行数: {}", affectedRows);
    }*/

    // ======================== 数据同步功能测试 ========================

    @Test
    @DisplayName("测试同步搜索结果数据")
    void testSyncSearchResultData() {
        // 准备搜索结果测试数据
        List<GoodsSearchResponse.GoodsInfo> searchResults = createMockSearchResults();

        log.info("测试同步搜索结果数据，数量: {}", searchResults.size());

        List<PdcProductMapping> pdcProductMappings = new ArrayList<>();

        // 执行同步
        List<PdcProductMapping> affectedRows = pdcProductMappingRepository.syncSearchResultData(searchResults);

        // 验证结果
        assertTrue(!affectedRows.isEmpty());
        assertEquals(searchResults.size(), affectedRows);
        log.info("搜索结果数据同步完成，受影响行数: {}", affectedRows);
    }

    @Test
    @DisplayName("测试同步图片搜索结果数据")
    void testSyncImageSearchResultData() {
        // 准备图片搜索结果测试数据
        List<GoodsImageSearchResponse.GoodsInfo> imageSearchResults = createMockImageSearchResults();

        log.info("测试同步图片搜索结果数据，数量: {}", imageSearchResults.size());

        // 执行同步
        var affectedRows = pdcProductMappingRepository.syncImageSearchResultData(imageSearchResults);

        // 验证结果
        assertTrue(affectedRows.size() > 0);
        assertEquals(imageSearchResults.size(), affectedRows);
        log.info("图片搜索结果数据同步完成，受影响行数: {}", affectedRows);
    }

    @Test
    @DisplayName("测试同步卖家商品数据")
    void testSyncSellerProductData() {
        // 准备卖家商品测试数据
        List<GoodsSellerResponse.ProductInfo> sellerProducts = createMockSellerProducts();

        log.info("测试同步卖家商品数据，数量: {}", sellerProducts.size());

        // 执行同步
        int affectedRows = pdcProductMappingRepository.syncSellerProductData(sellerProducts);

        // 验证结果
        assertTrue(affectedRows > 0);
        assertEquals(sellerProducts.size(), affectedRows);
        log.info("卖家商品数据同步完成，受影响行数: {}", affectedRows);
    }

    // ======================== 异常情况测试 ========================

    /*    @Test
    @DisplayName("测试空列表批量操作")
    void testBatchInsertOrUpdateDataSync_EmptyList() {
        log.info("测试空列表批量操作");
    
        int affectedRows = pdcProductMappingRepository.batchInsertOrUpdateDataSync(null);
        assertEquals(0, affectedRows);
    
        affectedRows = pdcProductMappingRepository.batchInsertOrUpdateDataSync(Arrays.asList());
        assertEquals(0, affectedRows);
    
        log.info("空列表测试通过");
    }*/

    @Test
    @DisplayName("测试API调用失败的情况")
    void testGetProductDetailWithCache_ApiFailure() {
        // 模拟API调用失败
        when(productManager.getProductDetail(testProductId)).thenReturn(null);

        log.info("测试API调用失败情况");

        // 执行调用
        AlibabaProductDetailDTO result = pdcProductMappingRepository.getProductDetailWithCache(testProductId, false);

        // 验证结果
        assertNull(result);
        log.info("API失败测试通过");
    }

    // ======================== 私有辅助方法 ========================

    /**
     * 创建模拟的商品详情数据
     */
    private GoodsDetailResponse.ProductDetail createMockProductDetail() {
        GoodsDetailResponse.ProductDetail productDetail = new GoodsDetailResponse.ProductDetail();
        productDetail.setOfferId(testProductId);
        productDetail.setSubject("测试商品");
        productDetail.setMinOrderQuantity(1);

        // 创建图片信息
        GoodsDetailResponse.ProductImage productImage = new GoodsDetailResponse.ProductImage();
        productImage.setImages(List.of("http://example.com/image1.jpg"));
        productDetail.setProductImage(productImage);

        return productDetail;
    }

    /**
     * 创建测试用的商品映射数据
     */
    private List<PdcProductMapping> createTestProductMappings(int count, boolean isUpdate) {
        return Arrays.asList(createNewProductMapping("10001" + (isUpdate
            ? "_updated"
            : "")), createNewProductMapping("10002" + (isUpdate
                ? "_updated"
                : "")), createNewProductMapping("10003" + (isUpdate ? "_updated" : ""))).subList(0, count);
    }

    /**
     * 创建新的商品映射对象
     */
    private PdcProductMapping createNewProductMapping(String platformProductId) {
        PdcProductMapping mapping = new PdcProductMapping();
        mapping.setPlatformCode(PlatformCodeEnum.PLATFORM_CODE_1688);
        mapping.setPlatformProductId(platformProductId);
        mapping.setPlatformProductName("测试商品_" + platformProductId);
        mapping.setPlatformProductPrice(new BigDecimal("99.99"));
        mapping.setPlatformProductMinQuantity(1);
        mapping.setIsSynced(PdcProductMappingSyncStatusEnum.SYNCED);
        mapping.setGmtCreated(LocalDateTime.now());
        mapping.setGmtModified(LocalDateTime.now());
        return mapping;
    }

    /**
     * 创建更新的商品映射对象
     */
    private PdcProductMapping createUpdatedProductMapping(String platformProductId) {
        PdcProductMapping mapping = createNewProductMapping(platformProductId);
        mapping.setPlatformProductName("更新后的商品_" + platformProductId);
        mapping.setPlatformProductPrice(new BigDecimal("199.99"));
        return mapping;
    }

    /**
     * 创建模拟搜索结果数据
     */
    private List<GoodsSearchResponse.GoodsInfo> createMockSearchResults() {
        GoodsSearchResponse.GoodsInfo goods1 = new GoodsSearchResponse.GoodsInfo();
        goods1.setOfferId(20001L);
        goods1.setSubject("搜索商品1");

        // 创建价格信息
        GoodsSearchResponse.PriceInfo priceInfo1 = new GoodsSearchResponse.PriceInfo();
        priceInfo1.setPrice("88.88");
        goods1.setPriceInfo(priceInfo1);

        GoodsSearchResponse.GoodsInfo goods2 = new GoodsSearchResponse.GoodsInfo();
        goods2.setOfferId(20002L);
        goods2.setSubject("搜索商品2");

        GoodsSearchResponse.PriceInfo priceInfo2 = new GoodsSearchResponse.PriceInfo();
        priceInfo2.setPrice("77.77");
        goods2.setPriceInfo(priceInfo2);

        return Arrays.asList(goods1, goods2);
    }

    /**
     * 创建模拟图片搜索结果数据
     */
    private List<GoodsImageSearchResponse.GoodsInfo> createMockImageSearchResults() {
        GoodsImageSearchResponse.GoodsInfo goods1 = new GoodsImageSearchResponse.GoodsInfo();
        goods1.setOfferId(30001L);
        goods1.setSubject("图片搜索商品1");

        // 创建价格信息
        GoodsImageSearchResponse.PriceInfo priceInfo = new GoodsImageSearchResponse.PriceInfo();
        priceInfo.setPrice("66.66");
        goods1.setPriceInfo(priceInfo);

        return Arrays.asList(goods1);
    }

    /**
     * 创建模拟卖家商品数据
     */
    private List<GoodsSellerResponse.ProductInfo> createMockSellerProducts() {
        GoodsSellerResponse.ProductInfo product1 = new GoodsSellerResponse.ProductInfo();
        product1.setOfferId(40001L);
        product1.setSubject("卖家商品1");

        // 创建价格信息
        GoodsSellerResponse.PriceInfo priceInfo = new GoodsSellerResponse.PriceInfo();
        priceInfo.setPrice("55.55");
        product1.setPriceInfo(priceInfo);

        return Arrays.asList(product1);
    }
}
