/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.repository.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fulfillmen.shop.domain.dto.PageDTO;
import com.fulfillmen.shop.domain.dto.ProductInfoDTO;
import com.fulfillmen.shop.domain.dto.ProductSearchRequestDTO;
import com.fulfillmen.shop.domain.req.AggregateSearchReq;
import com.fulfillmen.shop.domain.req.AggregateSearchReq.SearchType;
import com.fulfillmen.shop.manager.TestConfiguration;
import com.fulfillmen.shop.manager.core.repository.impl.PdcProductMappingRepositoryImpl;
import com.fulfillmen.support.alibaba.enums.LanguageEnum;
import java.util.Arrays;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

/**
 * 商品映射仓储搜索功能集成测试
 *
 * <pre>
 * 此测试类专门测试PdcProductMappingRepository中的搜索相关方法：
 * 1. searchProductInfoListSync - 搜索商品信息列表并自动同步入库
 * 2. searchSimilarProductsSync - 搜索相似商品并同步进库
 * 3. aggregateSearchSync - 聚合搜索商品
 *
 * 测试重点：
 * - 真实API调用和数据同步
 * - 分页功能验证
 * - 搜索参数处理
 * - 数据转换和存储
 * - 异常情况处理
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/5/29 23:30
 * @since 1.0.0
 */
@Slf4j
@SpringBootTest(classes = TestConfiguration.class)
@ActiveProfiles("test")
@Transactional
class PdcProductMappingRepositorySearchIntegrationTest {

    @Autowired
    private PdcProductMappingRepositoryImpl pdcProductMappingRepository;

    @BeforeEach
    void setUp() {
        log.info("=== 准备执行搜索功能集成测试 ===");
    }

    // ======================== searchProductInfoListSync 测试 ========================

    @Test
    @DisplayName("集成测试 - 关键词搜索商品信息列表并同步入库")
    void testSearchProductInfoListSync_KeywordSearch() {
        log.info("=== 测试关键词搜索商品信息列表并同步入库 ===");

        // 准备搜索请求参数
        ProductSearchRequestDTO searchRequest = ProductSearchRequestDTO.builder()
            .keyword("手机")
            .language(LanguageEnum.EN)
            .page(1)
            .pageSize(10)
            .minPrice("100")
            .maxPrice("5000")
            .sortField("price")
            .sortOrder("asc")
            .build();

        log.info("搜索参数 - 关键词: {}, 页码: {}, 每页大小: {}", searchRequest.getKeyword(), searchRequest.getPage(), searchRequest
            .getPageSize());

        // 执行搜索
        long startTime = System.currentTimeMillis();
        PageDTO<ProductInfoDTO> result = pdcProductMappingRepository.searchProductInfoListSync(searchRequest);
        long endTime = System.currentTimeMillis();

        log.info("搜索完成，耗时: {}ms", (endTime - startTime));

        // 验证搜索结果
        assertNotNull(result, "搜索结果不应为空");
        assertNotNull(result.getRecords(), "搜索记录列表不应为空");
        assertTrue(result.getTotal() >= 0, "总记录数应大于等于0");
        assertEquals(1, result.getPageIndex(), "页码应为1");
        assertTrue(result.getPageSize() <= 10, "每页大小应小于等于10");

        log.info("搜索结果 - 总记录数: {}, 当前页记录数: {}, 页码: {}, 每页大小: {}", result.getTotal(), result.getRecords().size(), result
            .getPageIndex(), result.getPageSize());

        // 验证商品信息完整性
        if (!result.getRecords().isEmpty()) {
            ProductInfoDTO firstProduct = result.getRecords().get(0);
            assertNotNull(firstProduct.getId(), "商品ID不应为空");
            assertNotNull(firstProduct.getName(), "商品名称不应为空");
            assertNotNull(firstProduct.getPlatformCode(), "平台代码不应为空");

            log.info("第一个商品信息 - ID: {}, 名称: {}, 平台: {}, 价格: {}", firstProduct.getId(), firstProduct
                .getName(), firstProduct.getPlatformCode(), firstProduct.getPrice());
        }
    }

    @Test
    @DisplayName("集成测试 - 关键词搜索分页功能")
    void testSearchProductInfoListSync_Pagination() {
        log.info("=== 测试关键词搜索分页功能 ===");

        String keyword = "电脑";
        int pageSize = 5;

        // 第一页搜索
        ProductSearchRequestDTO firstPageRequest = ProductSearchRequestDTO.builder()
            .keyword(keyword)
            .language(LanguageEnum.EN)
            .page(1)
            .pageSize(pageSize)
            .build();

        PageDTO<ProductInfoDTO> firstPageResult = pdcProductMappingRepository
            .searchProductInfoListSync(firstPageRequest);

        // 第二页搜索
        ProductSearchRequestDTO secondPageRequest = ProductSearchRequestDTO.builder()
            .keyword(keyword)
            .language(LanguageEnum.EN)
            .page(2)
            .pageSize(pageSize)
            .build();

        PageDTO<ProductInfoDTO> secondPageResult = pdcProductMappingRepository
            .searchProductInfoListSync(secondPageRequest);

        // 验证分页结果
        assertNotNull(firstPageResult, "第一页结果不应为空");
        assertNotNull(secondPageResult, "第二页结果不应为空");

        assertEquals(1, firstPageResult.getPageIndex(), "第一页页码应为1");
        assertEquals(2, secondPageResult.getPageIndex(), "第二页页码应为2");

        assertEquals(firstPageResult.getTotal(), secondPageResult.getTotal(), "两页的总记录数应相同");

        log.info("分页测试结果 - 第一页记录数: {}, 第二页记录数: {}, 总记录数: {}", firstPageResult.getRecords().size(), secondPageResult
            .getRecords()
            .size(), firstPageResult.getTotal());

        // 验证不同页的商品不重复（如果有足够的数据）
        if (!firstPageResult.getRecords().isEmpty() && !secondPageResult.getRecords().isEmpty()) {
            List<Long> firstPageIds = firstPageResult.getRecords().stream().map(ProductInfoDTO::getId).toList();
            List<Long> secondPageIds = secondPageResult.getRecords().stream().map(ProductInfoDTO::getId).toList();

            // 检查是否有重复的商品ID
            boolean hasOverlap = firstPageIds.stream().anyMatch(secondPageIds::contains);
            assertFalse(hasOverlap, "不同页的商品ID不应重复");
        }
    }

    @Test
    @DisplayName("集成测试 - 搜索参数边界值测试")
    void testSearchProductInfoListSync_BoundaryValues() {
        log.info("=== 测试搜索参数边界值 ===");

        // 测试最小页码和页大小
        ProductSearchRequestDTO minRequest = ProductSearchRequestDTO.builder()
            .keyword("测试")
            .language(LanguageEnum.EN)
            .page(1)
            .pageSize(1)
            .build();

        PageDTO<ProductInfoDTO> minResult = pdcProductMappingRepository.searchProductInfoListSync(minRequest);
        assertNotNull(minResult, "最小参数搜索结果不应为空");
        assertTrue(minResult.getRecords().size() <= 1, "最小页大小搜索结果应不超过1条");

        // 测试较大页大小
        ProductSearchRequestDTO maxRequest = ProductSearchRequestDTO.builder()
            .keyword("测试")
            .language(LanguageEnum.EN)
            .page(1)
            .pageSize(50)
            .build();

        PageDTO<ProductInfoDTO> maxResult = pdcProductMappingRepository.searchProductInfoListSync(maxRequest);
        assertNotNull(maxResult, "大页大小搜索结果不应为空");
        assertTrue(maxResult.getRecords().size() <= 50, "大页大小搜索结果应不超过50条");

        log.info("边界值测试完成 - 最小页大小结果: {}条, 大页大小结果: {}条", minResult.getRecords().size(), maxResult.getRecords().size());
    }

    // ======================== searchSimilarProductsSync 测试 ========================

    @Test
    @DisplayName("集成测试 - 搜索相似商品并同步入库")
    void testSearchSimilarProductsSync() {
        log.info("=== 测试搜索相似商品并同步入库 ===");

        // 使用一个真实的商品ID进行测试
        Long testOfferId = 43838984254L;
        LanguageEnum language = LanguageEnum.EN;
        Integer pageIndex = 1;
        Integer pageSize = 10;

        log.info("相似商品搜索参数 - offerId: {}, 语言: {}, 页码: {}, 每页大小: {}", testOfferId, language, pageIndex, pageSize);

        // 执行相似商品搜索
        long startTime = System.currentTimeMillis();
        PageDTO<ProductInfoDTO> result = pdcProductMappingRepository
            .searchSimilarProductsSync(testOfferId, language, pageIndex, pageSize);
        long endTime = System.currentTimeMillis();

        log.info("相似商品搜索完成，耗时: {}ms", (endTime - startTime));

        // 验证搜索结果
        assertNotNull(result, "相似商品搜索结果不应为空");
        assertNotNull(result.getRecords(), "相似商品记录列表不应为空");
        assertTrue(result.getTotal() >= 0, "总记录数应大于等于0");
        assertEquals(pageIndex, result.getPageIndex(), "页码应匹配");
        assertTrue(result.getPageSize() <= pageSize, "每页大小应小于等于请求值");

        log.info("相似商品搜索结果 - 总记录数: {}, 当前页记录数: {}, 页码: {}, 每页大小: {}", result.getTotal(), result.getRecords()
            .size(), result.getPageIndex(), result.getPageSize());

        // 验证相似商品信息
        if (!result.getRecords().isEmpty()) {
            ProductInfoDTO firstSimilarProduct = result.getRecords().get(0);
            assertNotNull(firstSimilarProduct.getId(), "相似商品ID不应为空");
            assertNotNull(firstSimilarProduct.getName(), "相似商品名称不应为空");
            assertNotNull(firstSimilarProduct.getPlatformCode(), "相似商品平台代码不应为空");

            log.info("第一个相似商品 - ID: {}, 名称: {}, 平台: {}, 价格: {}", firstSimilarProduct.getId(), firstSimilarProduct
                .getName(), firstSimilarProduct.getPlatformCode(), firstSimilarProduct.getPrice());
        }
    }

    @Test
    @DisplayName("集成测试 - 相似商品搜索分页测试")
    void testSearchSimilarProductsSync_Pagination() {
        log.info("=== 测试相似商品搜索分页 ===");

        Long testOfferId = 43838984254L;
        LanguageEnum language = LanguageEnum.EN;
        int pageSize = 5;

        // 第一页
        PageDTO<ProductInfoDTO> firstPage = pdcProductMappingRepository
            .searchSimilarProductsSync(testOfferId, language, 1, pageSize);

        // 第二页
        PageDTO<ProductInfoDTO> secondPage = pdcProductMappingRepository
            .searchSimilarProductsSync(testOfferId, language, 2, pageSize);

        // 验证分页结果
        assertNotNull(firstPage, "第一页结果不应为空");
        assertNotNull(secondPage, "第二页结果不应为空");

        assertEquals(1, firstPage.getPageIndex(), "第一页页码应为1");
        assertEquals(2, secondPage.getPageIndex(), "第二页页码应为2");

        log.info("相似商品分页测试 - 第一页: {}条, 第二页: {}条", firstPage.getRecords().size(), secondPage.getRecords().size());
    }

    // ======================== aggregateSearchSync 测试 ========================

    @Test
    @DisplayName("集成测试 - 聚合搜索（关键词搜索）")
    void testAggregateSearchSync_KeywordSearch() {
        log.info("=== 测试聚合搜索 - 关键词搜索 ===");

        // 准备关键词搜索请求
        AggregateSearchReq keywordSearchReq = AggregateSearchReq.builder()
            .searchType(SearchType.KEYWORD.getValue()) // searchType - 关键词搜索
            .keyword("笔记本电脑") // keyword
            .imageId(null) // imageId
            .page(1) // page
            .pageSize(10) // pageSize
            .categoryId(50000652L) // categoryId - 电脑、办公类目
            .categoryIdList(null) // categoryIdList
            .minPrice("1000") // minPrice
            .maxPrice("10000") // maxPrice
            .sortField("price") // sortField
            .sortOrder("asc") // sortOrder
            .filter("shipIn24Hours") // filter
            .snId(null) // snId
            .keywordTranslate(false) // keywordTranslate
            .build();

        log.info("聚合搜索参数 - 类型: 关键词搜索, 关键词: {}, 类目ID: {}", keywordSearchReq.getKeyword(), keywordSearchReq
            .getCategoryId());

        // 执行聚合搜索
        long startTime = System.currentTimeMillis();
        PageDTO<ProductInfoDTO> result = pdcProductMappingRepository.aggregateSearchSync(keywordSearchReq);
        long endTime = System.currentTimeMillis();

        log.info("聚合搜索（关键词）完成，耗时: {}ms", (endTime - startTime));

        // 验证搜索结果
        assertNotNull(result, "聚合搜索结果不应为空");
        assertNotNull(result.getRecords(), "搜索记录列表不应为空");
        assertTrue(result.getTotal() >= 0, "总记录数应大于等于0");
        assertEquals(1, result.getPageIndex(), "页码应为1");

        log.info("聚合搜索结果 - 总记录数: {}, 当前页记录数: {}", result.getTotal(), result.getRecords().size());

        // 验证商品信息
        if (!result.getRecords().isEmpty()) {
            ProductInfoDTO product = result.getRecords().get(0);
            assertNotNull(product.getId(), "商品ID不应为空");
            assertNotNull(product.getName(), "商品名称不应为空");

            log.info("聚合搜索第一个商品 - ID: {}, 名称: {}, 价格: {}", product.getId(), product.getName(), product.getPrice());
        }
    }

    @Test
    @DisplayName("集成测试 - 聚合搜索（图片搜索）")
    void testAggregateSearchSync_ImageSearch() {
        log.info("=== 测试聚合搜索 - 图片搜索 ===");

        // 准备图片搜索请求
        // 注意：这里需要一个真实的图片ID，可能需要先上传图片获取ID
        AggregateSearchReq imageSearchReq = AggregateSearchReq.builder()
            .searchType(SearchType.IMAGE.getValue()) // searchType - 图片搜索
            .keyword("手机") // keyword - 可选的关键词过滤
            .imageId("1629408464299626672") // imageId - 这里使用测试图片ID
            .page(1) // page
            .pageSize(10) // pageSize
            .categoryId(null) // categoryId
            .categoryIdList(null) // categoryIdList
            .minPrice("500") // minPrice
            .maxPrice("5000") // maxPrice
            .sortField(null) // sortField
            .sortOrder(null) // sortOrder
            .filter(null) // filter
            .snId(null) // snId
            .keywordTranslate(null) // keywordTranslate
            .build();

        log.info("聚合搜索参数 - 类型: 图片搜索, 图片ID: {}, 关键词: {}", imageSearchReq.getImageId(), imageSearchReq.getKeyword());

        try {
            // 执行聚合搜索
            long startTime = System.currentTimeMillis();
            PageDTO<ProductInfoDTO> result = pdcProductMappingRepository.aggregateSearchSync(imageSearchReq);
            long endTime = System.currentTimeMillis();

            log.info("聚合搜索（图片）完成，耗时: {}ms", (endTime - startTime));

            // 验证搜索结果
            assertNotNull(result, "图片搜索结果不应为空");
            assertNotNull(result.getRecords(), "搜索记录列表不应为空");
            assertTrue(result.getTotal() >= 0, "总记录数应大于等于0");

            log.info("图片搜索结果 - 总记录数: {}, 当前页记录数: {}", result.getTotal(), result.getRecords().size());

        } catch (Exception e) {
            log.warn("图片搜索测试失败，可能是因为测试图片ID无效: {}", e.getMessage());
            // 图片搜索可能因为测试环境限制而失败，这是可以接受的
            assertTrue(e.getMessage().contains("图片") || e.getMessage().contains("imageId"), "异常信息应该与图片搜索相关");
        }
    }

    @Test
    @DisplayName("集成测试 - 聚合搜索多种分类组合")
    void testAggregateSearchSync_MultipleCategoriesSearch() {
        log.info("=== 测试聚合搜索 - 多种分类组合 ===");

        // 准备多分类搜索请求
        List<Long> categoryIds = Arrays.asList(50000652L, 50000653L, 50000654L); // 多个类目ID

        AggregateSearchReq multiCategoryReq = AggregateSearchReq.builder()
            .searchType(SearchType.KEYWORD.getValue()) // searchType - 关键词搜索
            .keyword("数码产品") // keyword
            .page(1) // page
            .pageSize(15) // pageSize
            .categoryIdList(categoryIds) // categoryIdList
            .minPrice("100") // minPrice
            .maxPrice("8000") // maxPrice
            .sortField("monthSold") // sortField
            .sortOrder("desc") // sortOrder
            .build();

        log.info("多分类搜索参数 - 关键词: {}, 分类数量: {}, 分类ID: {}", multiCategoryReq.getKeyword(), categoryIds
            .size(), categoryIds);

        // 执行搜索
        PageDTO<ProductInfoDTO> result = pdcProductMappingRepository.aggregateSearchSync(multiCategoryReq);

        // 验证结果
        assertNotNull(result, "多分类搜索结果不应为空");
        assertTrue(result.getTotal() >= 0, "总记录数应大于等于0");

        log.info("多分类搜索结果 - 总记录数: {}, 当前页记录数: {}", result.getTotal(), result.getRecords().size());
    }

    // ======================== 异常情况测试 ========================

    @Test
    @DisplayName("异常测试 - 空关键词搜索")
    void testSearchProductInfoListSync_EmptyKeyword() {
        log.info("=== 测试空关键词搜索异常处理 ===");

        ProductSearchRequestDTO emptyKeywordRequest = ProductSearchRequestDTO.builder()
            .keyword("") // 空关键词
            .language(LanguageEnum.EN)
            .page(1)
            .pageSize(10)
            .build();

        // 应该抛出异常或返回空结果
        assertThrows(Exception.class, () -> {
            pdcProductMappingRepository.searchProductInfoListSync(emptyKeywordRequest);
        }, "空关键词应该抛出异常");
    }

    @Test
    @DisplayName("异常测试 - 聚合搜索无效搜索类型")
    void testAggregateSearchSync_InvalidSearchType() {
        log.info("=== 测试聚合搜索无效搜索类型 ===");

        AggregateSearchReq invalidTypeReq = AggregateSearchReq.builder()
            .searchType(SearchType.KEYWORD.getValue()) // searchType - 无效的搜索类型
            .keyword("测试") // keyword
            .imageId(null) // imageId
            .page(1) // page
            .pageSize(10) // pageSize
            .build();

        // 应该抛出异常
        assertThrows(Exception.class, () -> {
            pdcProductMappingRepository.aggregateSearchSync(invalidTypeReq);
        }, "无效搜索类型应该抛出异常");
    }

    @Test
    @DisplayName("异常测试 - 聚合搜索缺少必要参数")
    void testAggregateSearchSync_MissingRequiredParams() {
        log.info("=== 测试聚合搜索缺少必要参数 ===");

        // 关键词搜索但缺少关键词
        AggregateSearchReq missingKeywordReq = AggregateSearchReq.builder()
            .searchType(SearchType.KEYWORD.getValue()) // searchType
            .keyword(null) // keyword - 缺少关键词
            .imageId(null) // imageId
            .page(1) // page
            .pageSize(10) // pageSize
            .build();

        assertThrows(Exception.class, () -> {
            pdcProductMappingRepository.aggregateSearchSync(missingKeywordReq);
        }, "关键词搜索缺少关键词应该抛出异常");

        // 图片搜索但缺少图片ID
        AggregateSearchReq missingImageIdReq = AggregateSearchReq.builder()
            .searchType(SearchType.IMAGE.getValue()) // searchType
            .keyword("测试") // keyword
            .imageId(null) // imageId - 缺少图片ID
            .page(1) // page
            .pageSize(10) // pageSize
            .build();

        assertThrows(Exception.class, () -> {
            pdcProductMappingRepository.aggregateSearchSync(missingImageIdReq);
        }, "图片搜索缺少图片ID应该抛出异常");
    }

    @Test
    @DisplayName("性能测试 - 大批量搜索")
    void testSearchPerformance() {
        log.info("=== 搜索性能测试 ===");

        ProductSearchRequestDTO performanceRequest = ProductSearchRequestDTO.builder()
            .keyword("手机")
            .language(LanguageEnum.EN)
            .page(1)
            .pageSize(50) // 较大的页大小
            .build();

        // 执行多次搜索测试性能
        long totalTime = 0;
        int testRounds = 3;

        for (int i = 0; i < testRounds; i++) {
            long startTime = System.currentTimeMillis();
            PageDTO<ProductInfoDTO> result = pdcProductMappingRepository.searchProductInfoListSync(performanceRequest);
            long endTime = System.currentTimeMillis();

            long roundTime = endTime - startTime;
            totalTime += roundTime;

            log.info("第{}轮搜索耗时: {}ms, 结果数量: {}", (i + 1), roundTime, result.getRecords().size());

            assertNotNull(result, "性能测试结果不应为空");
        }

        long avgTime = totalTime / testRounds;
        log.info("性能测试完成 - 平均耗时: {}ms, 总耗时: {}ms", avgTime, totalTime);

        // 性能断言：平均响应时间应在合理范围内（比如10秒以内）
        assertTrue(avgTime < 10000, "平均搜索时间应在10秒以内");
    }

}
