/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.common.context;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.fulfillmen.starter.core.util.ExceptionUtils;
import com.fulfillmen.starter.core.util.IpUtils;
import com.fulfillmen.starter.web.util.ServletUtils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户额外上下文
 *
 * <AUTHOR>
 * @date 2025/4/27 14:24
 * @description: todo
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserExtraContext implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * IP
     */
    private String ip;

    /**
     * IP 归属地
     */
    private String address;

    /**
     * 浏览器
     */
    private String browser;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 登录时间
     */
    private LocalDateTime loginTime;

    /**
     * 构建用户额外上下文
     * <p>
     * 已废弃，请使用 {@link #build(HttpServletRequest)} 方法替代。
     * </p>
     *
     * @param request 请求
     */
    @Deprecated
    public UserExtraContext(HttpServletRequest request) {
        this.ip = JakartaServletUtil.getClientIP(request);
        this.address = ExceptionUtils.exToNull(() -> IpUtils.getIpv4Address(this.ip));
        this.setBrowser(ServletUtils.getBrowser(request));
        this.setLoginTime(LocalDateTime.now());
        this.setOs(StrUtil.subBefore(ServletUtils.getOs(request), " or", false));
    }

    /**
     * 构建用户额外上下文
     *
     * @param request 请求
     * @return 用户额外上下文
     */
    public static UserExtraContext build(HttpServletRequest request) {
        return UserExtraContext.builder()
            .ip(JakartaServletUtil.getClientIP(request))
            .address(ExceptionUtils.exToNull(() -> IpUtils.getIpv4Address(JakartaServletUtil.getClientIP(request))))
            .browser(ServletUtils.getBrowser(request))
            .loginTime(LocalDateTime.now())
            .os(StrUtil.subBefore(ServletUtils.getOs(request), " or", false))
            .build();
    }

}
