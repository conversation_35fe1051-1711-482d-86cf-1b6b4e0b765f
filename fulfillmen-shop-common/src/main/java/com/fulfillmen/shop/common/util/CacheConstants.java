/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.common.util;

import com.fulfillmen.starter.core.constant.StringConstants;

/**
 * 缓存相关常量
 *
 * <AUTHOR>
 * @date 2025年02月15日22:55:15
 */
public class CacheConstants {

    /**
     * 分隔符
     */
    public static final String DELIMITER = StringConstants.COLON;

    /**
     * 验证码键前缀
     */
    public static final String CAPTCHA_KEY_PREFIX = "CAPTCHA" + DELIMITER;

    /**
     * 用户缓存键前缀
     */
    public static final String USER_KEY_PREFIX = "USER" + DELIMITER;
    /**
     * 用户密码错误次数缓存键前缀
     */
    public static final String USER_PASSWORD_ERROR_KEY_PREFIX = USER_KEY_PREFIX + "PASSWORD_ERROR" + DELIMITER;
    /**
     * 菜单缓存键前缀
     */
    public static final String MENU_KEY_PREFIX = "MENU" + DELIMITER;
    /**
     * 字典缓存键前缀
     */
    public static final String DICT_KEY_PREFIX = "DICT" + DELIMITER;
    /**
     * 参数缓存键前缀
     */
    public static final String OPTION_KEY_PREFIX = "OPTION" + DELIMITER;
    /**
     * 仪表盘缓存键前缀
     */
    public static final String DASHBOARD_KEY_PREFIX = "DASHBOARD" + DELIMITER;
    /**
     * 数据导入临时会话key
     */
    public static final String DATA_IMPORT_KEY = "SYSTEM" + DELIMITER + "DATA_IMPORT" + DELIMITER;

    /**
     * alibaba category 类目缓存
     */
    public static final String ALIBABA_CATEGORY = "alibaba" + DELIMITER + "category" + DELIMITER;
    /**
     * alibaba category 类目列表
     */
    public static final String ALIBABA_CATEGORY_MAP = "alibaba" + DELIMITER + "category" + DELIMITER + "map";
    /**
     * 账户缓存 key 前缀： openapi:account:access_key
     */
    public static final String OPENAPI_ACCOUNT_ACCESSKEY = "openapi" + DELIMITER + "account" + DELIMITER;
    /**
     * 汇率计算缓存key juhe:currency:rate
     */
    public static final String JUHE_CURRENCY_RATE = "juhe" + DELIMITER + "currency" + DELIMITER + "rate";

    /**
     * 产品DTO缓存前缀 product:dto:
     */
    public static final String PRODUCT_DTO = "product" + DELIMITER + "dto" + DELIMITER;

    /**
     * 产品详情缓存前缀 product:detail:
     */
    public static final String PRODUCT_DETAIL = "product" + DELIMITER + "detail" + DELIMITER;

    /**
     * 产品VO缓存前缀 product:vo:
     */
    public static final String PRODUCT_VO = "product" + DELIMITER + "vo" + DELIMITER;

    private CacheConstants() {
    }
}
