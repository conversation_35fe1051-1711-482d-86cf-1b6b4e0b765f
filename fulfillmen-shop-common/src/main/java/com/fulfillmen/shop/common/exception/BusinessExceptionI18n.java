/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.common.exception;

import com.fulfillmen.shop.common.enums.IErrorResult;
import com.fulfillmen.shop.common.exception.handler.GlobalExceptionHandler;
import com.fulfillmen.shop.common.util.I18nMessageUtils;
import com.fulfillmen.starter.core.exception.BusinessException;
import java.text.MessageFormat;
import java.util.Arrays;
import java.util.Locale;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 全局国际化业务异常
 *
 * <p>
 * 该异常用于承载国际化所需的信息（错误码、i18nKey、参数）， 最终的国际化消息转换由 {@link GlobalExceptionHandler} 处理。
 * </p>
 *
 * <AUTHOR>
 * @date 2025/6/10
 * @description: 全局国际化业务异常基类
 * @since 1.0.0
 */
@Slf4j
@Getter
public class BusinessExceptionI18n extends BusinessException {

    /**
     * 错误码
     */
    private final Integer errorCode;

    /**
     * 国际化键
     */
    private final String i18nKey;

    /**
     * 国际化参数
     */
    private final Object[] i18nArgs;

    /**
     * 语言环境
     */
    private final Locale locale;

    /**
     * 基础构造函数
     *
     * @param message 错误消息
     */
    public BusinessExceptionI18n(String message) {
        super(message);
        this.errorCode = null;
        this.i18nKey = null;
        this.i18nArgs = null;
        this.locale = I18nMessageUtils.getCurrentLocale();
    }

    /**
     * 完整构造函数
     *
     * @param errorCode 错误码
     * @param i18nKey   国际化键
     * @param i18nArgs  国际化参数
     * @param locale    语言环境
     */
    public BusinessExceptionI18n(Integer errorCode, String i18nKey, Object[] i18nArgs, Locale locale) {
        super(formatFallbackMessage(errorCode, i18nKey, i18nArgs));
        this.errorCode = errorCode;
        this.i18nKey = i18nKey;
        this.i18nArgs = i18nArgs;
        this.locale = locale;

        log.debug("创建国际化异常: errorCode={}, i18nKey={}, locale={}, args={}", errorCode, i18nKey, locale, Arrays
            .toString(i18nArgs));
    }

    /**
     * 工厂方法 - 基于错误结果枚举创建异常
     *
     * @param errorResult 错误结果枚举
     * @param args        国际化参数
     * @return 国际化异常实例
     */
    public static BusinessExceptionI18n of(IErrorResult errorResult, Object... args) {
        return of(errorResult.getI18nKey(), args);
    }

    /**
     * 工厂方法 - 基于错误结果枚举创建异常
     *
     * @param i18nKey 国际化键
     * @param args    国际化参数
     * @return 国际化异常实例
     */
    public static BusinessExceptionI18n of(String i18nKey, Object... args) {
        return new BusinessExceptionI18n(null, i18nKey, args, I18nMessageUtils
            .getCurrentLocale());
    }

    /**
     * 格式化回退消息
     *
     * @param errorCode 错误码
     * @param i18nKey   国际化键
     * @param args      参数
     * @return 格式化后的消息
     */
    private static String formatFallbackMessage(Integer errorCode, String i18nKey, Object[] args) {
        if (i18nKey == null) {
            return errorCode != null ? "Error code: " + errorCode : "Unknown error";
        }

        try {
            String template = "[" + errorCode + "] " + i18nKey;
            if (args != null && args.length > 0) {
                return MessageFormat.format(template + " (args: {0})", Arrays.toString(args));
            }
            return template;
        } catch (Exception e) {
            log.warn("回退消息格式化失败: i18nKey={}, args={}", i18nKey, Arrays.toString(args), e);
            return errorCode != null ? "Error code: " + errorCode : "Unknown error";
        }
    }
}
