/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.common.context;

import java.util.List;

import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;

import lombok.Builder;
import lombok.Getter;

/**
 * 采购订单上下文
 *
 * <pre>
 * 采购订单上下文用于传递采购订单信息，如采购订单、店铺/供应商订单、订单商品、购物车ID等。
 * TODO: 提供后续状态机设计流转使用
 * </pre>
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Builder
@Getter
public class OrderContext {

    /**
     * 采购订单 (主订单)
     */
    private final TzOrderPurchase purchaseOrder;
    /**
     * 店铺/供应商订单 (子订单)
     */
    private final List<TzOrderSupplier> supplierOrders;
    /**
     * 订单商品 (订单项)
     */
    private final List<TzOrderItem> orderItems;

    /**
     * 购物车ID
     *
     * <pre>
     * 如果订单来自购物车，则需要传入购物车ID
     * 如果订单来自商品详情页，则不需要传入购物车ID
     * </pre>
     */
    private final List<Long> shoppingCartIds;

}
