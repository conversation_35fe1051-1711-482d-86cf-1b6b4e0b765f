/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.common.context;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Builder;

/**
 * 用户上下文
 *
 * <AUTHOR>
 * @date 2025/4/27 14:17
 * @description: todo
 * @since 1.0.0
 */
@Builder
public record UserContext(
    /**
     * ID
     */
    Long id,
    /**
     * 用户名
     */
    String username,
    /**
     * wms code 客户编码
     */
    String wmsCusCode,
    /**
     * wms 用户api key
     */
    String wmsApiKey,
    /**
     * 所属租户 ID
     */
    Long tenantId,
    /**
     * 最后一次修改密码时间
     */
    LocalDateTime pwdResetTime,
    /**
     * 登录时系统设置的密码过期天数
     */
    Integer passwordExpirationDays
) implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    // 编写重载
    public UserContext(Long id, String username) {
        this(id, username, null, null, 0L, null, null);
    }

    public UserContext(Long id, String username, String wmsCusCode, String wmsApiKey, Long tenantId) {
        this(id, username, wmsCusCode, wmsApiKey, tenantId, null, null);
    }

}
