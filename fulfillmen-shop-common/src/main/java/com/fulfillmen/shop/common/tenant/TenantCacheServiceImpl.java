/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.common.tenant;

import com.fulfillmen.starter.cache.redisson.util.RedisUtils;
import java.time.Duration;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 租户缓存服务实现类
 * 使用 RedisUtils 进行缓存操作，支持 15 分钟命中策略
 *
 * <AUTHOR>
 * @date 2025/7/3 11:00
 * @description: 租户缓存服务实现类
 * @since 2025-07-03
 */
@Slf4j
@Service
public class TenantCacheServiceImpl implements TenantCacheService {

    /**
     * 租户缓存键前缀
     */
    private static final String TENANT_CACHE_KEY_PREFIX = "tenant:context:";

    /**
     * 缓存统计计数器
     */
    private final AtomicLong hitCount = new AtomicLong(0);
    private final AtomicLong missCount = new AtomicLong(0);
    private final AtomicLong setCount = new AtomicLong(0);
    private final AtomicLong deleteCount = new AtomicLong(0);
    private final AtomicLong refreshCount = new AtomicLong(0);

    @Override
    public Optional<EnhancedTenantContext> getTenantContext(String tenantId) {
        try {
            String cacheKey = buildCacheKey(tenantId);
            Optional<EnhancedTenantContext> result = RedisUtils.get(cacheKey);

            if (result.isPresent()) {
                hitCount.incrementAndGet();
                EnhancedTenantContext context = result.get();

                // 更新访问信息
                context.updateAccessInfo();

                // 检查是否需要刷新缓存时间（15分钟内有访问则延长缓存时间）
                if (context.shouldRefreshCache()) {
                    refreshTenantContext(tenantId);
                }

                log.debug("租户缓存命中: {}", tenantId);
                return Optional.of(context);
            } else {
                missCount.incrementAndGet();
                log.debug("租户缓存未命中: {}", tenantId);
                return Optional.empty();
            }
        } catch (Exception e) {
            log.error("获取租户缓存失败: {}", tenantId, e);
            missCount.incrementAndGet();
            return Optional.empty();
        }
    }

    @Override
    public void setTenantContext(String tenantId, EnhancedTenantContext tenantContext, long timeout) {
        try {
            String cacheKey = buildCacheKey(tenantId);

            // 设置缓存元数据
            if (tenantContext.getCacheMetadata() == null) {
                tenantContext.setCacheMetadata(new EnhancedTenantContext.CacheMetadata());
            }

            var metadata = tenantContext.getCacheMetadata();
            metadata.setCacheCreatedTime(java.time.LocalDateTime.now());
            metadata.setLastAccessTime(java.time.LocalDateTime.now());
            metadata.setAccessCount(1L);
            metadata.setCacheVersion("1.0");
            metadata.setNeedRefresh(false);

            RedisUtils.set(cacheKey, tenantContext, Duration.ofSeconds(timeout));
            setCount.incrementAndGet();

            log.debug("租户缓存设置成功: {}, 过期时间: {}秒", tenantId, timeout);
        } catch (Exception e) {
            log.error("设置租户缓存失败: {}", tenantId, e);
            // 不抛出异常，避免影响业务流程
        }
    }

    @Override
    public boolean refreshTenantContext(String tenantId) {
        try {
            String cacheKey = buildCacheKey(tenantId);

            // 检查缓存是否存在
            if (!RedisUtils.exists(cacheKey)) {
                log.debug("租户缓存不存在，无法刷新: {}", tenantId);
                return false;
            }

            // 延长缓存时间
            RedisUtils.expire(cacheKey, Duration.ofSeconds(CACHE_EXTEND_TIMEOUT));
            refreshCount.incrementAndGet();

            log.debug("租户缓存刷新成功: {}, 延长时间: {}秒", tenantId, CACHE_EXTEND_TIMEOUT);
            return true;
        } catch (Exception e) {
            log.error("刷新租户缓存失败: {}", tenantId, e);
            return false;
        }
    }

    @Override
    public boolean deleteTenantContext(String tenantId) {
        try {
            String cacheKey = buildCacheKey(tenantId);
            boolean deleted = RedisUtils.delete(cacheKey);

            if (deleted) {
                deleteCount.incrementAndGet();
                log.debug("租户缓存删除成功: {}", tenantId);
            } else {
                log.debug("租户缓存不存在，无需删除: {}", tenantId);
            }

            return deleted;
        } catch (Exception e) {
            log.error("删除租户缓存失败: {}", tenantId, e);
            return false;
        }
    }

    @Override
    public boolean existsTenantContext(String tenantId) {
        try {
            String cacheKey = buildCacheKey(tenantId);
            return RedisUtils.exists(cacheKey);
        } catch (Exception e) {
            log.error("检查租户缓存存在性失败: {}", tenantId, e);
            return false;
        }
    }

    @Override
    public CacheStatistics getCacheStatistics() {
        long totalHits = hitCount.get();
        long totalMisses = missCount.get();
        double hitRate = (totalHits + totalMisses) > 0 ? (double) totalHits / (totalHits + totalMisses) : 0.0;

        return new CacheStatistics(
            totalHits,
            totalMisses,
            setCount.get(),
            deleteCount.get(),
            refreshCount.get(),
            hitRate
        );
    }

    /**
     * 构建缓存键
     *
     * @param tenantId 租户ID
     * @return 缓存键
     */
    private String buildCacheKey(String tenantId) {
        return TENANT_CACHE_KEY_PREFIX + tenantId;
    }
}
