/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.common.tenant;

import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

/**
 * 租户上下文使用示例
 * 演示如何在业务代码中使用增强的租户上下文功能
 *
 * <AUTHOR>
 * @date 2025/7/3 11:00
 * @description: 租户上下文使用示例
 * @since 2025-07-03
 */
@Slf4j
public class TenantContextExample {

    /**
     * 示例1: 获取基本租户信息
     */
    public void exampleBasicTenantInfo() {
        // 获取当前租户ID
        String tenantId = EnhancedTenantContextHolder.getCurrentTenantId();
        if (tenantId == null) {
            log.warn("当前请求没有租户上下文");
            return;
        }

        // 获取租户名称
        String tenantName = EnhancedTenantContextHolder.getCurrentTenantName();
        log.info("当前租户: ID={}, 名称={}", tenantId, tenantName);

        // 获取主域名
        String primaryDomain = EnhancedTenantContextHolder.getCurrentPrimaryDomain();
        log.info("主域名: {}", primaryDomain);
    }

    /**
     * 示例2: 获取租户仓库信息
     */
    public void exampleWarehouseInfo() {
        // 获取默认仓库
        EnhancedTenantContext.TenantWarehouseInfo defaultWarehouse = EnhancedTenantContextHolder.getCurrentDefaultWarehouse();

        if (defaultWarehouse != null) {
            log.info("默认仓库: {}, 地址: {}",
                defaultWarehouse.getName(),
                defaultWarehouse.getAddress());
        } else {
            log.warn("租户未配置默认仓库");
        }
    }

    /**
     * 示例3: 检查套餐状态
     */
    public boolean examplePlanValidation() {
        // 检查套餐是否过期
        boolean isExpired = EnhancedTenantContextHolder.isCurrentTenantPlanExpired();

        if (isExpired) {
            log.error("租户套餐已过期，无法执行业务操作");
            return false;
        }

        log.info("租户套餐有效，可以继续业务操作");
        return true;
    }

    /**
     * 示例4: 获取完整租户上下文
     */
    public void exampleFullTenantContext() {
        Optional<EnhancedTenantContext> contextOpt = EnhancedTenantContextHolder.getEnhancedTenantContext();

        if (!contextOpt.isPresent()) {
            log.warn("租户上下文不存在");
            return;
        }

        EnhancedTenantContext context = contextOpt.get();

        // 获取基础信息
        EnhancedTenantContext.TenantBasicInfo basicInfo = context.getBasicInfo();
        if (basicInfo != null) {
            log.info("租户基础信息: 用户名={}, 邮箱={}, 状态={}",
                basicInfo.getUsername(),
                basicInfo.getEmail(),
                basicInfo.getStatus());
        }

        // 获取套餐信息
        EnhancedTenantContext.TenantPlanInfo planInfo = context.getPlanInfo();
        if (planInfo != null) {
            log.info("套餐信息: 名称={}, 最大用户数={}, 最大商品数={}",
                planInfo.getPlanName(),
                planInfo.getMaxUsers(),
                planInfo.getMaxProducts());
        }

        // 获取所有仓库
        List<EnhancedTenantContext.TenantWarehouseInfo> warehouses = context.getWarehouses();
        if (warehouses != null && !warehouses.isEmpty()) {
            log.info("租户共有 {} 个仓库", warehouses.size());
            warehouses.forEach(warehouse -> log.info("仓库: {} ({})", warehouse.getName(), warehouse.getWarehouseCode()));
        }

        // 获取所有域名
        List<EnhancedTenantContext.TenantDomainInfo> domains = context.getDomains();
        if (domains != null && !domains.isEmpty()) {
            log.info("租户共有 {} 个域名", domains.size());
            domains.forEach(domain -> log.info("域名: {} (主域名: {})", domain.getDomainName(), domain.getIsPrimary()));
        }
    }

    /**
     * 示例5: 业务服务中的使用
     * 模拟订单创建服务
     */
    public void exampleCreateOrder() {
        try {
            // 1. 检查租户上下文是否存在
            if (!EnhancedTenantContextHolder.hasEnhancedTenantContext()) {
                throw new RuntimeException("租户上下文不存在，无法创建订单");
            }

            // 2. 检查套餐状态
            if (EnhancedTenantContextHolder.isCurrentTenantPlanExpired()) {
                throw new RuntimeException("租户套餐已过期，无法创建订单");
            }

            // 3. 获取默认仓库
            EnhancedTenantContext.TenantWarehouseInfo warehouse = EnhancedTenantContextHolder.getCurrentDefaultWarehouse();
            if (warehouse == null) {
                throw new RuntimeException("租户未配置默认仓库，无法创建订单");
            }

            // 4. 获取租户信息
            String tenantId = EnhancedTenantContextHolder.getCurrentTenantId();
            String tenantName = EnhancedTenantContextHolder.getCurrentTenantName();

            // 5. 模拟创建订单
            log.info("为租户 {}({}) 在仓库 {}({}) 创建订单",
                tenantName, tenantId,
                warehouse.getName(), warehouse.getWarehouseCode());

            // 这里可以进行实际的订单创建逻辑
            // orderService.createOrder(...);

        } catch (Exception e) {
            log.error("创建订单失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 示例6: 批量操作中的优化使用
     * 模拟批量处理商品
     */
    public void exampleBatchProcess() {
        // 一次性获取租户上下文，避免重复获取
        Optional<EnhancedTenantContext> contextOpt = EnhancedTenantContextHolder.getEnhancedTenantContext();

        if (!contextOpt.isPresent()) {
            log.warn("租户上下文不存在，跳过批量处理");
            return;
        }

        EnhancedTenantContext context = contextOpt.get();
        String tenantId = context.getTenantId();

        // 获取套餐限制
        EnhancedTenantContext.TenantPlanInfo planInfo = context.getPlanInfo();
        int maxProducts = planInfo != null ? planInfo.getMaxProducts() : 1000;

        log.info("租户 {} 最多可以有 {} 个商品", tenantId, maxProducts);

        // 模拟批量处理
        for (int i = 1; i <= 100; i++) {
            // 在循环中不再重复获取租户上下文
            log.debug("处理商品 {}/{} for 租户 {}", i, 100, tenantId);

            // 检查是否超过套餐限制
            if (i > maxProducts) {
                log.warn("商品数量超过套餐限制 {}", maxProducts);
                break;
            }

            // 模拟商品处理逻辑
            // productService.processProduct(...);
        }
    }

    /**
     * 示例7: 租户配置验证
     */
    public boolean exampleTenantValidation() {
        Optional<EnhancedTenantContext> contextOpt = EnhancedTenantContextHolder.getEnhancedTenantContext();

        if (!contextOpt.isPresent()) {
            log.error("租户上下文验证失败: 上下文不存在");
            return false;
        }

        EnhancedTenantContext context = contextOpt.get();

        // 验证基础信息
        if (context.getBasicInfo() == null) {
            log.error("租户基础信息缺失");
            return false;
        }

        // 验证套餐信息
        if (context.getPlanInfo() == null) {
            log.error("租户套餐信息缺失");
            return false;
        }

        // 验证仓库配置
        if (context.getWarehouses() == null || context.getWarehouses().isEmpty()) {
            log.error("租户未配置仓库");
            return false;
        }

        // 验证域名配置
        if (context.getDomains() == null || context.getDomains().isEmpty()) {
            log.warn("租户未配置自定义域名，将使用默认域名");
        }

        log.info("租户配置验证通过");
        return true;
    }

    /**
     * 示例8: 缓存统计信息查看
     */
    public void exampleCacheStatistics() {
        Optional<EnhancedTenantContext> contextOpt = EnhancedTenantContextHolder.getEnhancedTenantContext();

        if (contextOpt.isPresent()) {
            EnhancedTenantContext context = contextOpt.get();
            EnhancedTenantContext.CacheMetadata cacheMetadata = context.getCacheMetadata();

            if (cacheMetadata != null) {
                log.info("缓存统计: 创建时间={}, 最后访问时间={}, 访问次数={}, 版本={}",
                    cacheMetadata.getCacheCreatedTime(),
                    cacheMetadata.getLastAccessTime(),
                    cacheMetadata.getAccessCount(),
                    cacheMetadata.getCacheVersion());
            }
        }
    }
}
