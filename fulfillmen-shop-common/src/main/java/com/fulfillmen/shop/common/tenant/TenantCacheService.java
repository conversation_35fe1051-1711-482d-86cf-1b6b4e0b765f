/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.common.tenant;

import java.util.Optional;

/**
 * 租户缓存服务接口
 * 定义租户信息缓存的标准操作
 *
 * <AUTHOR>
 * @date 2025/7/3 11:00
 * @description: 租户缓存服务接口
 * @since 2025-07-03
 */
public interface TenantCacheService {

    /**
     * 默认缓存时间（秒）- 4小时
     */
    long DEFAULT_CACHE_TIMEOUT = 4 * 60 * 60;

    /**
     * 缓存刷新阈值（分钟）- 15分钟
     */
    long CACHE_REFRESH_THRESHOLD_MINUTES = 15;

    /**
     * 缓存延长时间（秒）- 2小时
     */
    long CACHE_EXTEND_TIMEOUT = 2 * 60 * 60;

    /**
     * 获取租户缓存信息
     *
     * @param tenantId 租户ID
     * @return 增强的租户上下文，如果不存在则返回空
     */
    Optional<EnhancedTenantContext> getTenantContext(String tenantId);

    /**
     * 设置租户缓存信息
     *
     * @param tenantId      租户ID
     * @param tenantContext 增强的租户上下文
     * @param timeout       超时时间（秒）
     */
    void setTenantContext(String tenantId, EnhancedTenantContext tenantContext, long timeout);

    /**
     * 设置租户缓存信息（使用默认超时时间）
     *
     * @param tenantId      租户ID
     * @param tenantContext 增强的租户上下文
     */
    default void setTenantContext(String tenantId, EnhancedTenantContext tenantContext) {
        setTenantContext(tenantId, tenantContext, DEFAULT_CACHE_TIMEOUT);
    }

    /**
     * 刷新租户缓存（延长过期时间）
     *
     * @param tenantId 租户ID
     * @return 是否刷新成功
     */
    boolean refreshTenantContext(String tenantId);

    /**
     * 删除租户缓存
     *
     * @param tenantId 租户ID
     * @return 是否删除成功
     */
    boolean deleteTenantContext(String tenantId);

    /**
     * 检查租户缓存是否存在
     *
     * @param tenantId 租户ID
     * @return 是否存在
     */
    boolean existsTenantContext(String tenantId);

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    CacheStatistics getCacheStatistics();

    /**
     * 缓存统计信息
     */
    record CacheStatistics(
        long hitCount,
        long missCount,
        long setCount,
        long deleteCount,
        long refreshCount,
        double hitRate
    ) {

        /**
         * 计算命中率
         */
        public double calculateHitRate() {
            long totalRequests = hitCount + missCount;
            return totalRequests > 0 ? (double) hitCount / totalRequests : 0.0;
        }
    }
}
