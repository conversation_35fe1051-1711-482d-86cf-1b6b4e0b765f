/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.common.tenant;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import com.fulfillmen.shop.domain.entity.TenantWarehouse;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 增强的租户上下文数据类
 * 包含完整的租户信息，用于缓存和业务处理
 *
 * <AUTHOR>
 * @date 2025/1/3 16:00
 * @description: 增强的租户上下文，包含租户完整信息
 * @since 1.0.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EnhancedTenantContext implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 租户基础信息
     */
    private TenantBasicInfo basicInfo;

    /**
     * 租户详细信息
     */
    private TenantDetailInfo detailInfo;

    /**
     * 当前套餐信息
     */
    private TenantPlanInfo planInfo;

    /**
     * 自定义域名列表
     */
    private List<TenantDomainInfo> domains;

    /**
     * OSS 文件存储路径列表
     */
    private List<TenantFileInfo> fileStoragePaths;

    /**
     * 租户佣金规则
     */
    private TenantCommissionInfo commissionInfo;

    /**
     * 仓库列表
     */
    private List<TenantWarehouseInfo> warehouses;

    /**
     * 本地化配置
     */
    private TenantLocaleInfo localeInfo;

    /**
     * 缓存相关信息
     */
    private CacheMetadata cacheMetadata;

    // ================================ 内部数据类 ================================

    /**
     * 租户基础信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TenantBasicInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        private Long id;
        private String domainPrefix;
        private String username;
        private String email;
        private String phone;
        private String avatar;
        private Short gender;
        private LocalDate birth;
        private LocalDateTime lastLoginTime;
        private Integer lastLoginCount;
        private String lastLoginIp;
        private String lastLoginLocation;
        private String lastLoginBrowser;
        private String lastLoginOs;
        private String lastLoginDevice;
        private String status;
        private LocalDateTime gmtCreated;
        private LocalDateTime gmtModified;
    }

    /**
     * 租户详细信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TenantDetailInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        private String phone;
        private String tel;
        private String companyName;
        private String country;
        private String province;
        private String city;
        private String address;
        private String idCard;
        private String businessLicense;
        private String postalCode;
        private Integer serviceFee;
        private String wmsCusId;
        private String wmsCusCode;
        private String wmsCusName;
        private String wmsApiKey;
        private String wmsApiUrl;
    }

    /**
     * 租户套餐信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TenantPlanInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        private Long planId;
        private String planName;
        private BigDecimal planPrice;
        private String planStatus;
        private LocalDateTime expiredTime;
        private Integer maxUsers;
        private Integer maxProducts;
        private Integer maxCategories;
        private String features;  // JSON 格式的功能权限配置
    }

    /**
     * 租户域名信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TenantDomainInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        private Long id;
        private String domainName;
        private String certificateInfo;
        private Boolean isPrimary;
        private Boolean verified;
    }

    /**
     * 租户文件存储信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TenantFileInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        private Long id;
        private String filePath;
        private String fileType;
    }

    /**
     * 租户佣金信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TenantCommissionInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        private Long id;
        private String commissionType;  // fixed 固定，tiered 阶梯式
        private BigDecimal fixedRate;
        private String tieredRules;  // JSON 格式的阶梯费率规则
        private LocalDate effectiveDate;
    }

    /**
     * 租户仓库信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TenantWarehouseInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;
        /**
         * 仓库ID
         */
        private Long id;
        /**
         * 仓库名称
         */
        private String name;
        /**
         * 仓库编码
         */
        private String warehouseCode;
        /**
         * 仓库描述
         */
        private String warehouseDesc;
        /**
         * 仓库图标
         */
        private String warehouseIcon;
        /**
         * 仓库颜色
         */
        private String warehouseColor;
        /**
         * 仓库类型
         * 1-国内仓, 2-海外仓
         */
        private String warehouseType;
        /**
         * 仓库排序
         */
        private Integer warehouseSort;
        /**
         * 仓库状态
         * 1-正常, 2-禁用
         */
        private String warehouseStatus;
        /**
         * 是否默认仓库
         */
        private Integer isDefault;
        /**
         * 国家
         */
        private String country;
        /**
         * 省份
         */
        private String province;
        /**
         * 城市
         */
        private String city;
        /**
         * 区县
         */
        private String district;
        /**
         * 区县编码
         */
        private String districtCode;
        /**
         * 详细地址
         */
        private String address;
        /**
         * 邮编
         */
        private String postcode;
        /**
         * 经度
         */
        private BigDecimal longitude;
        /**
         * 纬度
         */
        private BigDecimal latitude;
        /**
         * 联系人姓名
         */
        private String contactName;
        /**
         * 联系人电话
         */
        private String contactPhone;
        /**
         * 联系人邮箱
         */
        private String contactEmail;
        /**
         * 联系人手机
         */
        private String contactMobile;
        /**
         * 备注
         */
        private String remark;

        /**
         * 从数据库实体转换为DTO
         *
         * @param tenantWarehouse 数据库实体
         * @return DTO
         */
        public static TenantWarehouseInfo convertFrom(TenantWarehouse tenantWarehouse) {
            return TenantWarehouseInfo.builder()
                .id(tenantWarehouse.getId())
                .name(tenantWarehouse.getName())
                .warehouseCode(tenantWarehouse.getWarehouseCode())
                .warehouseDesc(tenantWarehouse.getWarehouseDesc())
                .warehouseIcon(tenantWarehouse.getWarehouseIcon())
                .warehouseColor(tenantWarehouse.getWarehouseColor())
                .warehouseSort(tenantWarehouse.getWarehouseSort())
                .warehouseStatus(String.valueOf(tenantWarehouse.getWarehouseStatus().getCode()))
                .isDefault(tenantWarehouse.getIsDefault())
                .country(tenantWarehouse.getCountry())
                .province(tenantWarehouse.getProvince())
                .city(tenantWarehouse.getCity())
                .district(tenantWarehouse.getDistrict())
                .address(tenantWarehouse.getAddress())
                .postcode(tenantWarehouse.getPostcode())
                .districtCode(tenantWarehouse.getDistrictCode())
                .longitude(tenantWarehouse.getLongitude())
                .contactName(tenantWarehouse.getContactName())
                .contactPhone(tenantWarehouse.getContactPhone())
                .contactEmail(tenantWarehouse.getContactEmail())
                .contactMobile(tenantWarehouse.getContactMobile())
                .remark(tenantWarehouse.getRemark())
                .build();
        }
    }

    /**
     * 租户本地化信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TenantLocaleInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        private String languageCode;
        private String currencyCode;
        private String timezone;
        private String dateFormat;
    }

    /**
     * 缓存元数据
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CacheMetadata implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 缓存创建时间
         */
        private LocalDateTime cacheCreatedTime;

        /**
         * 最后访问时间
         */
        private LocalDateTime lastAccessTime;

        /**
         * 访问次数
         */
        private Long accessCount;

        /**
         * 缓存版本
         */
        private String cacheVersion;

        /**
         * 是否需要刷新
         */
        private Boolean needRefresh;
    }

    // ================================ 业务方法 ================================

    /**
     * 获取租户ID
     */
    public String getTenantId() {
        return basicInfo != null ? String.valueOf(basicInfo.getId()) : null;
    }

    /**
     * 获取租户名称
     */
    public String getTenantName() {
        return basicInfo != null ? basicInfo.getUsername() : null;
    }

    /**
     * 获取默认仓库
     */
    public TenantWarehouseInfo getDefaultWarehouse() {
        if (warehouses == null || warehouses.isEmpty()) {
            return null;
        }
        // 如果没有默认仓库，返回第一个
        return warehouses.stream()
            .filter(warehouse -> Integer.valueOf(1).equals(warehouse.getIsDefault()))
            .findFirst()
            .orElse(warehouses.get(0));
    }

    /**
     * 获取主域名
     */
    public String getPrimaryDomain() {
        if (domains == null || domains.isEmpty()) {
            return null;
        }
        // 如果没有主域名，返回第一个
        return domains.stream()
            .filter(domain -> Boolean.TRUE.equals(domain.getIsPrimary()))
            .map(TenantDomainInfo::getDomainName)
            .findFirst()
            .orElse(domains.get(0).getDomainName());
    }

    /**
     * 检查套餐是否过期
     */
    public boolean isPlanExpired() {
        if (planInfo == null || planInfo.getExpiredTime() == null) {
            return false;
        }
        return LocalDateTime.now().isAfter(planInfo.getExpiredTime());
    }

    /**
     * 更新访问信息
     */
    public void updateAccessInfo() {
        if (cacheMetadata == null) {
            cacheMetadata = new CacheMetadata();
        }
        cacheMetadata.setLastAccessTime(LocalDateTime.now());
        cacheMetadata.setAccessCount(
            cacheMetadata.getAccessCount() != null ? cacheMetadata.getAccessCount() + 1 : 1L
        );
    }

    /**
     * 检查是否应该刷新缓存（15分钟内有访问）
     */
    public boolean shouldRefreshCache() {
        if (cacheMetadata == null || cacheMetadata.getLastAccessTime() == null) {
            return true;
        }

        LocalDateTime lastAccess = cacheMetadata.getLastAccessTime();
        LocalDateTime now = LocalDateTime.now();
        long minutesSinceLastAccess = java.time.Duration.between(lastAccess, now).toMinutes();

        // 如果距离上次访问时间在15分钟内，则需要刷新缓存时间
        return minutesSinceLastAccess <= 15;
    }
}
