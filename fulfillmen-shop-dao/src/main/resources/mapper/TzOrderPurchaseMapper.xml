<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper">
  <resultMap id="BaseResultMap" type="com.fulfillmen.shop.domain.entity.TzOrderPurchase">
    <!--@mbg.generated-->
    <!--@Table tz_order_purchase-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="goods_amount" jdbcType="DECIMAL" property="goodsAmount" />
    <result column="total_freight" jdbcType="DECIMAL" property="totalFreight" />
    <result column="purchase_order_no" jdbcType="VARCHAR" property="purchaseOrderNo" />
    <result column="buyer_id" jdbcType="BIGINT" property="buyerId" />
    <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
    <result column="order_date" jdbcType="TIMESTAMP" property="orderDate" />
    <result column="payment_date" jdbcType="TIMESTAMP" property="paymentDate" />
    <result column="completion_date" jdbcType="TIMESTAMP" property="completionDate" />
    <result column="service_fee" jdbcType="DECIMAL" property="serviceFee" />
    <result column="exchange_rate" jdbcType="DECIMAL" property="exchangeRate" />
    <result column="total_discount" jdbcType="DECIMAL" property="totalDiscount" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="finsh_pay_amount" jdbcType="DECIMAL" property="finshPayAmount" />
    <result column="total_tax" jdbcType="DECIMAL" property="totalTax" />
    <result column="net_amount" jdbcType="DECIMAL" property="netAmount" />
    <result column="delivery_address" jdbcType="LONGVARCHAR" property="deliveryAddress" />
    <result column="postal_code" jdbcType="VARCHAR" property="postalCode" />
    <result column="country_code" jdbcType="VARCHAR" property="countryCode" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="district" jdbcType="VARCHAR" property="district" />
    <result column="consignee_name" jdbcType="VARCHAR" property="consigneeName" />
    <result column="consignee_phone" jdbcType="VARCHAR" property="consigneePhone" />
    <result column="supplier_count" jdbcType="INTEGER" property="supplierCount" />
    <result column="line_item_count" jdbcType="INTEGER" property="lineItemCount" />
    <result column="total_quantity" jdbcType="INTEGER" property="totalQuantity" />
    <result column="purchase_notes" jdbcType="LONGVARCHAR" property="purchaseNotes" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="revision" jdbcType="INTEGER" property="revision" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, goods_amount, total_freight, purchase_order_no, buyer_id, order_status, order_date, 
    payment_date, completion_date, service_fee, exchange_rate, total_discount, total_amount, 
    finsh_pay_amount, total_tax, net_amount, delivery_address, postal_code, country_code, 
    province, city, district, consignee_name, consignee_phone, supplier_count, line_item_count, 
    total_quantity, purchase_notes, tenant_id, is_deleted, revision, gmt_created, gmt_modified
  </sql>
</mapper>