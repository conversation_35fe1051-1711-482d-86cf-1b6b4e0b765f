<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fulfillmen.starter</groupId>
        <artifactId>fulfillmen-starter-messaging</artifactId>
        <version>1.2.6-SNAPSHOT</version>
<!--        <relativePath>../pom.xml</relativePath>-->
    </parent>

    <artifactId>fulfillmen-starter-messaging-websocket</artifactId>
    <packaging>jar</packaging>
    <name>Fulfillmen Starter Messaging Websocket ${project.version}</name>
    <description>
        websocket 双向通信服务
    </description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- websocket lib -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
    </dependencies>
</project>
