0000000000000000000000000000000000000000 0b5ab0d150b86c34830c5c41b475b09775e8b2b9 james <<EMAIL>> 1747992173 +0800	branch: Created from origin/develop
0b5ab0d150b86c34830c5c41b475b09775e8b2b9 5f8436acd5dbb30af7aed7900c18499d91f33b08 james <<EMAIL>> 1747992323 +0800	commit: 提交代码修改。
5f8436acd5dbb30af7aed7900c18499d91f33b08 c5fb5bdcbf4e230f83b55e2235d203325ee4e41b james <<EMAIL>> 1748395714 +0800	commit: docs(fulfillmen-shop): 添加依赖管理相关文档
c5fb5bdcbf4e230f83b55e2235d203325ee4e41b d7198639a598306f8c32b3dbde414f8bcee8eb82 james <<EMAIL>> 1748585257 +0800	commit: feat(fulfillmen-shop): 新增产品SKU和SPU映射接口及商品详情DTO
d7198639a598306f8c32b3dbde414f8bcee8eb82 928c0c433beef01a6322ea28b1c17c4ef75c5f65 james <<EMAIL>> 1748591375 +0800	commit: feat(fulfillmen-shop): 新增商品数据批量操作重构总结文档
928c0c433beef01a6322ea28b1c17c4ef75c5f65 50231653db5e0987ff0462ab67182aab4e600f53 james <<EMAIL>> 1748596854 +0800	commit: feat(fulfillmen-shop): 更新商品映射和转换逻辑
50231653db5e0987ff0462ab67182aab4e600f53 5d0eaa1da44c37b912f9b78ac3b3a0f6432ccb12 james <<EMAIL>> 1748603988 +0800	commit: feat(fulfillmen-shop): 新增批量插入和更新功能
5d0eaa1da44c37b912f9b78ac3b3a0f6432ccb12 e09166a279b00f54dc74eedbca3da4d19b2ac0d5 james <<EMAIL>> 1748621061 +0800	commit: feat(fulfillmen-shop): 新增 OpenAPI 账户和接口管理功能
e09166a279b00f54dc74eedbca3da4d19b2ac0d5 957cb14f6aad3593cbc621a4f5fb3754ed18ef87 james <<EMAIL>> 1748621216 +0800	rebase (finish): refs/heads/develop onto 0b5ab0d150b86c34830c5c41b475b09775e8b2b9
957cb14f6aad3593cbc621a4f5fb3754ed18ef87 1d4fc76e8c56974ffa3b6c9c9c7744331ef4eb40 james <<EMAIL>> 1748946682 +0800	commit: feat(fulfillmen-shop): 新增 OpenAPI 产品转换映射器及相关数据结构
1d4fc76e8c56974ffa3b6c9c9c7744331ef4eb40 67e64bb0adecb8ffc679a92f9e393e4a25afaeb0 james <<EMAIL>> 1748946696 +0800	commit: feat(fulfillmen-shop): 更新 OpenAPI 产品接口和多语言支持
67e64bb0adecb8ffc679a92f9e393e4a25afaeb0 7b965dba00408cf34060791121e71fe68c7631b7 james <<EMAIL>> 1748950864 +0800	commit: feat(fulfillmen-shop): 增强产品搜索接口的多语言支持
7b965dba00408cf34060791121e71fe68c7631b7 337eb4c1937738cb94ce13c1b6e394b509f9fe85 james <<EMAIL>> 1748963583 +0800	rebase (finish): refs/heads/develop onto 957cb14f6aad3593cbc621a4f5fb3754ed18ef87
337eb4c1937738cb94ce13c1b6e394b509f9fe85 31ec063d8e3e8f3060dec75adf58bef7819013d6 james <<EMAIL>> 1749040920 +0800	commit: feat(fulfillmen-shop): 为 OpenAPI 账户表添加签名类型和 RSA 公钥支持
31ec063d8e3e8f3060dec75adf58bef7819013d6 a970cba7e43c0366008bf7583cf914cfd03eb10b james <<EMAIL>> 1749043707 +0800	commit: feat(fulfillmen-shop): 新增统一签名结构及相关文档
a970cba7e43c0366008bf7583cf914cfd03eb10b 7f24b04b38c1c438941bf9740628967c6175dbd5 james <<EMAIL>> 1749110574 +0800	commit: feat(fulfillmen-shop): 增强 OpenAPI 系统的安全性与限流功能
7f24b04b38c1c438941bf9740628967c6175dbd5 e1649f2c3dfc5e6c803b669ce39246833bd49a28 james <<EMAIL>> 1749110653 +0800	rebase (finish): refs/heads/develop onto 337eb4c1937738cb94ce13c1b6e394b509f9fe85
e1649f2c3dfc5e6c803b669ce39246833bd49a28 13dc7df1202573a9b2393ebcabe1f2958aeb79ae james <<EMAIL>> ********** +0800	commit: 移除 openapi 自定义异常。整合进全局异常处理类
13dc7df1202573a9b2393ebcabe1f2958aeb79ae 46855960435d88a6afa9bbb4d4d1ef0431afb46c james <<EMAIL>> ********** +0800	commit: 新增阿里巴巴Ocean客户端依赖配置文档及安装脚本
46855960435d88a6afa9bbb4d4d1ef0431afb46c ee6b00f2c9e79d0a8566bb80c0354e0884ba926a james <<EMAIL>> ********** +0800	commit: perf(openapi): 优化 nonce防重放攻击验证逻辑- 修改 validateNonceForReplayAttack 方法，增加 accountInfo 参数
ee6b00f2c9e79d0a8566bb80c0354e0884ba926a 1d3e9224fd1b0c54a1ccb6a5847bc32c1178eea5 james <<EMAIL>> ********** +0800	commit: feat(fulfillmen-shop): 新增货币转换服务及相关文档
1d3e9224fd1b0c54a1ccb6a5847bc32c1178eea5 469f1223251624f916fe96b14327b7e70bacfda6 james <<EMAIL>> ********** +0800	commit: feat(汇率): 实现汇率缓存管理和货币转换功能
469f1223251624f916fe96b14327b7e70bacfda6 1051edb107b5b175fd95bbf0d539b3004f645542 james <<EMAIL>> ********** +0800	commit (merge): Merge remote-tracking branch 'origin/develop' into develop
1051edb107b5b175fd95bbf0d539b3004f645542 979c814be206659db14738cc54ce59215ef2ac14 james <<EMAIL>> ********** +0800	commit: feat(限流): 更新产品搜索和详情接口的限流策略
979c814be206659db14738cc54ce59215ef2ac14 f10ade86e17df16f7c013720fb7dff5f870a9e55 james <<EMAIL>> ********** +0800	commit: feat(限流): 优化产品接口的限流策略与性能
f10ade86e17df16f7c013720fb7dff5f870a9e55 5c4a001d6769a2045809884f17484062233ba9d7 james <<EMAIL>> 1749535923 +0800	commit: feat(文档): 更新文档存放规范及新增文档
5c4a001d6769a2045809884f17484062233ba9d7 9cf7006d9bdf2d576bf363759ab3a2d0484ad620 james <<EMAIL>> 1749552649 +0800	commit: feat(国际化): 实现全局国际化方案及异常处理
9cf7006d9bdf2d576bf363759ab3a2d0484ad620 b95c3adc573b078979e1b07c19d3c3e59ad56f43 james <<EMAIL>> 1749610569 +0800	commit: feat(限流): 新增多重限流功能及相关文档
b95c3adc573b078979e1b07c19d3c3e59ad56f43 700bdaee89e1bea244c3561ea0eb17075de4eea8 james <<EMAIL>> 1749640842 +0800	commit: feat(shop): 增加商品详情和搜索结果的中英文字段映射
700bdaee89e1bea244c3561ea0eb17075de4eea8 75a3f7f8908be32416d3ca0f8da92b5eedbbed0c james <<EMAIL>> 1749708657 +0800	commit: feat(国际化): 完善国际化支持及异常处理
75a3f7f8908be32416d3ca0f8da92b5eedbbed0c 56b26a604994fcd08c44f9064f1aaae5dd31cf22 james <<EMAIL>> 1749710834 +0800	commit: feat(国际化): 实现从 Filter 到 LocaleResolver 的迁移
56b26a604994fcd08c44f9064f1aaae5dd31cf22 19898333e6401606b3e5bc7203c3ca354914c51a james <<EMAIL>> 1749720775 +0800	commit: feat(国际化): 完成从 Filter 到 LocaleResolver 的迁移及相关优化
19898333e6401606b3e5bc7203c3ca354914c51a e2fc37e8ce934b30a3cdb566d1a29474f3260f6c james <<EMAIL>> 1749723034 +0800	commit: feat(国际化): 添加验证码过期的国际化支持
e2fc37e8ce934b30a3cdb566d1a29474f3260f6c b532f6ceb28b3e1d71a0a824205d80febe562b04 james <<EMAIL>> 1749777868 +0800	commit: feat(国际化): 整合国际化配置与参数校验支持
b532f6ceb28b3e1d71a0a824205d80febe562b04 b602b61786f655a2bf37b986bd21318ef498c8c5 james <<EMAIL>> 1750041049 +0800	rebase (finish): refs/heads/develop onto 1051edb107b5b175fd95bbf0d539b3004f645542
b602b61786f655a2bf37b986bd21318ef498c8c5 5167ffa425f7d8204cf72a171dd4bb2d54f527f4 james <<EMAIL>> 1750043842 +0800	commit: feat(文档): 新增 Fulfillmen Shop 快速开发指南及优化总结
5167ffa425f7d8204cf72a171dd4bb2d54f527f4 56bd2c68ba93a84b9db6def6eccf2d73f357cfcc james <<EMAIL>> 1750143999 +0800	commit: feat(配置): 更新数据源和国际化支持
56bd2c68ba93a84b9db6def6eccf2d73f357cfcc 83c1e09b33fabcba31281ac101328996d5dfc253 james <<EMAIL>> 1750153737 +0800	commit: fix(fulfillmen-shop-common):修复 URL 编码在签名验证中的问题
83c1e09b33fabcba31281ac101328996d5dfc253 7bace26df4606c99abf9a7f5341d07524fa3326a james <<EMAIL>> 1750157512 +0800	commit: feat(多租户): 新增多租户相关功能及文档
7bace26df4606c99abf9a7f5341d07524fa3326a d1dd7eaf027bfd3f7bb4e9f99a0c1507a3a71192 james <<EMAIL>> 1750241320 +0800	commit: refactor(domain): 重构产品信息相关 DTO 类
d1dd7eaf027bfd3f7bb4e9f99a0c1507a3a71192 b57eb46545646332ca32d835eb7909677abecdae james <<EMAIL>> 1750391434 +0800	commit: rm build bug
b57eb46545646332ca32d835eb7909677abecdae db3471e1b770b8abb8590b231a00415040a16214 james <<EMAIL>> 1750391440 +0800	merge origin/develop: Merge made by the 'ort' strategy.
db3471e1b770b8abb8590b231a00415040a16214 9a558bd52d8372711746c7763325fc10d08a4738 james <<EMAIL>> 1750406313 +0800	commit: refactor(fulfillmen-shop):优化代码格式和结构
9a558bd52d8372711746c7763325fc10d08a4738 72dbb09034c76eab87e9bf3cbf362f7da286ab9b james <<EMAIL>> 1750408949 +0800	commit: refactor(fulfillmen-shop):优化 API 签名脚本和用户上下文类
72dbb09034c76eab87e9bf3cbf362f7da286ab9b 5549f5e3167505d952ecad77341483f0ec8be35c james <<EMAIL>> 1750408975 +0800	rebase (finish): refs/heads/develop onto db3471e1b770b8abb8590b231a00415040a16214
5549f5e3167505d952ecad77341483f0ec8be35c 4c372ffe0fadd0231dd6684ef37930fa4890eef4 james <<EMAIL>> 1750413174 +0800	commit: feat(kubernetes): 优化 fulfillmen-shop 部署配置
4c372ffe0fadd0231dd6684ef37930fa4890eef4 961810ddea32dff09f581a873b70723ac5285a70 james <<EMAIL>> 1750414083 +0800	commit: feat(kubernetes): 优化 fulfillmen-shop 部署配置
961810ddea32dff09f581a873b70723ac5285a70 be02b537ecd02e4db3b852cdd7902d0e39d52c96 james <<EMAIL>> 1750414128 +0800	commit (amend): fixed managed 安全 bug
be02b537ecd02e4db3b852cdd7902d0e39d52c96 b92b97ef96619f21ebcec8b414f3f0d26b6ce63c james <<EMAIL>> 1750414744 +0800	commit: 更新 application-sealos.yml 文件，新增允许跨域的域名配置，包括 'shop.nayasource.com'、'shop.fulfillmen.com' 和 'shop.1688hub.com'，以支持更多的跨域请求来源。
b92b97ef96619f21ebcec8b414f3f0d26b6ce63c 63ee941d649377c4e5a55b799d5adb81981f87bb james <<EMAIL>> 1750474613 +0800	commit: docs(shop): 更新跨域配置并添加项目描述
63ee941d649377c4e5a55b799d5adb81981f87bb cfdb246f6773e76aceb0a5028c945e6c276fb13e james <<EMAIL>> 1750728281 +0800	commit: build(bootstrap): 引入 MyBatis Plus 配置
cfdb246f6773e76aceb0a5028c945e6c276fb13e 1d71084aa4e4e7b3cf8cf978aa6a0fffeea9ceb6 james <<EMAIL>> 1750748608 +0800	commit: feat(限流): 优化产品接口的限流策略与性能
1d71084aa4e4e7b3cf8cf978aa6a0fffeea9ceb6 48e7c91b1a644935d481a181f76c1fe031ec69cf james <<EMAIL>> 1750821272 +0800	merge origin/develop: Fast-forward
48e7c91b1a644935d481a181f76c1fe031ec69cf 615ad5a904e04edf41311ab91e2ab7c4712e1767 james <<EMAIL>> 1750834868 +0800	commit: feat(domain): 优化产品 SKU 数据结构和映射
615ad5a904e04edf41311ab91e2ab7c4712e1767 dfb51d719c362c049e184ed96f0f306e0e990e2a james <<EMAIL>> 1750921890 +0800	commit: 更新 pom.xml 中 fulfillmen-support.version 版本至 1.1.6-SNAPSHOT；新增订单预览 JSON 测试文件；删除不再使用的国际化测试类和配置文件；优化国际化消息和异常处理逻辑，增强代码可读性和注释；更新多个实体类和映射类，添加最小起订量字段，完善订单相关功能。
dfb51d719c362c049e184ed96f0f306e0e990e2a ff153f3cc89c02d0612aa8f9a9d29de39696700d james <<EMAIL>> 1750947799 +0800	commit: 优化代码格式和可读性，添加最小起订量字段至多个实体类和映射类，更新购物车和订单相关功能，增强国际化消息处理，修复部分代码缩进问题。
ff153f3cc89c02d0612aa8f9a9d29de39696700d 217f114dd3b328a157ce1bce4e7c661f63215460 james <<EMAIL>> 1751019163 +0800	commit: 新增租户仓库表和相关字段，删除不再使用的 JSON 文件，优化订单相关功能，增强国际化消息处理，更新订单状态枚举，完善订单流程设计，提升代码可读性和注释。
217f114dd3b328a157ce1bce4e7c661f63215460 c0f5d1181746ab0fed79b564f4acda223cb56078 james <<EMAIL>> 1751206930 +0800	commit: feat(fulfillmen-shop-api):为 OpenapiSearchProductReq 类添加销量筛选参数
c0f5d1181746ab0fed79b564f4acda223cb56078 d4cbb612c4fe65866da0506b33607d21e0b9fe65 james <<EMAIL>> 1751207916 +0800	commit: docs(架构重构): 提交Convert包和Repository领域设计文档
d4cbb612c4fe65866da0506b33607d21e0b9fe65 eba91c6add28bcbf0987a69d650b2b272d29dd5f james <<EMAIL>> 1751208459 +0800	rebase (finish): refs/heads/develop onto 63ee941d649377c4e5a55b799d5adb81981f87bb
eba91c6add28bcbf0987a69d650b2b272d29dd5f a415a43ba25f80941e78a4a60e98a68d7ca6735d james <<EMAIL>> 1751208748 +0800	commit (merge): Merge remote-tracking branch 'origin/develop' into develop
a415a43ba25f80941e78a4a60e98a68d7ca6735d 15640f815fde5610688f1cbf41efeca5cbcb2584 james <<EMAIL>> 1751274226 +0800	merge origin/develop: Fast-forward
15640f815fde5610688f1cbf41efeca5cbcb2584 471126742f13c19ff10825f5ee9a3654907ccbcf james <<EMAIL>> 1751360321 +0800	commit: 更新 pom.xml 版本至 1.2.5-SNAPSHOT；在 TenantConfig.java 中增加注释以提高可读性；在 UserContextHolder.java 中将 ThreadLocal 替换为 TransmittableThreadLocal；在 BusinessExceptionI18n.java 中新增工厂方法以支持国际化异常创建；在 TzUserMapper.xml 中添加新字段以支持用户信息；新增 AlibabaCreateOrderConvert、CreateOrderDTO 和 CreateOrderRespDTO 类以支持订单创建功能；在 OrderReq.java 中添加购物车相关字段；删除 ProductSyncMonitor.java 文件；更新 OrderEventManager.java 中的事件发布逻辑；修改 OpenapiAccountRepository 接口以继承 IRepository；新增 OrderRepository 和 OrderRepositoryImpl 类以支持订单数据访问；更新 TenantResolverService.java 中的查询逻辑；在 IOrderManager 接口中更新创建订单方法返回类型；在 OrderManager.java 中更新订单创建逻辑；优化多个类中的代码格式和注释。
471126742f13c19ff10825f5ee9a3654907ccbcf 3f5053f836496a368a6052c91d2563ba6e62a4db james <<EMAIL>> ********** +0800	commit: 在 TzOrderItemMapper.xml 中新增 is_single_item 字段映射；在 TzProductMapping.java 中优化代码格式和注释，调整方法参数格式；在 CreateOrderDTO.java 中重构订单数据结构，新增采购订单和供应商订单字段；在 TzOrderItem.java 中添加 isSignleItem 字段以支持单品逻辑；在 OrderEventManager.java 中优化订单创建事件处理逻辑；在 FrontendOrderConvert.java 中更新单品处理逻辑，确保订单预览功能正常；在 OrderSubmitVO.java 中调整字段结构以提高可读性。
3f5053f836496a368a6052c91d2563ba6e62a4db 1c1a2c27465b2292131e2e6be6e8650452ad85d7 james <<EMAIL>> 1751444058 +0800	commit (merge): Merge remote-tracking branch 'origin/develop' into develop
1c1a2c27465b2292131e2e6be6e8650452ad85d7 7ca5ba0cd712bfaf8372103c3a9b63a67feb1330 james <<EMAIL>> 1751463965 +0800	commit: refactor(order): 重构订单创建逻辑
7ca5ba0cd712bfaf8372103c3a9b63a67feb1330 91cdbacb8d1f8d441364fc4c5fc5c1ad226c63a3 james <<EMAIL>> 1751525462 +0800	commit: feat(租户): 升级租户处理机制到过滤器级别并集成 Redis 缓存
91cdbacb8d1f8d441364fc4c5fc5c1ad226c63a3 2f8b9fd3f59ce543154c4e35f4cd0d1206e90ef6 james <<EMAIL>> 1751607950 +0800	commit: refactor(领域模型): 重构订单相关实体类
2f8b9fd3f59ce543154c4e35f4cd0d1206e90ef6 9880e859243d0f92a5a496130b3db23ddd616ffa james <<EMAIL>> 1751608036 +0800	rebase (finish): refs/heads/develop onto 1c1a2c27465b2292131e2e6be6e8650452ad85d7
9880e859243d0f92a5a496130b3db23ddd616ffa 84d9e33666197f0081dfd6618250ae4c8f588e3d james <<EMAIL>> 1751617241 +0800	commit: refactor(fulfillmen-shop):调整 API 路径和添加开发环境配置
84d9e33666197f0081dfd6618250ae4c8f588e3d d022afed02d84dc4d53897cd11ad1e46986ac9be james <<EMAIL>> 1751622088 +0800	commit: feat(order): 优化订单创建逻辑，支持服务费和价格比较- 新增服务费计算功能，根据租户配置动态调整服务费率
