feat(i18n): 更新国际化文案，增强订单成功页面和购物车结算功能

- 在英文和中文的国际化文件中新增了与订单成功相关的文案，包括配送信息、价格明细和下一步提示。
- 更新了购物车结算页面，优化了订单数据的处理逻辑，确保用户在订单成功后获得清晰的反馈。
- 增加了对订单商品种类和总数量的显示，提升用户体验。

此次更新旨在提升多语言支持和用户在订单处理过程中的体验。


refactor(fulfillmen-shop):调整 API 路径和添加开发环境配置

-将 SaExtensionInterceptor 的路径从 "/api/**" 修改为 StringConstants.PATH_PATTERN
- 添加了 application-sealosDev.yml 开发环境配置文件
- 在 application.yml 中注释掉了部分配置项，以便在开发环境中使用 application-sealosDev.yml 中的配置