<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fulfillmen.starter</groupId>
        <artifactId>fulfillmen-starter-parent</artifactId>
        <version>1.2.6-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>fulfillmen-starter-auth</artifactId>
    <name>Fulfillmen Starter 认证模块 ${project.version}</name>
    <modules>
        <module>fulfillmen-starter-auth-satoken</module>
        <module>fulfillmen-starter-auth-justauth</module>
    </modules>
    <packaging>pom</packaging>

    <properties>
        <reivsion>${project.parent.version}</reivsion>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.fulfillmen.starter</groupId>
                <artifactId>fulfillmen-starter-auth-justauth</artifactId>
            </dependency>
            <dependency>
                <groupId>com.fulfillmen.starter</groupId>
                <artifactId>fulfillmen-starter-auth-satoken</artifactId>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!--核心模块-->
        <dependency>
            <groupId>com.fulfillmen.starter</groupId>
            <artifactId>fulfillmen-starter-core</artifactId>
        </dependency>

        <!-- Test Dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>testcontainers</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- 编译插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <compilerArgument>-parameters</compilerArgument>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
