<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fulfillmen.starter</groupId>
        <artifactId>fulfillmen-starter-auth</artifactId>
        <version>1.2.6-SNAPSHOT</version>
<!--        <relativePath>../pom.xml</relativePath>-->
    </parent>
    <artifactId>fulfillmen-starter-auth-justauth</artifactId>
    <name>Fulfillmen Starter 认证模块 JustAuth</name>
    <packaging>jar</packaging>

    <description>
        Fulfillmen Starter 认证模块 JustAuth
        JustAuth（开箱即用的整合第三方登录的开源组件，脱离繁琐的第三方登录 SDK，让登录变得 So easy!）
    </description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- Just Auth（开箱即用的整合第三方登录的开源组件，脱离繁琐的第三方登录 SDK，让登录变得 So easy!） -->
        <dependency>
            <groupId>me.zhyd.oauth</groupId>
            <artifactId>JustAuth</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xkcoding.justauth</groupId>
            <artifactId>justauth-spring-boot-starter</artifactId>
        </dependency>

        <!-- 缓存模块 - Redisson -->
        <dependency>
            <groupId>com.fulfillmen.starter</groupId>
            <artifactId>fulfillmen-starter-cache-redisson</artifactId>
            <optional>true</optional>
            <scope>provided</scope>
        </dependency>

    </dependencies>
</project>
