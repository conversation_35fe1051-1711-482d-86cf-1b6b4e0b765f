<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fulfillmen.starter</groupId>
        <artifactId>fulfillmen-starter-auth</artifactId>
        <version>1.2.6-SNAPSHOT</version>
<!--        <relativePath>../pom.xml</relativePath>-->
    </parent>
    <artifactId>fulfillmen-starter-auth-satoken</artifactId>
    <name>Fulfillmen Starter 认证模块 - SaToken</name>
    <packaging>jar</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- Sa-Token（轻量级 Java 权限认证框架，让鉴权变得简单、优雅） -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-spring-boot3-starter</artifactId>
        </dependency>

        <!-- Sa-Token 整合 JWT -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-jwt</artifactId>
        </dependency>

        <!-- 缓存模块 - Redisson -->
        <dependency>
            <groupId>com.fulfillmen.starter</groupId>
            <artifactId>fulfillmen-starter-cache-redisson</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- Sa-Token 集成 Redisson 客户端 -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-redisson</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.redisson</groupId>
                    <artifactId>redisson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Sa-Token 整合 API 参数签名校验 -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-sign</artifactId>
        </dependency>


        <dependency>
            <groupId>com.fulfillmen.starter</groupId>
            <artifactId>fulfillmen-starter-json-jackson</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- web 模块 -->
        <dependency>
            <groupId>com.fulfillmen.starter</groupId>
            <artifactId>fulfillmen-starter-web</artifactId>
            <optional>true</optional>
        </dependency>

    </dependencies>
</project>
