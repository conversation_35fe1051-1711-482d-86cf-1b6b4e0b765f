/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.convert;

import com.fulfillmen.shop.domain.entity.TzProductSku;
import com.fulfillmen.shop.domain.entity.TzProductSpu;
import com.fulfillmen.shop.domain.entity.enums.TzProductSpuSingleItemEnum;
import com.fulfillmen.shop.domain.util.CurrencyConversionUtils;
import com.fulfillmen.shop.frontend.vo.OrderPreviewVO;
import com.fulfillmen.shop.manager.support.alibaba.util.OrderErrorCodeUtil;
import com.fulfillmen.support.alibaba.api.response.model.CreateOrderPreviewResultCargoModel;
import com.fulfillmen.support.alibaba.api.response.model.OrderPreviewResult;
import com.fulfillmen.support.alibaba.api.response.order.OrderPreviewResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 前端订单转换器
 *
 * <AUTHOR>
 * @date 2025/6/24
 * @description 前端订单数据转换器（重构版本，按产品和元单位分组）
 * @since 1.0.0
 */
@Slf4j
@Component
public class FrontendOrderConvert {

    /**
     * 服务费率（15%）
     */
    private static final BigDecimal SERVICE_FEE_RATE = new BigDecimal("0.15");

    /**
     * 转换订单预览响应为前端VO
     *
     * @param supplierResults 供应商预览结果列表
     * @return 订单预览VO
     */
    public OrderPreviewVO convertToOrderPreviewVO(List<SupplierPreviewResult> supplierResults) {
        if (CollectionUtils.isEmpty(supplierResults)) {
            return buildEmptyOrderPreviewVO();
        }

        List<OrderPreviewVO.ProductItemPreview> productItems = new ArrayList<>();
        List<OrderPreviewVO.OrderPreviewError> errors = new ArrayList<>();
        PriceAggregator priceAggregator = new PriceAggregator();

        for (SupplierPreviewResult supplierResult : supplierResults) {
            if (!supplierResult.isSuccess() || supplierResult.getResponse() == null) {
                addSupplierErrorToProductItems(supplierResult, productItems, errors);
                continue;
            }
            OrderPreviewResponse response = supplierResult.getResponse();
            if (!response.getSuccess()) {
                addApiErrorToProductItems(supplierResult, response, productItems, errors);
                continue;
            }
            List<OrderPreviewResult> previewResultList = response.getOrderPreviewResult();
            if (CollectionUtils.isEmpty(previewResultList)) {
                addEmptyResultErrorToProductItems(supplierResult, productItems, errors);
                continue;
            }

            OrderPreviewResult previewResult = previewResultList.get(0);
            processPreviewResult(supplierResult, previewResult, productItems, errors, priceAggregator);
        }

        OrderPreviewVO.PriceDetails priceDetails = buildPriceDetails(priceAggregator);
        OrderPreviewVO.OrderPreviewSummary summary = buildPreviewSummary(productItems, errors);

        return OrderPreviewVO.builder()
            .orderPreviewSummary(summary)
            .productItems(productItems)
            .priceDetails(priceDetails)
            .errors(errors)
            .build();
    }

    private void processPreviewResult(SupplierPreviewResult supplierResult,
        OrderPreviewResult previewResult,
        List<OrderPreviewVO.ProductItemPreview> productItems,
        List<OrderPreviewVO.OrderPreviewError> errors,
        PriceAggregator priceAggregator) {

        if (previewResult.getStatus() != null && !previewResult.getStatus()) {
            addPreviewResultErrorToProductItems(supplierResult, previewResult, productItems, errors);
            return;
        }

        priceAggregator.addMerchandiseAmount(fenToYuan(previewResult.getSumPaymentNoCarriage()));
        priceAggregator.addShippingAmount(fenToYuan(previewResult.getSumCarriage()));
        priceAggregator.addDiscountAmount(fenToYuan(previewResult.getDiscountFee()));

        List<CreateOrderPreviewResultCargoModel> cargoList = previewResult.getCargoList();
        if (CollectionUtils.isEmpty(cargoList)) {
            return;
        }

        for (CreateOrderPreviewResultCargoModel cargo : cargoList) {
            OrderItemInfo matchedItem = findMatchedItem(supplierResult.getItems(), cargo.getOfferId(),
                cargo.getSpecId());
            if (matchedItem == null) {
                log.warn("未找到匹配的商品项，offerId: {}, specId: {}", cargo.getOfferId(), cargo.getSpecId());
                continue;
            }

            if (hasCargoError(cargo)) {
                addCargoErrorToErrors(cargo, supplierResult.getSupplierId(), errors);
                addErrorProductItem(cargo, matchedItem, productItems);
            } else {
                addSuccessProductItem(cargo, matchedItem, productItems);
            }
        }
    }

    private boolean hasCargoError(CreateOrderPreviewResultCargoModel cargo) {
        String resultCode = cargo.getResultCode();
        return resultCode != null && !resultCode.equals("200") && !resultCode.equals("0")
            && !resultCode.equals("success");
    }

    private void addSuccessProductItem(CreateOrderPreviewResultCargoModel cargo,
        OrderItemInfo matchedItem,
        List<OrderPreviewVO.ProductItemPreview> productItems) {

        BigDecimal unitPrice = doubleToBigDecimal(cargo.getFinalUnitPrice());
        BigDecimal lineTotalAmount = doubleToBigDecimal(cargo.getAmount());
        BigDecimal unitPriceUsd = CurrencyConversionUtils.convertCurrency(unitPrice, "CNY", "USD");
        BigDecimal lineTotalAmountUsd = CurrencyConversionUtils.convertCurrency(lineTotalAmount, "CNY", "USD");

        OrderPreviewVO.ProductItemPreview productItem = OrderPreviewVO.ProductItemPreview.builder()
            .spuId(matchedItem.getSpu().getId())
            .specId(matchedItem.getSku().getPlatformSpecId())
            .skuId(matchedItem.getSku().getId())
            .productTitle(matchedItem.getSpu().getTitle())
            .productTitleEn(matchedItem.getSpu().getTitleTrans())
            .productImageUrl(matchedItem.getSpu().getMainImage())
            .skuImage(matchedItem.getSku().getImage())
            .skuSpecs(matchedItem.getSku().getSpecs())
            .unitPrice(unitPrice)
            .unitPriceUsd(unitPriceUsd != null ? unitPriceUsd : BigDecimal.ZERO)
            .orderedQuantity(matchedItem.getQuantity())
            .lineTotalAmount(lineTotalAmount)
            .lineTotalAmountUsd(lineTotalAmountUsd != null ? lineTotalAmountUsd : BigDecimal.ZERO)
            .unitOfMeasure(getUnitOfMeasure(matchedItem.getSku()))
            .available(true)
            .message("商品可用")
            .build();

        productItems.add(productItem);
    }

    /**
     * 添加错误商品项
     */
    private void addErrorProductItem(CreateOrderPreviewResultCargoModel cargo,
        OrderItemInfo matchedItem,
        List<OrderPreviewVO.ProductItemPreview> productItems) {
        OrderPreviewVO.ProductItemPreview productItem = OrderPreviewVO.ProductItemPreview.builder()
            .spuId(matchedItem.getSpu().getId())
            .specId(matchedItem.getSku().getPlatformSpecId())
            .skuId(matchedItem.getSku().getId())
            .productTitle(matchedItem.getSpu().getTitle())
            .productTitleEn(matchedItem.getSpu().getTitleTrans())
            .productImageUrl(matchedItem.getSpu().getMainImage())
            .skuImage(matchedItem.getSku().getImage())
            .skuSpecs(matchedItem.getSku().getSpecs())
            .unitPrice(BigDecimal.ZERO)
            .unitPriceUsd(BigDecimal.ZERO)
            .orderedQuantity(matchedItem.getQuantity())
            .lineTotalAmount(BigDecimal.ZERO)
            .lineTotalAmountUsd(BigDecimal.ZERO)
            .unitOfMeasure(getUnitOfMeasure(matchedItem.getSku()))
            .available(false)
            .message(OrderErrorCodeUtil.getFriendlyMessage(cargo.getResultCode()))
            .build();

        productItems.add(productItem);
    }

    /**
     * 添加供应商错误到商品项
     */
    private void addSupplierErrorToProductItems(SupplierPreviewResult supplierResult,
        List<OrderPreviewVO.ProductItemPreview> productItems,
        List<OrderPreviewVO.OrderPreviewError> errors) {
        errors.add(OrderPreviewVO.OrderPreviewError.builder()
            .errorCode("SUPPLIER_ERROR")
            .errorMessage(supplierResult.getErrorMessage() != null ? supplierResult.getErrorMessage() : "供应商服务异常")
            .errorType("SUPPLIER")
            .build());
        supplierResult.getItems().forEach(item -> addUnavailableProductItem(item, productItems, "供应商服务异常，商品暂时不可购买"));
    }

    /**
     * 添加API错误到商品项
     */
    private void addApiErrorToProductItems(SupplierPreviewResult supplierResult,
        OrderPreviewResponse response,
        List<OrderPreviewVO.ProductItemPreview> productItems,
        List<OrderPreviewVO.OrderPreviewError> errors) {
        String errorMessage = OrderErrorCodeUtil.getFriendlyMessage(response.getErrorCode());
        errors.add(OrderPreviewVO.OrderPreviewError.builder()
            .errorCode(response.getErrorCode())
            .errorMessage(errorMessage)
            .errorType("API")
            .build());
        supplierResult.getItems().forEach(item -> addUnavailableProductItem(item, productItems, errorMessage));
    }

    /**
     * 添加空结果错误到商品项
     */
    private void addEmptyResultErrorToProductItems(SupplierPreviewResult supplierResult,
        List<OrderPreviewVO.ProductItemPreview> productItems,
        List<OrderPreviewVO.OrderPreviewError> errors) {
        errors.add(OrderPreviewVO.OrderPreviewError.builder()
            .errorCode("EMPTY_RESULT")
            .errorMessage("预览结果为空")
            .errorType("API")
            .build());
        supplierResult.getItems().forEach(item -> addUnavailableProductItem(item, productItems, "预览失败，请稍后重试"));
    }

    /**
     * 添加预览结果错误到商品项
     */
    private void addPreviewResultErrorToProductItems(SupplierPreviewResult supplierResult,
        OrderPreviewResult previewResult,
        List<OrderPreviewVO.ProductItemPreview> productItems,
        List<OrderPreviewVO.OrderPreviewError> errors) {
        String errorMessage = previewResult.getMessage() != null ? previewResult.getMessage() : "预览失败";
        errors.add(OrderPreviewVO.OrderPreviewError.builder()
            .errorCode(previewResult.getResultCode())
            .errorMessage(errorMessage)
            .errorType("PREVIEW")
            .build());
        supplierResult.getItems().forEach(item -> addUnavailableProductItem(item, productItems, errorMessage));
    }

    /**
     * 添加cargo错误到错误列表
     */
    private void addCargoErrorToErrors(CreateOrderPreviewResultCargoModel cargo,
        String supplierId,
        List<OrderPreviewVO.OrderPreviewError> errors) {
        String friendlyMessage = OrderErrorCodeUtil.getFriendlyMessage(cargo.getResultCode());
        errors.add(OrderPreviewVO.OrderPreviewError.builder()
            .errorCode(cargo.getResultCode())
            .errorMessage(friendlyMessage)
            .offerId(cargo.getOfferId())
            .specId(cargo.getSpecId())
            .errorType("PRODUCT")
            .build());
    }

    /**
     * 添加不可用商品项
     */
    private void addUnavailableProductItem(OrderItemInfo item,
        List<OrderPreviewVO.ProductItemPreview> productItems,
        String message) {
        OrderPreviewVO.ProductItemPreview productItem = OrderPreviewVO.ProductItemPreview.builder()
            .spuId(item.getSpu().getId())
            .specId(item.getSku().getPlatformSpecId())
            .skuId(item.getSku().getId())
            .productTitle(item.getSpu().getTitle())
            .productTitleEn(item.getSpu().getTitleTrans())
            .productImageUrl(item.getSpu().getMainImage())
            .skuImage(item.getSku().getImage())
            .skuSpecs(item.getSku().getSpecs())
            .unitPrice(BigDecimal.ZERO)
            .unitPriceUsd(BigDecimal.ZERO)
            .orderedQuantity(item.getQuantity())
            .lineTotalAmount(BigDecimal.ZERO)
            .lineTotalAmountUsd(BigDecimal.ZERO)
            .unitOfMeasure(getUnitOfMeasure(item.getSku()))
            .available(false)
            .message(message)
            .build();
        productItems.add(productItem);
    }

    /**
     * 构建价格详情
     */
    private OrderPreviewVO.PriceDetails buildPriceDetails(PriceAggregator aggregator) {
        BigDecimal merchandiseAmount = aggregator.getMerchandiseAmount();
        BigDecimal shippingAmount = aggregator.getShippingAmount();
        BigDecimal discountAmount = aggregator.getDiscountAmount();
        BigDecimal serviceFee = merchandiseAmount.multiply(SERVICE_FEE_RATE).setScale(2, RoundingMode.HALF_UP);
        BigDecimal totalAmount = merchandiseAmount.add(shippingAmount).add(serviceFee).subtract(discountAmount);

        return OrderPreviewVO.PriceDetails.builder()
            .merchandiseAmount(merchandiseAmount)
            .merchandiseAmountUsd(
                Optional.ofNullable(CurrencyConversionUtils.convertCurrency(merchandiseAmount, "CNY", "USD"))
                    .orElse(BigDecimal.ZERO))
            .shippingAmount(shippingAmount)
            .shippingAmountUsd(
                Optional.ofNullable(CurrencyConversionUtils.convertCurrency(shippingAmount, "CNY", "USD"))
                    .orElse(BigDecimal.ZERO))
            .serviceFee(serviceFee)
            .serviceFeeUsd(Optional.ofNullable(CurrencyConversionUtils.convertCurrency(serviceFee, "CNY", "USD"))
                .orElse(BigDecimal.ZERO))
            .discountAmount(discountAmount)
            .discountAmountUsd(
                Optional.ofNullable(CurrencyConversionUtils.convertCurrency(discountAmount, "CNY", "USD"))
                    .orElse(BigDecimal.ZERO))
            .totalAmount(totalAmount)
            .totalAmountUsd(Optional.ofNullable(CurrencyConversionUtils.convertCurrency(totalAmount, "CNY", "USD"))
                .orElse(BigDecimal.ZERO))
            .build();
    }

    /**
     * 构建预览概览
     */
    private OrderPreviewVO.OrderPreviewSummary buildPreviewSummary(List<OrderPreviewVO.ProductItemPreview> productItems,
        List<OrderPreviewVO.OrderPreviewError> errors) {
        int totalQuantity = productItems.stream().mapToInt(OrderPreviewVO.ProductItemPreview::getOrderedQuantity).sum();
        boolean hasErrors = !errors.isEmpty();
        boolean success = !productItems.isEmpty()
            && productItems.stream().anyMatch(OrderPreviewVO.ProductItemPreview::getAvailable);
        return OrderPreviewVO.OrderPreviewSummary.builder()
            .totalQuantity(totalQuantity)
            .productTypeCount(productItems.size())
            .success(success)
            .hasErrors(hasErrors)
            .build();
    }

    /**
     * 查找匹配的商品项
     */
    private OrderItemInfo findMatchedItem(List<OrderItemInfo> items, Long offerId, String specId) {
        return items.stream()
            .filter(item -> {
                boolean isSingleItem = item.getSpu().getIsSingleItem() == TzProductSpuSingleItemEnum.YES;
                // 单品情况下，specId 可能为空
                return Objects.equals(item.getSku().getPlatformProductId(), offerId.toString())
                    && (Objects.equals(item.getSku().getPlatformSpecId(), specId) || isSingleItem);
            })
            .findFirst()
            .orElse(null);
    }

    /**
     * 获取计量单位
     */
    private String getUnitOfMeasure(TzProductSku sku) {
        return sku.getUnitInfo() != null ? sku.getUnitInfo().getUnit() : "件";
    }

    /**
     * 构建空订单预览VO
     */
    private OrderPreviewVO buildEmptyOrderPreviewVO() {
        return OrderPreviewVO.builder()
            .orderPreviewSummary(OrderPreviewVO.OrderPreviewSummary.builder()
                .totalQuantity(0).productTypeCount(0).success(false).hasErrors(false).build())
            .productItems(new ArrayList<>())
            .priceDetails(OrderPreviewVO.PriceDetails.builder()
                .merchandiseAmount(BigDecimal.ZERO).merchandiseAmountUsd(BigDecimal.ZERO)
                .shippingAmount(BigDecimal.ZERO).shippingAmountUsd(BigDecimal.ZERO)
                .serviceFee(BigDecimal.ZERO).serviceFeeUsd(BigDecimal.ZERO)
                .discountAmount(BigDecimal.ZERO).discountAmountUsd(BigDecimal.ZERO)
                .totalAmount(BigDecimal.ZERO).totalAmountUsd(BigDecimal.ZERO)
                .build())
            .errors(new ArrayList<>())
            .build();
    }

    /**
     * 分转换为元
     */
    private BigDecimal fenToYuan(Long fen) {
        if (fen == null) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(fen).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
    }

    /**
     * 双精度转换为BigDecimal
     */
    private BigDecimal doubleToBigDecimal(Double value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(value).setScale(2, RoundingMode.HALF_UP);
    }

    private static class PriceAggregator {

        private BigDecimal merchandiseAmount = BigDecimal.ZERO;
        private BigDecimal shippingAmount = BigDecimal.ZERO;
        private BigDecimal discountAmount = BigDecimal.ZERO;

        public void addMerchandiseAmount(BigDecimal amount) {
            this.merchandiseAmount = this.merchandiseAmount.add(amount != null ? amount : BigDecimal.ZERO);
        }

        public void addShippingAmount(BigDecimal amount) {
            this.shippingAmount = this.shippingAmount.add(amount != null ? amount : BigDecimal.ZERO);
        }

        public void addDiscountAmount(BigDecimal amount) {
            this.discountAmount = this.discountAmount.add(amount != null ? amount : BigDecimal.ZERO);
        }

        public BigDecimal getMerchandiseAmount() {
            return merchandiseAmount;
        }

        public BigDecimal getShippingAmount() {
            return shippingAmount;
        }

        public BigDecimal getDiscountAmount() {
            return discountAmount;
        }
    }

    @Data
    @Builder
    public static class OrderItemInfo {

        private TzProductSku sku;
        private TzProductSpu spu;
        private Integer quantity;
    }

    @Data
    @Builder
    public static class SupplierPreviewResult {

        private String supplierId;
        private String supplierName;
        private List<OrderItemInfo> items;
        private OrderPreviewResponse response;
        private boolean success;
        private String errorMessage;
    }
}
