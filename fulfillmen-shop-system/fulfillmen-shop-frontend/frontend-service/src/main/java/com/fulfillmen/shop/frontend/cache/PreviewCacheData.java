/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.cache;

import com.fulfillmen.shop.domain.req.OrderReq.OrderPreviewReq;
import com.fulfillmen.shop.frontend.vo.OrderPreviewVO;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 预览数据缓存对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PreviewCacheData implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 订单预览请求
     */
    private OrderPreviewReq orderPreviewReq;
    /**
     * 订单预览结果
     */
    private OrderPreviewVO orderPreviewVO;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 预览订单的过期时间
     */
    private LocalDateTime tokenExpiryTime;
    /**
     * 是否来自购物车 0: 否 , 1: 是
     */
    private Integer isShoppingCart;
    /**
     * 购物车ID 多个ID用逗号分隔，需要删除的购物车 Id 列表。如果 null 则不删除。
     */
    private String shoppingCartIds;
}
