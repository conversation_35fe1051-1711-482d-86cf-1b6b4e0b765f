/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.service.impl;

import com.fulfillmen.shop.domain.dto.CaptchaDTO;
import com.fulfillmen.shop.domain.dto.RmbQuotResponseDTO;
import com.fulfillmen.shop.frontend.service.ICommonService;
import com.fulfillmen.shop.manager.core.common.ICaptchaManager;
import com.fulfillmen.starter.core.exception.BusinessException;
import jakarta.mail.MessagingException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * 公共服务实现类
 * <pre>
 * 提供验证码服务
 * 提供发送邮箱验证码
 * 提供发送短信验证码
 * 提供通用的服务：
 * - juhe api 服务
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/4/29 17:58
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CommonServiceImpl implements ICommonService {

    private final ICaptchaManager captchaManager;

    @Override
    public String sendEmailCaptcha(String email) {
        try {
            return captchaManager.sendEmailCaptcha(email);
        } catch (MessagingException e) {
            log.error("Failed to send email captcha to [{}]", email, e);
            throw new BusinessException("Failed to send email captcha: " + e.getMessage());
        }
    }

    @Override
    public CaptchaDTO getCaptchaByImage() {
        return captchaManager.getCaptchaByImage();
    }

    @Override
    public Mono<RmbQuotResponseDTO> getRmbQuot(String type, Integer bank) {
        // TODO: 2025/4/30 待补充开发，底层代码已修改
        return null;
    }

    @Override
    public Mono<RmbQuotResponseDTO> getRmbQuot() {
        // TODO: 2025/4/30 待补充开发，底层代码已修改
        return null;
    }

}
