/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.config;

import com.fulfillmen.shop.dao.mapper.TzUserMapper;
import com.fulfillmen.shop.manager.core.common.ICaptchaManager;

import org.mockito.Mockito;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 测试配置类
 *
 * <AUTHOR>
 * @date 2025/4/24
 * @since 1.0.0
 */
@Configuration
@EnableAutoConfiguration
@ComponentScan("com.fulfillmen.shop.frontend.service")
@MapperScan("com.fulfillmen.shop.dao.mapper")
public class TestConfig {

    /**
     * 模拟TzUserMapper
     */
    @Bean
    @Primary
    public TzUserMapper tzUserMapper() {
        return Mockito.mock(TzUserMapper.class);
    }

    /**
     * 模拟ICaptchaManager
     */
    @Bean
    @Primary
    public ICaptchaManager captchaManager() {
        return Mockito.mock(ICaptchaManager.class);
    }
}
