/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.openapi.service.impl;

import org.springframework.stereotype.Service;
import com.fulfillmen.shop.common.enums.OpenapiErrorCodeEnum;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.common.exception.OpenapiExceptionI18n;
import com.fulfillmen.shop.domain.dto.PageDTO;
import com.fulfillmen.shop.domain.dto.ProductInfoDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.manager.core.repository.PdcProductMappingRepository;
import com.fulfillmen.shop.openapi.convert.OpenapiProductConvertMapping;
import com.fulfillmen.shop.openapi.req.OpenapiSearchProductReq;
import com.fulfillmen.shop.openapi.service.OpenapiProductService;
import com.fulfillmen.shop.openapi.vo.OpenapiProductDetailVO;
import com.fulfillmen.shop.openapi.vo.OpenapiProductInfoVO;
import lombok.extern.slf4j.Slf4j;

/**
 * OpenApi 产品服务实现
 *
 * <AUTHOR>
 * @date 2025/5/30 23:14
 * @description: 提供给第三方系统的产品相关服务实现，支持多语言
 * @since 1.0.0
 */
@Slf4j
@Service
public class OpenapiProductServiceImpl implements OpenapiProductService {

    private final PdcProductMappingRepository pdcProductMappingRepository;

    public OpenapiProductServiceImpl(PdcProductMappingRepository pdcProductMappingRepository) {
        this.pdcProductMappingRepository = pdcProductMappingRepository;
    }

    /**
     * 产品搜索 分页查询
     *
     * @param searchRequest 搜索请求参数
     * @param language      语言代码，支持 zh（中文）、en（英文），默认为 zh
     * @return 产品信息分页列表，支持多语言动态切换
     */
    @Override
    public PageDTO<OpenapiProductInfoVO> searchProductPageBySearchParam(OpenapiSearchProductReq searchRequest,
        String language) {
        // 参数校验
        if (searchRequest == null) {
            throw OpenapiExceptionI18n.of(OpenapiErrorCodeEnum.OPENAPI_REQUEST_PARAM_ERROR, "searchRequest");
        }

        log.debug("Processing product search request - language: {}, page: {}, pageSize: {}", language, searchRequest
            .getPage(), searchRequest.getPageSize());

        try {
            // 调用仓储层进行产品搜索并同步数据
            var productSearchRequestDTO = searchRequest.toProductSearchRequestDTO();
            // 不需要配置, 暂时只支持 中英文，默认 1688 就是中英文一起返回
            // LanguageEnum languageEnum = LanguageEnum.fromLanguage(language);
            // if (languageEnum == null || languageEnum == LanguageEnum.ZH) {
            //      languageEnum = LanguageEnum.EN;
            // }
            // productSearchRequestDTO.setLanguage(languageEnum);
            PageDTO<ProductInfoDTO> productInfoPage = pdcProductMappingRepository
                .searchProductInfoListSyncWithCache(productSearchRequestDTO, false);

            // 转换为OpenapiProductInfoVO列表，应用多语言逻辑
            PageDTO<OpenapiProductInfoVO> result = PageDTO.<OpenapiProductInfoVO>builder()
                .records(OpenapiProductConvertMapping.INSTANCE.toOpenapiProductInfoVOList(productInfoPage
                    .getRecords(), language))
                .total(productInfoPage.getTotal())
                .pageIndex(productInfoPage.getPageIndex())
                .pageSize(productInfoPage.getPageSize())
                .build();

            log.debug("Product search completed - found {} products", result.getTotal());
            return result;

        } catch (OpenapiExceptionI18n e) {
            throw e;
        } catch (BusinessExceptionI18n e) {
            // 保持原始的国际化异常，不要重新包装
            log.error("Business exception in product search", e);
            throw e;
        } catch (Exception e) {
            log.error("Failed to search products", e);
            throw OpenapiExceptionI18n.of(OpenapiErrorCodeEnum.OPENAPI_RESPONSE_ERROR, e.getMessage());
        }
    }

    /**
     * 获取产品详情
     *
     * @param productId    产品 ID
     * @param forceRefresh 是否强制刷新缓存
     * @param language     语言代码，支持 zh（中文）、en（英文）
     * @return 产品详情VO，根据语言参数返回相应语言的内容
     */
    @Override
    public OpenapiProductDetailVO getProductDetail(Long productId, Boolean forceRefresh, String language) {
        // 参数校验
        if (productId == null || productId <= 0) {
            throw OpenapiExceptionI18n.of(OpenapiErrorCodeEnum.OPENAPI_REQUEST_PARAM_ERROR, "productId");
        }

        log.debug("Processing product detail request - productId: {}, forceRefresh: {}, language: {}", productId, forceRefresh, language);

        try {
            // 调用仓储层获取阿里巴巴产品详情
            AlibabaProductDetailDTO alibabaProductDetail = pdcProductMappingRepository
                .getProductDetailWithCache(productId, forceRefresh);

            if (alibabaProductDetail == null) {
                log.warn("Product not found - productId: {}", productId);
                throw OpenapiExceptionI18n.of(OpenapiErrorCodeEnum.OPENAPI_RESPONSE_ERROR, "Product not found");
            }

            // 使用转换器将阿里巴巴产品详情转换为OpenAPI产品详情VO，并应用多语言逻辑
            OpenapiProductDetailVO result = OpenapiProductConvertMapping.INSTANCE
                .toOpenapiProductDetailVO(alibabaProductDetail, language);

            log.debug("Product detail retrieved successfully - productId: {}, title: {}", productId, result.getTitle());
            return result;

        } catch (OpenapiExceptionI18n e) {
            throw e;
        } catch (com.fulfillmen.shop.common.exception.BusinessExceptionI18n e) {
            // 保持原始的国际化异常，不要重新包装
            log.error("Business exception in product detail - productId: {}", productId, e);
            throw e;
        } catch (Exception e) {
            log.error("Failed to get product detail - productId: {}", productId, e);
            throw OpenapiExceptionI18n.of(OpenapiErrorCodeEnum.OPENAPI_RESPONSE_ERROR, e.getMessage());
        }
    }
}
