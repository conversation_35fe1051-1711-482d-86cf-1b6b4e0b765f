/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.openapi.service.impl;

import com.fulfillmen.shop.domain.dto.openapi.OpenapiAccountInfoDTO;
import com.fulfillmen.shop.manager.core.repository.OpenapiAccountRepository;
import com.fulfillmen.shop.openapi.service.OpenapiAccountService;
import org.springframework.stereotype.Service;

/**
 * OpenApi 账户服务实现
 *
 * <AUTHOR>
 * @date 2025/6/4 17:22
 * @description: todo
 * @since 1.0.0
 */
@Service
public class OpenapiAccountServiceImpl implements OpenapiAccountService {

    private final OpenapiAccountRepository openapiAccountRepository;

    public OpenapiAccountServiceImpl(OpenapiAccountRepository openapiAccountRepository) {
        this.openapiAccountRepository = openapiAccountRepository;
    }

    @Override
    public OpenapiAccountInfoDTO getOpenApiAccountInfoDTOByAccessKey(String accessKey) {
        return openapiAccountRepository.getOpenApiAccountInfoDTOByAccessKey(accessKey);
    }
}
