2025-07-06 21:47:11 INFO  [background-preinit] [tid::uId::ip::os::browser:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-06 21:47:11 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Starting BootstrapApplication using Java 21.0.5 with PID 4001 (/Users/<USER>/work/fulfillmen/fulfillmen-shop/fulfillmen-shop-bootstrap/target/classes started by yzsama in /Users/<USER>/work/fulfillmen/fulfillmen-workspace)
2025-07-06 21:47:11 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Running with Spring Boot v3.3.11, Spring v6.1.19
2025-07-06 21:47:11 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - The following 1 profile is active: "local"
2025-07-06 21:47:12 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-06 21:47:12 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-06 21:47:12 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-07-06 21:47:12 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.s.a.dao.SaTokenDaoRedissionConfiguration - [Fulfillmen Starter] - Auto Configuration 'SaToken-Dao-Redis' completed initialization.
2025-07-06 21:47:12 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-06 21:47:12 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-06 21:47:12 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.d.m.a.MybatisPlusAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'MyBatis Plus' completed initialization.
2025-07-06 21:47:12 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - 多租户拦截器已配置，忽略表: [tenant_commission_config, tenants, regions, tenant_plan_relation, sys_alibaba_category, subregions, sys_users, tenant_domains, sys_option, openapi_account, pdc_product_mapping, tenant_plans, tenants_info, tenant_files, sys_config, openapi_account_permission, openapi_interface, tenant_locales]
2025-07-06 21:47:13 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.d.m.a.i.MyBatisPlusIdGeneratorConfiguration - [Fulfillmen Starter] - Auto Configuration 'MyBatis Plus-IdGenerator-CosId' completed initialization.
2025-07-06 21:47:13 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.FulfillmenWebMvcConfig - Default locale initialized to: en_US
2025-07-06 21:47:13 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.FulfillmenWebMvcConfig - MessageSource configured with basenames: [i18n/messages, i18n/openapi-message]
2025-07-06 21:47:13 WARN  [main] [tid::uId::ip::os::browser:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-06 21:47:13 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-06 21:47:13 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2298 ms
2025-07-06 21:47:13 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - === 过滤器配置信息 ===
2025-07-06 21:47:13 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 租户过滤器: enabled=true, order=-2147483638
2025-07-06 21:47:13 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - MDC 过滤器: enabled=true, order=-2147483628
2025-07-06 21:47:13 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 过滤器优先级说明:
过滤器执行顺序（数值越小优先级越高）：

1. TRACE_FILTER     (-2147483648) - 链路跟踪过滤器，生成 TraceId
2. TENANT_FILTER    (-2147483638) - 租户过滤器，设置租户上下文
3. MDC_FILTER       (-2147483628) - MDC 过滤器，设置日志上下文
4. XSS_FILTER       (-2147483548) - XSS 过滤器，安全防护
5. CORS_FILTER      (-2147483538) - CORS 过滤器，跨域处理
6. LOG_FILTER       (2147483637) - 日志过滤器，记录请求响应

注意：
- 链路跟踪过滤器优先级最高，确保 TraceId 在整个请求生命周期中可用
- 租户过滤器在链路跟踪之后，为后续过滤器提供租户上下文
- MDC 过滤器在租户过滤器之后，可以获取到租户信息并设置到日志上下文
- 安全相关过滤器（XSS、CORS）在业务过滤器之前执行
- 日志过滤器优先级最低，记录完整的请求响应信息

2025-07-06 21:47:13 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - ===================
2025-07-06 21:47:13 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 租户过滤器已注册 [enabled=true, order=-2147483638, urlPatterns=[/*], excludePatterns=20]
2025-07-06 21:47:13 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - MDC 过滤器已注册 [enabled=true, order=-2147483628, urlPatterns=[/*], features=IP标准化,浏览器信息,操作系统信息]
2025-07-06 21:47:13 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.trace.TraceAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web-Trace' completed initialization.
2025-07-06 21:47:13 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.l.i.autoconfigure.LogAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Log-interceptor' completed initialization.
2025-07-06 21:47:13 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?nayasource\.com
2025-07-06 21:47:13 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?nayasource\.com
2025-07-06 21:47:13 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http://localhost:[0-9]+
2025-07-06 21:47:13 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http://localhost:[0-9]+
2025-07-06 21:47:13 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?aliyuncs\.com
2025-07-06 21:47:13 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?aliyuncs\.com
2025-07-06 21:47:13 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?sealoshzh\.site
2025-07-06 21:47:13 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?sealoshzh\.site
2025-07-06 21:47:13 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 检测到 allowCredentials=true 且使用通配符配置，这可能导致CORS错误
2025-07-06 21:47:13 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 建议：1) 设置 allowCredentials=false，或 2) 使用具体的域名列表替换通配符
2025-07-06 21:47:13 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 当前策略：保持 allowCredentials=true，但建议检查配置
2025-07-06 21:47:13 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 跨域配置初始化完成 [常规域名: 0, 正则域名: 4, 允许凭证: true, 缓存时间: 3600s]
2025-07-06 21:47:13 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.w.a.cors.CorsConfigurationValidator - 
🔍 CORS配置分析报告:
==================================================
 1. ⚠️  安全建议: 生产环境中allowCredentials=true时建议明确指定允许的HTTP方法
 2. ⚠️  安全建议: 生产环境中allowCredentials=true时建议明确指定允许的请求头
 3. 🔒 安全建议: 生产环境建议明确指定允许的HTTP方法，避免使用 '*'
 4. ✅ 最佳实践: 正在使用正则表达式匹配，这是推荐的域名配置方式
==================================================
📖 更多信息请参考: fulfillmen-starter-web/CORS-CONFIG-EXAMPLE.md

2025-07-06 21:47:13 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-07-06 21:47:13 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-07-06 21:47:14 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.r.autoconfigure.RedissonAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Redisson' completed initialization.
2025-07-06 21:47:14 INFO  [main] [tid::uId::ip::os::browser:] org.redisson.Version - Redisson 3.45.1
2025-07-06 21:47:14 INFO  [redisson-netty-1-5] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-06 21:47:14 INFO  [redisson-netty-1-19] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-06 21:47:14 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-06 21:47:14 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 货币汇率缓存初始化完成
2025-07-06 21:47:14 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: fixed_window
2025-07-06 21:47:14 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: sliding_window
2025-07-06 21:47:14 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: token_bucket
2025-07-06 21:47:14 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: leaky_bucket
2025-07-06 21:47:14 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已启用
2025-07-06 21:47:14 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.g.a.GraphicCaptchaAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Captcha-Graphic' completed initialization.
2025-07-06 21:47:14 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - 🎯 自动同步功能初始化: 禁用
2025-07-06 21:47:14 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.manager.strategy.SyncStrategyFactory - 同步策略工厂初始化完成，可用策略: [AUTO, DISABLED, MANUAL]
2025-07-06 21:47:14 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.sync.chain.config.ProductSyncChainConfig - 产品同步责任链配置初始化完成
2025-07-06 21:47:14 WARN  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'productSyncChainBuilder' defined in file [/Users/<USER>/work/fulfillmen/fulfillmen-shop/fulfillmen-shop-manager/target/classes/com/fulfillmen/shop/manager/service/sync/chain/core/ProductSyncChainBuilder.class]: Unsatisfied dependency expressed through constructor parameter 6: Error creating bean with name 'cacheUpdateHandler' defined in file [/Users/<USER>/work/fulfillmen/fulfillmen-shop/fulfillmen-shop-manager/target/classes/com/fulfillmen/shop/manager/service/sync/chain/handlers/CacheUpdateHandler.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'org.springframework.cache.CacheManager' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-07-06 21:47:14 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-06 21:47:14 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-06 21:47:15 ERROR [main] [tid::uId::ip::os::browser:] o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.fulfillmen.shop.manager.service.sync.chain.handlers.CacheUpdateHandler required a bean of type 'org.springframework.cache.CacheManager' that could not be found.


Action:

Consider defining a bean of type 'org.springframework.cache.CacheManager' in your configuration.

2025-07-06 21:57:30 INFO  [background-preinit] [tid::uId::ip::os::browser:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-06 21:57:30 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Starting BootstrapApplication using Java 21.0.5 with PID 13031 (/Users/<USER>/work/fulfillmen/fulfillmen-shop/fulfillmen-shop-bootstrap/target/classes started by yzsama in /Users/<USER>/work/fulfillmen/fulfillmen-workspace)
2025-07-06 21:57:30 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Running with Spring Boot v3.3.11, Spring v6.1.19
2025-07-06 21:57:30 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - The following 1 profile is active: "local"
2025-07-06 21:57:31 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-06 21:57:31 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-06 21:57:31 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 27 ms. Found 0 Redis repository interfaces.
2025-07-06 21:57:31 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.s.a.dao.SaTokenDaoRedissionConfiguration - [Fulfillmen Starter] - Auto Configuration 'SaToken-Dao-Redis' completed initialization.
2025-07-06 21:57:31 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-06 21:57:31 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-06 21:57:31 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.d.m.a.MybatisPlusAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'MyBatis Plus' completed initialization.
2025-07-06 21:57:31 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - 多租户拦截器已配置，忽略表: [tenant_commission_config, tenants, regions, tenant_plan_relation, sys_alibaba_category, subregions, sys_users, tenant_domains, sys_option, openapi_account, pdc_product_mapping, tenant_plans, tenants_info, tenant_files, sys_config, openapi_account_permission, openapi_interface, tenant_locales]
2025-07-06 21:57:31 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.d.m.a.i.MyBatisPlusIdGeneratorConfiguration - [Fulfillmen Starter] - Auto Configuration 'MyBatis Plus-IdGenerator-CosId' completed initialization.
2025-07-06 21:57:32 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.FulfillmenWebMvcConfig - Default locale initialized to: en_US
2025-07-06 21:57:32 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.FulfillmenWebMvcConfig - MessageSource configured with basenames: [i18n/messages, i18n/openapi-message]
2025-07-06 21:57:32 WARN  [main] [tid::uId::ip::os::browser:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-06 21:57:32 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-06 21:57:32 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2270 ms
2025-07-06 21:57:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - === 过滤器配置信息 ===
2025-07-06 21:57:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 租户过滤器: enabled=true, order=-2147483638
2025-07-06 21:57:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - MDC 过滤器: enabled=true, order=-2147483628
2025-07-06 21:57:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 过滤器优先级说明:
过滤器执行顺序（数值越小优先级越高）：

1. TRACE_FILTER     (-2147483648) - 链路跟踪过滤器，生成 TraceId
2. TENANT_FILTER    (-2147483638) - 租户过滤器，设置租户上下文
3. MDC_FILTER       (-2147483628) - MDC 过滤器，设置日志上下文
4. XSS_FILTER       (-2147483548) - XSS 过滤器，安全防护
5. CORS_FILTER      (-2147483538) - CORS 过滤器，跨域处理
6. LOG_FILTER       (2147483637) - 日志过滤器，记录请求响应

注意：
- 链路跟踪过滤器优先级最高，确保 TraceId 在整个请求生命周期中可用
- 租户过滤器在链路跟踪之后，为后续过滤器提供租户上下文
- MDC 过滤器在租户过滤器之后，可以获取到租户信息并设置到日志上下文
- 安全相关过滤器（XSS、CORS）在业务过滤器之前执行
- 日志过滤器优先级最低，记录完整的请求响应信息

2025-07-06 21:57:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - ===================
2025-07-06 21:57:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 租户过滤器已注册 [enabled=true, order=-2147483638, urlPatterns=[/*], excludePatterns=20]
2025-07-06 21:57:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - MDC 过滤器已注册 [enabled=true, order=-2147483628, urlPatterns=[/*], features=IP标准化,浏览器信息,操作系统信息]
2025-07-06 21:57:32 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.trace.TraceAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web-Trace' completed initialization.
2025-07-06 21:57:32 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.l.i.autoconfigure.LogAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Log-interceptor' completed initialization.
2025-07-06 21:57:32 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?nayasource\.com
2025-07-06 21:57:32 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?nayasource\.com
2025-07-06 21:57:32 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http://localhost:[0-9]+
2025-07-06 21:57:32 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http://localhost:[0-9]+
2025-07-06 21:57:32 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?aliyuncs\.com
2025-07-06 21:57:32 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?aliyuncs\.com
2025-07-06 21:57:32 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?sealoshzh\.site
2025-07-06 21:57:32 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?sealoshzh\.site
2025-07-06 21:57:32 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 检测到 allowCredentials=true 且使用通配符配置，这可能导致CORS错误
2025-07-06 21:57:32 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 建议：1) 设置 allowCredentials=false，或 2) 使用具体的域名列表替换通配符
2025-07-06 21:57:32 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 当前策略：保持 allowCredentials=true，但建议检查配置
2025-07-06 21:57:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 跨域配置初始化完成 [常规域名: 0, 正则域名: 4, 允许凭证: true, 缓存时间: 3600s]
2025-07-06 21:57:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.w.a.cors.CorsConfigurationValidator - 
🔍 CORS配置分析报告:
==================================================
 1. ⚠️  安全建议: 生产环境中allowCredentials=true时建议明确指定允许的HTTP方法
 2. ⚠️  安全建议: 生产环境中allowCredentials=true时建议明确指定允许的请求头
 3. 🔒 安全建议: 生产环境建议明确指定允许的HTTP方法，避免使用 '*'
 4. ✅ 最佳实践: 正在使用正则表达式匹配，这是推荐的域名配置方式
==================================================
📖 更多信息请参考: fulfillmen-starter-web/CORS-CONFIG-EXAMPLE.md

2025-07-06 21:57:32 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-07-06 21:57:32 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-07-06 21:57:32 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.r.autoconfigure.RedissonAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Redisson' completed initialization.
2025-07-06 21:57:32 INFO  [main] [tid::uId::ip::os::browser:] org.redisson.Version - Redisson 3.45.1
2025-07-06 21:57:33 INFO  [redisson-netty-1-4] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-06 21:57:33 INFO  [redisson-netty-1-19] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-06 21:57:33 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-06 21:57:33 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 货币汇率缓存初始化完成
2025-07-06 21:57:33 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: fixed_window
2025-07-06 21:57:33 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: sliding_window
2025-07-06 21:57:33 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: token_bucket
2025-07-06 21:57:33 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: leaky_bucket
2025-07-06 21:57:33 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已启用
2025-07-06 21:57:33 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.g.a.GraphicCaptchaAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Captcha-Graphic' completed initialization.
2025-07-06 21:57:33 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - 🎯 自动同步功能初始化: 禁用
2025-07-06 21:57:33 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.manager.strategy.SyncStrategyFactory - 同步策略工厂初始化完成，可用策略: [AUTO, DISABLED, MANUAL]
2025-07-06 21:57:33 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.sync.chain.config.ProductSyncChainConfig - 产品同步责任链配置初始化完成
2025-07-06 21:57:33 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.sync.chain.handlers.CacheUpdateHandler - 产品同步缓存初始化完成
2025-07-06 21:57:33 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl - 缓存配置详情: ProductDetailVO[本地:8min,远程:25min,容量:300]
2025-07-06 21:57:33 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl - 🎯 ProductService优化缓存配置完成:
2025-07-06 21:57:33 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── ProductDetailVO缓存: 本地8min/远程25min, 容量300, 键前缀: frontend:product:vo:
2025-07-06 21:57:33 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── 双层缓存策略: 本地内存 + Redis远程
2025-07-06 21:57:33 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── 空值缓存: 已启用，防止缓存穿透
2025-07-06 21:57:33 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   └── 同步机制: 已启用，保证多实例一致性
2025-07-06 21:57:33 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Initialized CompositeLocaleResolver with default locale: en_US and supported locales: [en_US, zh_CN, zh_TW]
2025-07-06 21:57:33 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Initialized LocaleChangeInterceptor with param name: lang
2025-07-06 21:57:33 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.autoconfigure.SpringDocAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'ApiDoc' completed initialization.
2025-07-06 21:57:33 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.s.autoconfigure.SaTokenAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'SaToken' completed initialization.
2025-07-06 21:57:33 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.mvc.WebMvcAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web MVC' completed initialization.
2025-07-06 21:57:34 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-06 21:57:34 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已注册: order=100, patterns=/**, rate=100/60s
2025-07-06 21:57:34 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Added LocaleChangeInterceptor to registry
2025-07-06 21:57:34 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.a.response.GlobalResponseAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web-Global Response' completed initialization.
2025-07-06 21:57:34 INFO  [main] [tid::uId::ip::os::browser:] c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@22367b8
2025-07-06 21:57:34 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.j.autoconfigure.JetCacheAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'JetCache' completed initialization.
2025-07-06 21:57:34 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote instanceId:[InstanceId{instanceId=************:13031, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-06 21:57:34 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote machineState:[MachineState{machineId=7, lastTimeStamp=1751810254337}] - instanceId:[InstanceId{instanceId=************:13031, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-06 21:57:34 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no].
2025-07-06 21:57:34 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-06 21:57:34 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-06 21:57:34 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.order_no] jobSize:[0].
2025-07-06 21:57:34 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no].
2025-07-06 21:57:34 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2].
2025-07-06 21:57:34 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2] start.
2025-07-06 21:57:34 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.user_no] jobSize:[0].
2025-07-06 21:57:35 DEBUG [main] [tid::uId::ip::os::browser:] c.f.starter.log.interceptor.handler.LogFilter - Filter 'logFilter' configured for use
2025-07-06 21:57:35 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-06 21:57:35 INFO  [main] [tid::uId::ip::os::browser:] org.xnio - XNIO version 3.8.16.Final
2025-07-06 21:57:35 INFO  [main] [tid::uId::ip::os::browser:] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-06 21:57:35 INFO  [main] [tid::uId::ip::os::browser:] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-06 21:57:35 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-07-06 21:57:35 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-06 21:57:35 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 51 ms
2025-07-06 21:57:35 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 13 endpoints beneath base path '/actuator'
2025-07-06 21:57:35 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已注册: order=100, patterns=/**, rate=100/60s
2025-07-06 21:57:35 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Added LocaleChangeInterceptor to registry
2025-07-06 21:57:35 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-06 21:57:35 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8099 (http) with context path '/'
2025-07-06 21:57:35 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Started BootstrapApplication in 5.527 seconds (process running for 6.195)
2025-07-06 21:57:35 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 开始初始化汇率缓存...
2025-07-06 21:57:35 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 开始定时刷新汇率缓存...
2025-07-06 21:57:35 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到USD的汇率
2025-07-06 21:57:35 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到USD的汇率
2025-07-06 21:57:35 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.WebClientConfig - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=USD
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-07-06 21:57:35 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.WebClientConfig - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=USD
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-07-06 21:57:35 DEBUG [reactor-http-nio-11] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.WebClientConfig - [响应信息]
Status: 200 OK
Headers: [Date:"Sun, 06 Jul 2025 13:57:35 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=dcb288cc40c7c3baf54fd776ea60ad3e35be8886983f413c5ace29600bccddc8; Path=/; HttpOnly", Etag:"973748d511f7b572138de007096af38e"]
Body: {"reason":"查询成功!","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"USD","currencyT_Name":"美元","currencyFD":"1","exchange":"0.1396","result":"0.1396","updateTime":"2025-07-06 21:52:00"},{"currencyF":"USD","currencyF_Name":"美元","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"7.1645","result":"7.1645","updateTime":"2025-07-06 21:52:00"}],"error_code":0}

2025-07-06 21:57:35 DEBUG [reactor-http-nio-13] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.WebClientConfig - [响应信息]
Status: 200 OK
Headers: [Date:"Sun, 06 Jul 2025 13:57:35 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=b4883b6c147e34c272fb3c5c003ff72b055984503b75cc25d60ce3240b07faf7; Path=/; HttpOnly", Etag:"5570ab0d508bedb604e6a9859eb8654b"]
Body: {"reason":"查询成功!","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"USD","currencyT_Name":"美元","currencyFD":"1","exchange":"0.1396","result":"0.1396","updateTime":"2025-07-06 21:52:00"},{"currencyF":"USD","currencyF_Name":"美元","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"7.1645","result":"7.1645","updateTime":"2025-07-06 21:52:00"}],"error_code":0}

2025-07-06 21:57:35 DEBUG [reactor-http-nio-11] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: 查询成功!
2025-07-06 21:57:35 DEBUG [reactor-http-nio-13] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: 查询成功!
2025-07-06 21:57:35 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> USD
2025-07-06 21:57:35 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> USD
2025-07-06 21:57:35 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-USD = 0.1396
2025-07-06 21:57:35 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-USD = 0.1396
2025-07-06 21:57:35 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到EUR的汇率
2025-07-06 21:57:35 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.WebClientConfig - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=EUR
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-07-06 21:57:35 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到EUR的汇率
2025-07-06 21:57:35 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.WebClientConfig - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=EUR
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-07-06 21:57:35 DEBUG [reactor-http-nio-13] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.WebClientConfig - [响应信息]
Status: 200 OK
Headers: [Date:"Sun, 06 Jul 2025 13:57:35 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=c7caa5c0b6f400e8dd57bae551e0852ad7e2c1ea3feea4bcd70ceb8fe9c1d2a3; Path=/; HttpOnly", Etag:"5570ab0d508bedb604e6a9859eb8654b"]
Body: {"reason":"查询成功!","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"EUR","currencyT_Name":"欧元","currencyFD":"1","exchange":"0.1185","result":"0.1185","updateTime":"2025-07-06 21:52:00"},{"currencyF":"EUR","currencyF_Name":"欧元","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"8.4357","result":"8.4357","updateTime":"2025-07-06 21:52:00"}],"error_code":0}

2025-07-06 21:57:35 DEBUG [reactor-http-nio-13] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: 查询成功!
2025-07-06 21:57:35 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> EUR
2025-07-06 21:57:35 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-EUR = 0.1185
2025-07-06 21:57:35 DEBUG [reactor-http-nio-11] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.WebClientConfig - [响应信息]
Status: 200 OK
Headers: [Date:"Sun, 06 Jul 2025 13:57:35 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=dfda6d76b2f29ca201022ea9768639936b364700cc4394acb5c260ad4e6d73d5; Path=/; HttpOnly", Etag:"5570ab0d508bedb604e6a9859eb8654b"]
Body: {"reason":"查询成功!","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"EUR","currencyT_Name":"欧元","currencyFD":"1","exchange":"0.1185","result":"0.1185","updateTime":"2025-07-06 21:52:00"},{"currencyF":"EUR","currencyF_Name":"欧元","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"8.4357","result":"8.4357","updateTime":"2025-07-06 21:52:00"}],"error_code":0}

2025-07-06 21:57:35 DEBUG [reactor-http-nio-11] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: 查询成功!
2025-07-06 21:57:35 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> EUR
2025-07-06 21:57:35 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-EUR = 0.1185
2025-07-06 21:57:36 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到JPY的汇率
2025-07-06 21:57:36 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.WebClientConfig - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=JPY
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-07-06 21:57:36 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到JPY的汇率
2025-07-06 21:57:36 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.WebClientConfig - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=JPY
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-07-06 21:57:36 DEBUG [reactor-http-nio-13] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.WebClientConfig - [响应信息]
Status: 200 OK
Headers: [Date:"Sun, 06 Jul 2025 13:57:36 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=fa2704de489b93d2d9912e8c7be9278f0ecfdefcb38903200d124d4cf2c33294; Path=/; HttpOnly", Etag:"5570ab0d508bedb604e6a9859eb8654b"]
Body: {"reason":"查询成功!","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"JPY","currencyT_Name":"日元","currencyFD":"1","exchange":"20.1073","result":"20.1073","updateTime":"2025-07-06 21:52:00"},{"currencyF":"JPY","currencyF_Name":"日元","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"0.04973","result":"0.04973","updateTime":"2025-07-06 21:52:00"}],"error_code":0}

2025-07-06 21:57:36 DEBUG [reactor-http-nio-13] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: 查询成功!
2025-07-06 21:57:36 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> JPY
2025-07-06 21:57:36 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-JPY = 20.1073
2025-07-06 21:57:36 DEBUG [reactor-http-nio-11] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.WebClientConfig - [响应信息]
Status: 200 OK
Headers: [Date:"Sun, 06 Jul 2025 13:57:36 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=b1cbce48667a455b37dfed33778d9e1df7d9c60796016bbdbe38ac22fdabb06e; Path=/; HttpOnly", Etag:"5570ab0d508bedb604e6a9859eb8654b"]
Body: {"reason":"查询成功!","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"JPY","currencyT_Name":"日元","currencyFD":"1","exchange":"20.1073","result":"20.1073","updateTime":"2025-07-06 21:52:00"},{"currencyF":"JPY","currencyF_Name":"日元","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"0.04973","result":"0.04973","updateTime":"2025-07-06 21:52:00"}],"error_code":0}

2025-07-06 21:57:36 DEBUG [reactor-http-nio-11] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: 查询成功!
2025-07-06 21:57:36 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> JPY
2025-07-06 21:57:36 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-JPY = 20.1073
2025-07-06 21:57:36 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到KRW的汇率
2025-07-06 21:57:36 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.WebClientConfig - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=KRW
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-07-06 21:57:36 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到KRW的汇率
2025-07-06 21:57:36 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.WebClientConfig - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=KRW
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-07-06 21:57:36 DEBUG [reactor-http-nio-13] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.WebClientConfig - [响应信息]
Status: 200 OK
Headers: [Date:"Sun, 06 Jul 2025 13:57:36 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=1e0cf8049e1dc00f1c878475ce24ce057b6f398a8e17042c002a7bd5a4df7f30; Path=/; HttpOnly", Etag:"5570ab0d508bedb604e6a9859eb8654b"]
Body: {"reason":"查询成功!","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"KRW","currencyT_Name":"韩元","currencyFD":"1","exchange":"190.1975","result":"190.1975","updateTime":"2025-07-06 21:52:00"},{"currencyF":"KRW","currencyF_Name":"韩元","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"0.005258","result":"0.005258","updateTime":"2025-07-06 21:52:00"}],"error_code":0}

2025-07-06 21:57:36 DEBUG [reactor-http-nio-13] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: 查询成功!
2025-07-06 21:57:36 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> KRW
2025-07-06 21:57:36 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-KRW = 190.1975
2025-07-06 21:57:36 DEBUG [reactor-http-nio-11] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.WebClientConfig - [响应信息]
Status: 200 OK
Headers: [Date:"Sun, 06 Jul 2025 13:57:36 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=e40ce6ad77bde6960f5edde22e91f49566c83c9bbf0e7ceddc5c196f51c1cf2b; Path=/; HttpOnly", Etag:"973748d511f7b572138de007096af38e"]
Body: {"reason":"查询成功!","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"KRW","currencyT_Name":"韩元","currencyFD":"1","exchange":"190.1975","result":"190.1975","updateTime":"2025-07-06 21:52:00"},{"currencyF":"KRW","currencyF_Name":"韩元","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"0.005258","result":"0.005258","updateTime":"2025-07-06 21:52:00"}],"error_code":0}

2025-07-06 21:57:36 DEBUG [reactor-http-nio-11] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: 查询成功!
2025-07-06 21:57:36 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> KRW
2025-07-06 21:57:36 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-KRW = 190.1975
2025-07-06 21:57:36 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到INR的汇率
2025-07-06 21:57:36 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.WebClientConfig - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=INR
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-07-06 21:57:36 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到INR的汇率
2025-07-06 21:57:36 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.WebClientConfig - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=INR
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-07-06 21:57:36 DEBUG [reactor-http-nio-13] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.WebClientConfig - [响应信息]
Status: 200 OK
Headers: [Date:"Sun, 06 Jul 2025 13:57:36 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=cf72b3430e93a76e1dae00e321a84825c5694ba451428a38c1b156c29946f8cc; Path=/; HttpOnly", Etag:"5570ab0d508bedb604e6a9859eb8654b"]
Body: {"reason":"查询成功!","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"INR","currencyT_Name":"印度卢比","currencyFD":"1","exchange":"11.9314","result":"11.9314","updateTime":"2025-07-06 21:52:00"},{"currencyF":"INR","currencyF_Name":"印度卢比","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"0.08381","result":"0.08381","updateTime":"2025-07-06 21:52:00"}],"error_code":0}

2025-07-06 21:57:36 DEBUG [reactor-http-nio-13] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: 查询成功!
2025-07-06 21:57:36 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> INR
2025-07-06 21:57:36 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-INR = 11.9314
2025-07-06 21:57:36 DEBUG [reactor-http-nio-11] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.WebClientConfig - [响应信息]
Status: 200 OK
Headers: [Date:"Sun, 06 Jul 2025 13:57:36 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=e45465f0a6c7135c155dd5761c0cc05ab5568f7685ad7d9ca8f75de7aefe190f; Path=/; HttpOnly", Etag:"5570ab0d508bedb604e6a9859eb8654b"]
Body: {"reason":"查询成功!","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"INR","currencyT_Name":"印度卢比","currencyFD":"1","exchange":"11.9314","result":"11.9314","updateTime":"2025-07-06 21:52:00"},{"currencyF":"INR","currencyF_Name":"印度卢比","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"0.08381","result":"0.08381","updateTime":"2025-07-06 21:52:00"}],"error_code":0}

2025-07-06 21:57:36 DEBUG [reactor-http-nio-11] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: 查询成功!
2025-07-06 21:57:36 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> INR
2025-07-06 21:57:36 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-INR = 11.9314
2025-07-06 21:57:37 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.domain.util.CurrencyConversionUtils - 批量更新汇率缓存，共 5 条记录
2025-07-06 21:57:37 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - CNY基础汇率缓存更新完成: 成功 5/5 条记录
2025-07-06 21:57:37 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 定时刷新汇率缓存完成，缓存条目数: 5
2025-07-06 21:57:37 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.domain.util.CurrencyConversionUtils - 批量更新汇率缓存，共 5 条记录
2025-07-06 21:57:37 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - CNY基础汇率缓存更新完成: 成功 5/5 条记录
2025-07-06 21:57:37 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 汇率缓存初始化完成，缓存条目数: 5
2025-07-06 21:57:37 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - ----------------------------------------------
2025-07-06 21:57:37 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Fulfillmen Shop service started successfully.
2025-07-06 21:57:37 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - API地址：http://127.0.0.1:8080
2025-07-06 21:57:37 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - API文档：http://127.0.0.1:8080/doc.html
2025-07-06 21:57:37 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - ----------------------------------------------
2025-07-06 21:57:37 INFO  [RMI TCP Connection(1)-127.0.0.1] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-06 21:57:37 INFO  [RMI TCP Connection(2)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-06 21:57:37 INFO  [RMI TCP Connection(1)-127.0.0.1] [tid::uId::ip::os::browser:] org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-06 21:57:37 INFO  [RMI TCP Connection(1)-127.0.0.1] [tid::uId::ip::os::browser:] org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-06 21:57:37 INFO  [RMI TCP Connection(2)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@6eb46068
2025-07-06 21:57:37 INFO  [RMI TCP Connection(2)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-06 21:58:47 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - [1094018464715038722]:[0] 忽略表的多租户处理: tenant_domains
2025-07-06 21:58:48 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] c.f.shop.manager.service.TenantResolverService - [1094018464715038722]:[0] 从自定义域名(localhost)解析到租户ID: 10000
2025-07-06 21:58:48 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094018464715038722]:[0] 租户缓存未命中: 10000
2025-07-06 21:58:48 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094018464715038722]:[0] 缓存未命中，从数据库加载租户数据: 10000
2025-07-06 21:58:48 DEBUG [XNIO-1 task-3] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - [1094018464715038720]:[0] 忽略表的多租户处理: tenant_domains
2025-07-06 21:58:48 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - [1094018464715038722]:[0] 忽略表的多租户处理: tenants
2025-07-06 21:58:48 DEBUG [XNIO-1 task-3] [tid::uId::ip::os::browser:] c.f.shop.manager.service.TenantResolverService - [1094018464715038720]:[0] 从自定义域名(localhost)解析到租户ID: 10000
2025-07-06 21:58:48 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094018464715038720]:[0] 租户缓存未命中: 10000
2025-07-06 21:58:48 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094018464715038720]:[0] 缓存未命中，从数据库加载租户数据: 10000
2025-07-06 21:58:48 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - [1094018464715038722]:[0] 忽略表的多租户处理: tenants_info
2025-07-06 21:58:48 DEBUG [XNIO-1 task-4] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - [1094018464715038721]:[0] 忽略表的多租户处理: tenant_domains
2025-07-06 21:58:48 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - [1094018464715038720]:[0] 忽略表的多租户处理: tenants
2025-07-06 21:58:48 DEBUG [XNIO-1 task-4] [tid::uId::ip::os::browser:] c.f.shop.manager.service.TenantResolverService - [1094018464715038721]:[0] 从自定义域名(localhost)解析到租户ID: 10000
2025-07-06 21:58:48 DEBUG [XNIO-1 task-4] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094018464715038721]:[0] 租户缓存未命中: 10000
2025-07-06 21:58:48 DEBUG [XNIO-1 task-4] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094018464715038721]:[0] 缓存未命中，从数据库加载租户数据: 10000
2025-07-06 21:58:48 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - [1094018464715038720]:[0] 忽略表的多租户处理: tenants_info
2025-07-06 21:58:48 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - [1094018464715038722]:[0] 忽略表的多租户处理: tenant_plan_relation
2025-07-06 21:58:48 DEBUG [XNIO-1 task-4] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - [1094018464715038721]:[0] 忽略表的多租户处理: tenants
2025-07-06 21:58:48 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - [1094018464715038720]:[0] 忽略表的多租户处理: tenant_plan_relation
2025-07-06 21:58:48 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - [1094018464715038722]:[0] 忽略表的多租户处理: tenant_domains
2025-07-06 21:58:48 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - [1094018464715038720]:[0] 忽略表的多租户处理: tenant_domains
2025-07-06 21:58:48 DEBUG [XNIO-1 task-4] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - [1094018464715038721]:[0] 忽略表的多租户处理: tenants_info
2025-07-06 21:58:48 DEBUG [XNIO-1 task-4] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - [1094018464715038721]:[0] 忽略表的多租户处理: tenant_plan_relation
2025-07-06 21:58:48 DEBUG [XNIO-1 task-4] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - [1094018464715038721]:[0] 忽略表的多租户处理: tenant_domains
2025-07-06 21:58:48 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - [1094018464715038720]:[0] 忽略表的多租户处理: tenant_plans
2025-07-06 21:58:48 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - [1094018464715038722]:[0] 忽略表的多租户处理: tenant_plans
2025-07-06 21:58:48 DEBUG [XNIO-1 task-4] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - [1094018464715038721]:[0] 忽略表的多租户处理: tenant_plans
2025-07-06 21:58:48 DEBUG [XNIO-1 task-4] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094018464715038721]:[0] 租户缓存设置成功: 10000, 过期时间: 14400秒
2025-07-06 21:58:48 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094018464715038720]:[0] 租户缓存设置成功: 10000, 过期时间: 14400秒
2025-07-06 21:58:48 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094018464715038722]:[0] 租户缓存设置成功: 10000, 过期时间: 14400秒
2025-07-06 21:58:48 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094018464715038722]:[0] 设置增强租户上下文: tenantId=10000, tenantName=Fulfillmen
2025-07-06 21:58:48 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094018464715038720]:[0] 设置增强租户上下文: tenantId=10000, tenantName=Fulfillmen
2025-07-06 21:58:48 DEBUG [XNIO-1 task-4] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094018464715038721]:[0] 设置增强租户上下文: tenantId=10000, tenantName=Fulfillmen
2025-07-06 21:58:48 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094018464715038722]:[0] 数据库加载成功并缓存: 租户ID=10000, 租户名称=Fulfillmen
2025-07-06 21:58:48 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094018464715038720]:[0] 数据库加载成功并缓存: 租户ID=10000, 租户名称=Fulfillmen
2025-07-06 21:58:48 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094018464715038720]:[0] 过滤器设置当前请求的租户ID: 10000 (URI: /api/home/<USER>
2025-07-06 21:58:48 DEBUG [XNIO-1 task-4] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094018464715038721]:[0] 数据库加载成功并缓存: 租户ID=10000, 租户名称=Fulfillmen
2025-07-06 21:58:48 DEBUG [XNIO-1 task-4] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094018464715038721]:[0] 过滤器设置当前请求的租户ID: 10000 (URI: /api/home/<USER>
2025-07-06 21:58:48 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094018464715038722]:[0] 过滤器设置当前请求的租户ID: 10000 (URI: /api/home/<USER>
2025-07-06 21:58:48 DEBUG [XNIO-1 task-4] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094018464715038721]:[0] [RegexCors] 正则规则 http://localhost:[0-9]+ 匹配来源: http://localhost:5173
2025-07-06 21:58:48 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094018464715038720]:[0] [RegexCors] 正则规则 http://localhost:[0-9]+ 匹配来源: http://localhost:5173
2025-07-06 21:58:48 DEBUG [XNIO-1 task-4] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094018464715038721]:[0] [RegexCors] 正则匹配成功: http://localhost:5173
2025-07-06 21:58:48 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094018464715038722]:[0] [RegexCors] 正则规则 http://localhost:[0-9]+ 匹配来源: http://localhost:5173
2025-07-06 21:58:48 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094018464715038720]:[0] [RegexCors] 正则匹配成功: http://localhost:5173
2025-07-06 21:58:48 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094018464715038722]:[0] [RegexCors] 正则匹配成功: http://localhost:5173
2025-07-06 21:58:48 DEBUG [XNIO-1 task-4] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.config.satoken.SaExtensionInterceptor - [1094018464715038721]:[0] auth check result: true
2025-07-06 21:58:48 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.config.satoken.SaExtensionInterceptor - [1094018464715038720]:[0] auth check result: true
2025-07-06 21:58:48 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.config.satoken.SaExtensionInterceptor - [1094018464715038722]:[0] auth check result: true
2025-07-06 21:58:48 INFO  [XNIO-1 task-4] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094018464715038721]:[0] [GET] /api/home/<USER>
2025-07-06 21:58:48 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094018464715038722]:[0] [GET] /api/home/<USER>
2025-07-06 21:58:48 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094018464715038720]:[0] [GET] /api/home/<USER>
2025-07-06 21:58:48 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1094018464715038722]:[0] 全局限流检查通过: key=rate_limit:ip:0:0:0:0:0:0:0:1, current=1/100
2025-07-06 21:58:48 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1094018464715038720]:[0] 全局限流检查通过: key=rate_limit:ip:0:0:0:0:0:0:0:1, current=1/100
2025-07-06 21:58:48 DEBUG [XNIO-1 task-4] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1094018464715038721]:[0] 全局限流检查通过: key=rate_limit:ip:0:0:0:0:0:0:0:1, current=1/100
2025-07-06 21:58:48 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.HomeServiceImpl - [1094018464715038722]:[0] 加载热门商品列表（带缓存和同步入库）
2025-07-06 21:58:48 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.HomeServiceImpl - [1094018464715038720]:[0] 搜索电子产品（带缓存和同步入库）
2025-07-06 21:58:48 INFO  [XNIO-1 task-4] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.HomeServiceImpl - [1094018464715038721]:[0] 加载所有类目数据
2025-07-06 21:58:48 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094018464715038722]:[0] 开始获取推荐商品数据，语言: EN, 页码: 1, 每页大小: 20
2025-07-06 21:58:48 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094018464715038722]:[0] 推荐商品缓存获取开始，语言: EN, 页码: 1, 强制刷新: false
2025-07-06 21:58:48 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094018464715038722]:[0] 推荐商品缓存未命中，执行同步, key: recommend:EN:1:20
2025-07-06 21:58:48 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094018464715038722]:[0] 开始同步推荐商品数据，语言: EN, 页码: 1, 每页大小: 20
2025-07-06 21:58:48 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094018464715038720]:[0] 缓存未命中，执行搜索, fullKey: search:65d43bf503e3410204fa882720c56e62, baseKey: search:27eac5f609a7339af532c14c7e27214e:nopag
2025-07-06 21:58:48 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094018464715038720]:[0] 开始同步搜索商品数据，请求参数: ProductSearchRequestDTO(keyword=null, language=null, page=1, pageSize=10, categoryId=null, categoryIdList=null, regionOpp=null, productCollectionId=262069983, snId=null, keywordTranslate=null, minPrice=null, maxPrice=null, sortField=null, sortOrder=null, filter=null, imageId=null, imageUrl=null, saleFilterParams=null)
2025-07-06 21:58:48 DEBUG [XNIO-1 task-4] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] com.fulfillmen.shop.config.TenantConfig - [1094018464715038721]:[0] 忽略表的多租户处理: sys_alibaba_category
2025-07-06 21:58:48 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.fulfillmen.support.alibaba.sign.AlibabaSignature - [1094018464715038722]:[0] 签名因子: param2/1/com.alibaba.fenxiao.crossborder/product.search.offerRecommend/8390330_aop_timestamp1751810328134access_tokenb49dfbf3-c3d7-4dfa-9b50-2d3300bad6c2recommendOfferParam{"country":"en","beginPage":1,"pageSize":20,"outMemberId":null} 签名: B3E48F642E57E67BB37AD9B0C933BEE95A052434
2025-07-06 21:58:48 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.fulfillmen.support.alibaba.sign.AlibabaSignature - [1094018464715038720]:[0] 签名因子: param2/1/com.alibaba.fenxiao.crossborder/product.search.keywordQuery/8390330_aop_timestamp1751810328137access_tokenb49dfbf3-c3d7-4dfa-9b50-2d3300bad6c2offerQueryParam{"keyword":null,"country":"en","pageSize":10,"filter":null,"sort":null,"outMemberId":null,"priceStart":null,"priceEnd":null,"categoryId":null,"categoryIdList":null,"regionOpp":null,"productCollectionId":"262069983","snId":null,"keywordTranslate":null,"beginPage":1,"saleFilterList":null} 签名: 154908324A761211F909BAAA2879F370152349AD
2025-07-06 21:58:48 WARN  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] io.micrometer.core.instrument.MeterRegistry - [1094018464715038720]:[0] This Gauge has been already registered (MeterId{name='alibaba.api.concurrent.requests', tags=[]}), the registration will be ignored. Note that subsequent logs will be logged at debug level.
2025-07-06 21:58:48 INFO  [XNIO-1 task-4] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094018464715038721]:[0] [GET] /api/home/<USER>
2025-07-06 21:58:48 DEBUG [XNIO-1 task-4] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.fulfillmen.shop.common.context.UserContextHolder - [1094018464715038721]:[0] 清除用户上下文和 MDC 信息
2025-07-06 21:58:48 DEBUG [XNIO-1 task-4] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094018464715038721]:[0] 清理增强租户上下文
2025-07-06 21:58:48 DEBUG [XNIO-1 task-4] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094018464715038721]:[0] 过滤器清理租户上下文完成
2025-07-06 21:58:48 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094018464715038722]:[0] 开始同步推荐商品到数据库，数量: 20
2025-07-06 21:58:49 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] com.fulfillmen.shop.config.TenantConfig - [1094018464715038722]:[0] 忽略表的多租户处理: pdc_product_mapping
2025-07-06 21:58:49 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] com.fulfillmen.shop.config.TenantConfig - [1094018464715038722]:[0] 忽略表的多租户处理: pdc_product_mapping
2025-07-06 21:58:49 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094018464715038722]:[0] 批量插入成功，预期: 20 条，实际: 20 条
2025-07-06 21:58:49 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094018464715038722]:[0] 推荐商品同步完成，新增: 20 条，更新: 0 条
2025-07-06 21:58:49 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094018464715038722]:[0] 自动同步功能已关闭，跳过事件发布
2025-07-06 21:58:49 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094018464715038722]:[0] 推荐商品结果已缓存, key: recommend:EN:1:20
2025-07-06 21:58:49 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094018464715038722]:[0] 缓存统计 - Key: recommend:EN:1:20, Hit: false
2025-07-06 21:58:49 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.HomeServiceImpl - [1094018464715038722]:[0] 成功加载热门商品，数量: 20，ID同步状态：正常
2025-07-06 21:58:49 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094018464715038722]:[0] [GET] /api/home/<USER>
2025-07-06 21:58:49 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.fulfillmen.shop.common.context.UserContextHolder - [1094018464715038722]:[0] 清除用户上下文和 MDC 信息
2025-07-06 21:58:49 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094018464715038722]:[0] 清理增强租户上下文
2025-07-06 21:58:49 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094018464715038722]:[0] 过滤器清理租户上下文完成
2025-07-06 21:58:49 DEBUG [reactor-http-nio-14] [tid::uId::ip::os::browser:] c.f.s.manager.support.alibaba.impl.ProductManager - search result: GoodsSearchResponse.Result(success=true, code=200, message=null, result=GoodsSearchResponse.SearchResult(totalRecords=2000, totalPage=201, pageSize=10, currentPage=1, data=[GoodsSearchResponse.GoodsInfo(imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01jj690j1vJfYHdqU5R_!!*************-0-cib.jpg, subject=洗衣凝珠五合一超香水型持久留香珠除菌除螨强力去污浓缩型三盒一, subjectTrans=Laundry beads five-in-one super perfume type lasting fragrance beads sterilization mite removal strong decontamination concentrated type three boxes one, offerId=************, isJxhy=true, priceInfo=GoodsSearchResponse.PriceInfo(price=0.10, consignPrice=15.1, promotionPrice=null, jxhyPrice=null, pfJxhyPrice=null), repurchaseRate=17%, monthSold=2709774, traceInfo=object_id%40************%5Eobject_type%40offer%5Etrace%402150431a17518103286233708e16c4%5Ekeyword%40null%5EoutMemberId%40null, isOnePsale=true, sellerIdentities=[tp_member], offerIdentities=[yx, select], tradeScore=4.1, whiteImage=null, promotionModel=null, topCategoryId=*********, secondCategoryId=*********, thirdCategoryId=*********, isPatentProduct=false, createDate=2023-05-11 09:56:38, modifyDate=2025-07-06 20:17:29, isSelect=true, minOrderQuantity=100, sellerDataInfo=GoodsSearchResponse.SellerDataInfo(tradeMedalLevel=2, compositeServiceScore=4.0, logisticsExperienceScore=3.0, disputeComplaintScore=5.0, offerExperienceScore=5.0, afterSalesExperienceScore=5.0, consultingExperienceScore=2.0, repeatPurchasePercent=0.****************), productSimpleShippingInfo=null, promotionURL=https://detail.1688.com/offer/************.html?fromkv=refer:HVAVUVCTJVBFIR2NLFDDMTS2KJDTIM2EKFGUUWSHJUZUIRKOIJLUYNKRLBATESZXI5CTGVCLJVFFSSCFGNCE6TKSLJDUKMSUI5MDGM2QKU6T2PJ5HVEA1111&kjSource=pc, token=null), GoodsSearchResponse.GoodsInfo(imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01Vv28361m0aXZkDEn6_!!**********-0-cib.jpg, subject=批发开口喇叭圣诞节铃铛装饰10-50mm挂件风铃金属小吊饰圣诞铃铛, subjectTrans=Opening Bell-shaped Horn Bell Hanging Ornaments Christmas Bell diy10-50mm Pendant Wind Bell Metal Small Hanging Ornaments, offerId=************, isJxhy=false, priceInfo=GoodsSearchResponse.PriceInfo(price=0.06, consignPrice=2.96, promotionPrice=0.06, jxhyPrice=null, pfJxhyPrice=null), repurchaseRate=19%, monthSold=1395508, traceInfo=object_id%40************%5Eobject_type%40offer%5Etrace%402150431a17518103286233708e16c4%5Ekeyword%40null%5EoutMemberId%40null, isOnePsale=true, sellerIdentities=[super_factory, tp_member], offerIdentities=[select], tradeScore=5.0, whiteImage=null, promotionModel=GoodsSearchResponse.PromotionModel(hasPromotion=true, promotionType=plus), topCategoryId=67, secondCategoryId=*********, thirdCategoryId=*********, isPatentProduct=false, createDate=2022-07-16 18:29:08, modifyDate=2025-07-06 11:54:41, isSelect=true, minOrderQuantity=1, sellerDataInfo=GoodsSearchResponse.SellerDataInfo(tradeMedalLevel=5, compositeServiceScore=4.5, logisticsExperienceScore=4.0, disputeComplaintScore=4.0, offerExperienceScore=3.0, afterSalesExperienceScore=4.0, consultingExperienceScore=3.5, repeatPurchasePercent=0.****************), productSimpleShippingInfo=GoodsSearchResponse.ProductShippingInfo(shippingTimeGuarantee=shipIn48Hours), promotionURL=https://detail.1688.com/offer/************.html?fromkv=refer:HVAVUVCTJVBFIR2NLFDDMTSSLBEEKMSEIVHFEVSIIU2FIR2OKJLEYNKRLBATESZXI5CTGVCLJVFFSSCFGNCE6TKSLJDUKMSUI5MDGM2QKU6T2PJ5HVEA1111&kjSource=pc, token=null), GoodsSearchResponse.GoodsInfo(imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01eOj9s6219wo0NO3rW_!!**********-0-cib.jpg, subject=塑料地钉 户外天幕防风沙滩钉 帐篷配件野营帐篷地丁 螺纹帐篷钉, subjectTrans=Plastic ground nail outdoor canopy windproof beach nail tent accessories camping tent ground thread tent nail, offerId=************, isJxhy=true, priceInfo=GoodsSearchResponse.PriceInfo(price=0.05, consignPrice=4.05, promotionPrice=null, jxhyPrice=null, pfJxhyPrice=null), repurchaseRate=39%, monthSold=2961108, traceInfo=object_id%40************%5Eobject_type%40offer%5Etrace%402150431a17518103286233708e16c4%5Ekeyword%40null%5EoutMemberId%40null, isOnePsale=true, sellerIdentities=[powerful_merchants, tp_member], offerIdentities=[select], tradeScore=5.0, whiteImage=https://cbu01.alicdn.com/img/ibank/O1CN01hj1kzo1Bs2uuyw2of_!!0-0-cib.jpg, promotionModel=null, topCategoryId=18, secondCategoryId=281904, thirdCategoryId=*********, isPatentProduct=false, createDate=2023-11-10 11:34:48, modifyDate=2025-07-06 12:00:56, isSelect=true, minOrderQuantity=2, sellerDataInfo=GoodsSearchResponse.SellerDataInfo(tradeMedalLevel=5, compositeServiceScore=4.0, logisticsExperienceScore=3.5, disputeComplaintScore=4.0, offerExperienceScore=3.0, afterSalesExperienceScore=3.7, consultingExperienceScore=2.0, repeatPurchasePercent=0.****************), productSimpleShippingInfo=null, promotionURL=https://detail.1688.com/offer/************.html?fromkv=refer:HVAVUVCTJVBFIR2NLFDDMTS2KVEECWKUI5HEUWKHLFNFISKOJJLEYNKRLBATESZXI5CTGVCLJVFFSSCFGNCE6TKSLJDUKMSUI5MDGM2QKU6T2PJ5HVEA1111&kjSource=pc, token=null), GoodsSearchResponse.GoodsInfo(imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01gYjdMm1S86gKsJEax_!!*************-0-cib.jpg, subject=一次性医用外科口罩医用级独立包装不勒耳10个装三层防护白色口罩, subjectTrans=Disposable Medical Surgical Masks Medical Grade Independent Packaging 10 Pack Three-Layer Protective White Masks, offerId=************, isJxhy=true, priceInfo=GoodsSearchResponse.PriceInfo(price=0.07, consignPrice=2.57, promotionPrice=null, jxhyPrice=null, pfJxhyPrice=null), repurchaseRate=17%, monthSold=********, traceInfo=object_id%40************%5Eobject_type%40offer%5Etrace%402150431a17518103286233708e16c4%5Ekeyword%40null%5EoutMemberId%40null, isOnePsale=true, sellerIdentities=[tp_member], offerIdentities=[select], tradeScore=4.8, whiteImage=null, promotionModel=null, topCategoryId=70, secondCategoryId=*********, thirdCategoryId=*********, isPatentProduct=false, createDate=2024-03-27 13:14:09, modifyDate=2025-07-06 04:56:06, isSelect=true, minOrderQuantity=100, sellerDataInfo=GoodsSearchResponse.SellerDataInfo(tradeMedalLevel=3, compositeServiceScore=4.5, logisticsExperienceScore=3.5, disputeComplaintScore=5.0, offerExperienceScore=5.0, afterSalesExperienceScore=3.1, consultingExperienceScore=4.5, repeatPurchasePercent=0.****************), productSimpleShippingInfo=GoodsSearchResponse.ProductShippingInfo(shippingTimeGuarantee=shipIn48Hours), promotionURL=https://detail.1688.com/offer/************.html?fromkv=refer:HVAVUVCTJVBFIR2NLFDDMTS2LBDTIM2UJVGVEVKHLFMUIT2NJJLEYNKRLBATESZXI5CTGVCLJVFFSSCFGNCE6TKSLJDUKMSUI5MDGM2QKU6T2PJ5HVEA1111&kjSource=pc, token=null), GoodsSearchResponse.GoodsInfo(imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01gQulQn1r16x1vOhZl_!!*************-0-cib.jpg, subject=吸色片防串色洗衣片防串色吸色片防串色染色片染色片散装色母片, subjectTrans=Color Absorbing Film Anti-cross-color Laundry Film Anti-cross-color Absorbing Film Anti-cross-color Dyeing Film Dyeing Film Bulk Color Masterbatch, offerId=************, isJxhy=false, priceInfo=GoodsSearchResponse.PriceInfo(price=0.04, consignPrice=0.04, promotionPrice=null, jxhyPrice=null, pfJxhyPrice=null), repurchaseRate=0%, monthSold=0, traceInfo=object_id%40************%5Eobject_type%40offer%5Etrace%402150431a17518103286233708e16c4%5Ekeyword%40null%5EoutMemberId%40null, isOnePsale=false, sellerIdentities=[tp_member], offerIdentities=[], tradeScore=, whiteImage=null, promotionModel=null, topCategoryId=*********, secondCategoryId=*********, thirdCategoryId=*********, isPatentProduct=false, createDate=2025-04-17 13:55:30, modifyDate=2025-06-27 02:18:19, isSelect=false, minOrderQuantity=100, sellerDataInfo=GoodsSearchResponse.SellerDataInfo(tradeMedalLevel=3, compositeServiceScore=3.5, logisticsExperienceScore=3.0, disputeComplaintScore=4.0, offerExperienceScore=0.0, afterSalesExperienceScore=3.3, consultingExperienceScore=2.0, repeatPurchasePercent=0.*****************), productSimpleShippingInfo=GoodsSearchResponse.ProductShippingInfo(shippingTimeGuarantee=shipIn48Hours), promotionURL=https://detail.1688.com/offer/************.html?fromkv=refer:HVAVUVCTJVBFIR2NLFDDMT2KKJDU2WKUIFHFUWKIIFMUIQ2PIJLUYNKRLBATESZXI5CTGVCLJVFFSSCFGNCE6TKSLJDUKMSUI5MDGM2QKU6T2PJ5HVEA1111&kjSource=pc, token=null), GoodsSearchResponse.GoodsInfo(imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01MNt4LD1Ma96aION0m_!!*************-0-cib.jpg, subject=厂家现货塑料插扣织带调节卡扣快拆目字日字扣安全背包书包绳扣具, subjectTrans=Manufacturer in stock plastic buckle ribbon adjustable buckle quick release Japanese buckle safety backpack schoolbag rope buckle, offerId=************, isJxhy=true, priceInfo=GoodsSearchResponse.PriceInfo(price=0.07, consignPrice=3.57, promotionPrice=0.07, jxhyPrice=null, pfJxhyPrice=null), repurchaseRate=44%, monthSold=7661282, traceInfo=object_id%40************%5Eobject_type%40offer%5Etrace%402150431a17518103286233708e16c4%5Ekeyword%40null%5EoutMemberId%40null, isOnePsale=true, sellerIdentities=[tp_member], offerIdentities=[select], tradeScore=5.0, whiteImage=null, promotionModel=GoodsSearchResponse.PromotionModel(hasPromotion=true, promotionType=plus), topCategoryId=1042954, secondCategoryId=*********, thirdCategoryId=*********, isPatentProduct=false, createDate=2023-02-07 17:55:27, modifyDate=2025-07-04 09:06:34, isSelect=true, minOrderQuantity=200, sellerDataInfo=GoodsSearchResponse.SellerDataInfo(tradeMedalLevel=3, compositeServiceScore=5.0, logisticsExperienceScore=4.5, disputeComplaintScore=4.0, offerExperienceScore=4.0, afterSalesExperienceScore=4.4, consultingExperienceScore=4.0, repeatPurchasePercent=0.****************), productSimpleShippingInfo=GoodsSearchResponse.ProductShippingInfo(shippingTimeGuarantee=shipIn48Hours), promotionURL=https://detail.1688.com/offer/************.html?fromkv=refer:HVAVUVCTJVBFIR2NLFDDMTS2KFDUKWSEI5HUEUKHLEZUIS2OKJMEYNKRLBATESZXI5CTGVCLJVFFSSCFGNCE6TKSLJDUKMSUI5MDGM2QKU6T2PJ5HVEA1111&kjSource=pc, token=null), GoodsSearchResponse.GoodsInfo(imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01jHPuFv1d3wiWFnnKa_!!*************-0-cib.jpg, subject=一次性手环现货儿童游乐场手腕带会展演唱会防水门票杜邦纸质腕带, subjectTrans=Disposable Bracelet Spot Children's Playground Wristband Exhibition Concert Waterproof Tickets DuPont Paper Wristband, offerId=************, isJxhy=false, priceInfo=GoodsSearchResponse.PriceInfo(price=0.04, consignPrice=5.04, promotionPrice=null, jxhyPrice=null, pfJxhyPrice=null), repurchaseRate=16%, monthSold=********, traceInfo=object_id%40************%5Eobject_type%40offer%5Etrace%402150431a17518103286233708e16c4%5Ekeyword%40null%5EoutMemberId%40null, isOnePsale=true, sellerIdentities=[tp_member], offerIdentities=[select], tradeScore=5.0, whiteImage=null, promotionModel=null, topCategoryId=54, secondCategoryId=*********, thirdCategoryId=*********, isPatentProduct=false, createDate=2022-12-05 15:36:39, modifyDate=2025-07-06 12:11:53, isSelect=true, minOrderQuantity=1, sellerDataInfo=GoodsSearchResponse.SellerDataInfo(tradeMedalLevel=4, compositeServiceScore=4.5, logisticsExperienceScore=4.0, disputeComplaintScore=4.0, offerExperienceScore=3.0, afterSalesExperienceScore=4.0, consultingExperienceScore=4.0, repeatPurchasePercent=0.****************), productSimpleShippingInfo=GoodsSearchResponse.ProductShippingInfo(shippingTimeGuarantee=shipIn48Hours), promotionURL=https://detail.1688.com/offer/************.html?fromkv=refer:HVAVUVCTJVBFIR2NLFDDMTSSLJDVCWSEI5HEUVKHLFMVIRKNJJLEYNKRLBATESZXI5CTGVCLJVFFSSCFGNCE6TKSLJDUKMSUI5MDGM2QKU6T2PJ5HVEA1111&kjSource=pc, token=null), GoodsSearchResponse.GoodsInfo(imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01MhuR0Z1KKuisKG0hJ_!!*************-0-cib.jpg, subject=原创可爱镭射咕卡贴纸DIY明星贴纸文具贴纸装饰画手账贴纸批发, subjectTrans=Original cute laser goo card sticker DIY star sticker stationery sticker decorative painting hand account sticker wholesale, offerId=************, isJxhy=true, priceInfo=GoodsSearchResponse.PriceInfo(price=0.03, consignPrice=3.03, promotionPrice=null, jxhyPrice=null, pfJxhyPrice=null), repurchaseRate=33%, monthSold=********, traceInfo=object_id%40************%5Eobject_type%40offer%5Etrace%402150431a17518103286233708e16c4%5Ekeyword%40null%5EoutMemberId%40null, isOnePsale=true, sellerIdentities=[tp_member], offerIdentities=[select], tradeScore=5.0, whiteImage=null, promotionModel=null, topCategoryId=67, secondCategoryId=*********, thirdCategoryId=*********, isPatentProduct=false, createDate=2023-04-01 20:45:15, modifyDate=2025-07-06 12:12:45, isSelect=true, minOrderQuantity=1, sellerDataInfo=GoodsSearchResponse.SellerDataInfo(tradeMedalLevel=3, compositeServiceScore=4.0, logisticsExperienceScore=3.0, disputeComplaintScore=4.0, offerExperienceScore=3.0, afterSalesExperienceScore=3.3, consultingExperienceScore=4.0, repeatPurchasePercent=0.***************), productSimpleShippingInfo=null, promotionURL=https://detail.1688.com/offer/************.html?fromkv=refer:HVAVUVCTJVBFIR2NLFDDMTS2KJDUCWSUIVHUEVSHIE2EIUKPJJJUYNKRLBATESZXI5CTGVCLJVFFSSCFGNCE6TKSLJDUKMSUI5MDGM2QKU6T2PJ5HVEA1111&kjSource=pc, token=null), GoodsSearchResponse.GoodsInfo(imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01thdyO42MK7bY5fQZX_!!*************-0-cib.jpg, subject=跨境绿植婚庆插花伴手礼搭配装饰假花单支金钱叶尤加利叶仿真植物, subjectTrans=Cross-Border Green Plant Wedding Flower Arrangement Souvenir with Decorative Artificial Flower Single Money Leaf Eucalyptus Leaf Artificial Plant, offerId=************, isJxhy=false, priceInfo=GoodsSearchResponse.PriceInfo(price=0.23, consignPrice=3.46, promotionPrice=null, jxhyPrice=null, pfJxhyPrice=null), repurchaseRate=34%, monthSold=3648901, traceInfo=object_id%40************%5Eobject_type%40offer%5Etrace%402150431a17518103286233708e16c4%5Ekeyword%40null%5EoutMemberId%40null, isOnePsale=true, sellerIdentities=[powerful_merchants, tp_member], offerIdentities=[select], tradeScore=5.0, whiteImage=https://cbu01.alicdn.com/img/ibank/O1CN01nVDvGq1Bs2usv7YpI_!!0-0-cib.jpg, promotionModel=null, topCategoryId=67, secondCategoryId=*********, thirdCategoryId=*********, isPatentProduct=false, createDate=2023-02-13 11:17:59, modifyDate=2025-07-05 11:52:48, isSelect=true, minOrderQuantity=1, sellerDataInfo=GoodsSearchResponse.SellerDataInfo(tradeMedalLevel=4, compositeServiceScore=4.0, logisticsExperienceScore=3.5, disputeComplaintScore=4.0, offerExperienceScore=4.0, afterSalesExperienceScore=3.6, consultingExperienceScore=3.5, repeatPurchasePercent=0.****************), productSimpleShippingInfo=GoodsSearchResponse.ProductShippingInfo(shippingTimeGuarantee=shipIn48Hours), promotionURL=https://detail.1688.com/offer/************.html?fromkv=refer:HVAVUVCTJVBFIR2NLFDDMTS2KFDUSWKEINGUUU2HIVNEIR2NKJKUYNKRLBATESZXI5CTGVCLJVFFSSCFGNCE6TKSLJDUKMSUI5MDGM2QKU6T2PJ5HVEA1111&kjSource=pc, token=null), GoodsSearchResponse.GoodsInfo(imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01ZU4IRJ1PMM2xLgZyi_!!*************-0-cib.jpg, subject=新款锌合金弹簧圈开口龙虾扣门扣钥匙扣版扣配件洞洞鞋大圈材料, subjectTrans=New zinc alloy spring ring open Lobster clasp door buckle keychain version buckle accessories hole shoes big ring material, offerId=************, isJxhy=false, priceInfo=GoodsSearchResponse.PriceInfo(price=0.25, consignPrice=3.25, promotionPrice=0.24, jxhyPrice=null, pfJxhyPrice=null), repurchaseRate=19%, monthSold=1425371, traceInfo=object_id%40************%5Eobject_type%40offer%5Etrace%402150431a17518103286233708e16c4%5Ekeyword%40null%5EoutMemberId%40null, isOnePsale=true, sellerIdentities=[super_factory, tp_member], offerIdentities=[select], tradeScore=4.9, whiteImage=null, promotionModel=GoodsSearchResponse.PromotionModel(hasPromotion=true, promotionType=plus), topCategoryId=54, secondCategoryId=*********, thirdCategoryId=*********, isPatentProduct=false, createDate=2023-04-16 19:30:26, modifyDate=2025-07-06 16:37:18, isSelect=true, minOrderQuantity=1, sellerDataInfo=GoodsSearchResponse.SellerDataInfo(tradeMedalLevel=4, compositeServiceScore=4.0, logisticsExperienceScore=4.5, disputeComplaintScore=4.0, offerExperienceScore=2.0, afterSalesExperienceScore=4.0, consultingExperienceScore=3.0, repeatPurchasePercent=0.6964325570220784), productSimpleShippingInfo=GoodsSearchResponse.ProductShippingInfo(shippingTimeGuarantee=shipIn48Hours), promotionURL=https://detail.1688.com/offer/************.html?fromkv=refer:HVAVUVCTJVBFIR2NLFDDMTS2KJDUSNCEIFHEEUSHIUZUIUKNIJKEYNKRLBATESZXI5CTGVCLJVFFSSCFGNCE6TKSLJDUKMSUI5MDGM2QKU6T2PJ5HVEA1111&kjSource=pc, token=null)]))
2025-07-06 21:58:49 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094018464715038720]:[0] 开始批量同步搜索结果商品数据，数量: 10
2025-07-06 21:58:49 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094018464715038720]:[0] 提取商品ID完成，耗时: 2ms
2025-07-06 21:58:49 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] com.fulfillmen.shop.config.TenantConfig - [1094018464715038720]:[0] 忽略表的多租户处理: pdc_product_mapping
2025-07-06 21:58:49 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094018464715038720]:[0] 查询已存在数据完成，耗时: 25ms，已存在: 10 条
2025-07-06 21:58:49 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.d.c.product.PdcProductSearchConvertMapping - [1094018464715038720]:[0] 使用预设ID: 705814291476485 对应平台商品ID: ************
2025-07-06 21:58:49 DEBUG [ForkJoinPool.commonPool-worker-3] [tid::uId::ip::os::browser:] c.f.s.d.c.product.PdcProductSearchConvertMapping - [1094018464715038721]:[0] 使用预设ID: 706173138501698 对应平台商品ID: ************
2025-07-06 21:58:49 DEBUG [ForkJoinPool.commonPool-worker-6] [tid::uId::ip::os::browser:] c.f.s.d.c.product.PdcProductSearchConvertMapping - [1094018464715038721]:[0] 使用预设ID: 705814291476488 对应平台商品ID: ************
2025-07-06 21:58:49 DEBUG [ForkJoinPool.commonPool-worker-4] [tid::uId::ip::os::browser:] c.f.s.d.c.product.PdcProductSearchConvertMapping - [1094018464715038720]:[0] 使用预设ID: 705814291476490 对应平台商品ID: ************
2025-07-06 21:58:49 DEBUG [ForkJoinPool.commonPool-worker-5] [tid::uId::ip::os::browser:] c.f.s.d.c.product.PdcProductSearchConvertMapping - [1094018464715038720]:[0] 使用预设ID: 705814291476482 对应平台商品ID: ************
2025-07-06 21:58:49 DEBUG [ForkJoinPool.commonPool-worker-2] [tid::uId::ip::os::browser:] c.f.s.d.c.product.PdcProductSearchConvertMapping - [1094018464715038721]:[0] 使用预设ID: 705814291476486 对应平台商品ID: ************
2025-07-06 21:58:49 DEBUG [ForkJoinPool.commonPool-worker-8] [tid::uId::ip::os::browser:] c.f.s.d.c.product.PdcProductSearchConvertMapping - [1094018464715038721]:[0] 使用预设ID: 706943950012441 对应平台商品ID: ************
2025-07-06 21:58:49 DEBUG [ForkJoinPool.commonPool-worker-7] [tid::uId::ip::os::browser:] c.f.s.d.c.product.PdcProductSearchConvertMapping - [1094018464715038721]:[0] 使用预设ID: 710225718345749 对应平台商品ID: ************
2025-07-06 21:58:49 DEBUG [ForkJoinPool.commonPool-worker-9] [tid::uId::ip::os::browser:] c.f.s.d.c.product.PdcProductSearchConvertMapping - [1094018464715038721]:[0] 使用预设ID: 705814291476481 对应平台商品ID: ************
2025-07-06 21:58:49 DEBUG [ForkJoinPool.commonPool-worker-10] [tid::uId::ip::os::browser:] c.f.s.d.c.product.PdcProductSearchConvertMapping - [1094018464715038721]:[0] 使用预设ID: 705814291476489 对应平台商品ID: ************
2025-07-06 21:58:49 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094018464715038720]:[0] 数据处理完成，耗时: 12ms，新增: 0 条，更新: 10 条
2025-07-06 21:58:49 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] com.fulfillmen.shop.config.TenantConfig - [1094018464715038720]:[0] 忽略表的多租户处理: pdc_product_mapping
2025-07-06 21:58:49 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094018464715038720]:[0] 批量更新成功，预期: 10 条，实际: 10 条
2025-07-06 21:58:49 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094018464715038720]:[0] 数据库操作完成，耗时: 75ms，影响行数: 10
2025-07-06 21:58:49 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094018464715038720]:[0] 单批次同步完成，总数: 10, 影响: 10 条，总耗时: 114ms
2025-07-06 21:58:49 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094018464715038720]:[0] 自动同步功能已关闭，跳过事件发布
2025-07-06 21:58:49 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094018464715038720]:[0] 更新多层缓存完成, fullKey: search:65d43bf503e3410204fa882720c56e62, baseKey: search:27eac5f609a7339af532c14c7e27214e:nopag
2025-07-06 21:58:49 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094018464715038720]:[0] 缓存统计 - Key: search:65d43bf503e3410204fa882720c56e62, Hit: false
2025-07-06 21:58:49 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.HomeServiceImpl - [1094018464715038720]:[0] 成功搜索电子产品，数量: 10
2025-07-06 21:58:49 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094018464715038720]:[0] [GET] /api/home/<USER>
2025-07-06 21:58:49 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.fulfillmen.shop.common.context.UserContextHolder - [1094018464715038720]:[0] 清除用户上下文和 MDC 信息
2025-07-06 21:58:49 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094018464715038720]:[0] 清理增强租户上下文
2025-07-06 21:58:49 DEBUG [XNIO-1 task-3] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094018464715038720]:[0] 过滤器清理租户上下文完成
2025-07-06 21:59:12 DEBUG [XNIO-1 task-3] [tid::uId::ip::os::browser:] c.f.shop.manager.service.TenantResolverService - [1094018568154963968]:[0] 从缓存中获取租户ID: 10000
2025-07-06 21:59:12 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094018568154963968]:[0] 租户缓存刷新成功: 10000, 延长时间: 7200秒
2025-07-06 21:59:12 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094018568154963968]:[0] 租户缓存命中: 10000
2025-07-06 21:59:12 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094018568154963968]:[0] 清理增强租户上下文
2025-07-06 21:59:12 DEBUG [XNIO-1 task-3] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094018568154963968]:[0] 过滤器清理租户上下文完成
2025-07-06 21:59:12 ERROR [XNIO-1 task-3] [tid::uId::ip::os::browser:] io.undertow.request - UT005023: Exception handling request to /api/products/***************
java.lang.NoSuchMethodError: 'void com.fulfillmen.shop.common.tenant.EnhancedTenantContext$CacheMetadata.setLastAccessTime(java.time.LocalDateTime)'
	at com.fulfillmen.shop.config.filter.TenantFilter.updateAccessStatistics(TenantFilter.java:220)
	at com.fulfillmen.shop.config.filter.TenantFilter.loadAndCacheEnhancedTenantContext(TenantFilter.java:161)
	at com.fulfillmen.shop.config.filter.TenantFilter.setupTenantContext(TenantFilter.java:126)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:95)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-06 21:59:12 ERROR [XNIO-1 task-3] [tid::uId::ip::os::browser:] c.f.s.c.exception.handler.GlobalExceptionHandler - 未预期异常 - Method: GET, Path: /error
cn.dev33.satoken.exception.SaTokenContextException: SaTokenContext 上下文尚未初始化
	at cn.dev33.satoken.context.SaTokenContextForThreadLocalStaff.getModelBox(SaTokenContextForThreadLocalStaff.java:73)
	at cn.dev33.satoken.context.SaTokenContextForThreadLocal.getModelBox(SaTokenContextForThreadLocal.java:55)
	at cn.dev33.satoken.context.SaTokenContext.getRequest(SaTokenContext.java:66)
	at cn.dev33.satoken.context.SaHolder.getRequest(SaHolder.java:49)
	at cn.dev33.satoken.router.SaRouter.isMatchCurrURI(SaRouter.java:141)
	at cn.dev33.satoken.router.SaRouterStaff.match(SaRouterStaff.java:74)
	at cn.dev33.satoken.router.SaRouter.match(SaRouter.java:172)
	at com.fulfillmen.starter.auth.satoken.autoconfigure.SaTokenAutoConfiguration.lambda$saInterceptor$1(SaTokenAutoConfiguration.java:66)
	at cn.dev33.satoken.interceptor.SaInterceptor.preHandle(SaInterceptor.java:102)
	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:146)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:258)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchToPath(ServletInitialHandler.java:183)
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:415)
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:373)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:315)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-06 21:59:12 DEBUG [XNIO-1 task-3] [tid::uId::ip::os::browser:] c.f.shop.common.resolver.CompositeLocaleResolver - Parsed locale string 'en-US' to: en_US
2025-07-06 21:59:12 DEBUG [XNIO-1 task-3] [tid::uId::ip::os::browser:] c.f.shop.common.resolver.CompositeLocaleResolver - Resolved locale from Accept-Language header: en_US
2025-07-06 21:59:12 DEBUG [XNIO-1 task-3] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.I18nMessageUtils - LocaleContextHolder.getLocale() returned: en_US, system default: en_US
2025-07-06 21:59:12 DEBUG [XNIO-1 task-3] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.I18nMessageUtils - Using DEFAULT_LOCALE: en_US
2025-07-06 21:59:12 DEBUG [XNIO-1 task-3] [tid::uId::ip::os::browser:] c.f.shop.common.exception.BusinessExceptionI18n - 创建国际化异常: errorCode=null, i18nKey=global.internal.system.error, locale=en_US, args=[SaTokenContext 上下文尚未初始化]
2025-07-06 21:59:12 DEBUG [XNIO-1 task-3] [tid::uId::ip::os::browser:] c.f.s.c.exception.handler.GlobalExceptionHandler - Successfully resolved i18n message: key=global.internal.system.error, locale=en_US, message=Internal system error, please contact administrator
2025-07-06 21:59:12 DEBUG [XNIO-1 task-3] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.I18nMessageUtils - Cleared current locale context
2025-07-06 21:59:12 WARN  [XNIO-1 task-3] [tid::uId::ip::os::browser:] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Failure in @ExceptionHandler com.fulfillmen.shop.common.exception.handler.GlobalExceptionHandler#handleGenericException(Exception, HttpServletRequest)
java.lang.NullPointerException: Cannot invoke "java.lang.Integer.toString()" because the return value of "com.fulfillmen.shop.common.exception.BusinessExceptionI18n.getErrorCode()" is null
	at com.fulfillmen.shop.common.exception.handler.GlobalExceptionHandler.handleGenericException(GlobalExceptionHandler.java:653)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:432)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:74)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:175)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1358)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1161)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1106)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:258)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchToPath(ServletInitialHandler.java:183)
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:415)
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:373)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:315)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-06 21:59:12 ERROR [XNIO-1 task-3] [tid::uId::ip::os::browser:] io.undertow.request - UT005022: Exception generating error page /error
jakarta.servlet.ServletException: Request processing failed: cn.dev33.satoken.exception.SaTokenContextException: SaTokenContext 上下文尚未初始化
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1022)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:258)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchToPath(ServletInitialHandler.java:183)
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:415)
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:373)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:315)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: cn.dev33.satoken.exception.SaTokenContextException: SaTokenContext 上下文尚未初始化
	at cn.dev33.satoken.context.SaTokenContextForThreadLocalStaff.getModelBox(SaTokenContextForThreadLocalStaff.java:73)
	at cn.dev33.satoken.context.SaTokenContextForThreadLocal.getModelBox(SaTokenContextForThreadLocal.java:55)
	at cn.dev33.satoken.context.SaTokenContext.getRequest(SaTokenContext.java:66)
	at cn.dev33.satoken.context.SaHolder.getRequest(SaHolder.java:49)
	at cn.dev33.satoken.router.SaRouter.isMatchCurrURI(SaRouter.java:141)
	at cn.dev33.satoken.router.SaRouterStaff.match(SaRouterStaff.java:74)
	at cn.dev33.satoken.router.SaRouter.match(SaRouter.java:172)
	at com.fulfillmen.starter.auth.satoken.autoconfigure.SaTokenAutoConfiguration.lambda$saInterceptor$1(SaTokenAutoConfiguration.java:66)
	at cn.dev33.satoken.interceptor.SaInterceptor.preHandle(SaInterceptor.java:102)
	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:146)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	... 50 common frames omitted
2025-07-06 22:00:00 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-06 21:57:33,366 to 2025-07-06 22:00:00,007
cache                        |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
alibaba:category:list:       |      0.01|  0.00%|             1|             0|             0|             0|      186.0|        186
alibaba:category:list:_local |      0.01|  0.00%|             1|             0|             0|             0|        0.0|          0
alibaba:category:list:_remote|      0.01|  0.00%|             1|             0|             0|             0|        0.0|          0
categories.                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.               |      0.07|  0.00%|            10|             0|             0|             0|        0.0|          0
currency.rate._local         |      0.07|  0.00%|            10|             0|             0|             0|        0.0|          0
currency.rate._remote        |      0.07|  0.00%|            10|             0|             0|             0|        0.0|          0
frontend:product:vo:         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:                 |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:          |      0.02|  0.00%|             3|             0|             0|             0|        0.0|          0
pdc:product:search:_local    |      0.02|  0.00%|             3|             0|             0|             0|        0.0|          0
pdc:product:search:_remote   |      0.02|  0.00%|             3|             0|             0|             0|        0.0|          0
product:detail:              |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
product:detail:_local        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
product:detail:_remote       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
product:dto:                 |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
product:dto:_local           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
product:dto:_remote          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-06 22:00:21 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-06 22:00:21 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-06 22:00:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-06 22:00:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-06 22:00:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:00:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:00:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:00:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:00:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:00:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:00:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:00:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:00:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:00:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:00:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:00:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:00:21 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-06 22:00:21 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-06 22:00:21 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-06 22:00:21 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Revert Remote [MachineState{machineId=7, lastTimeStamp=1751810421423}] instanceId:[InstanceId{instanceId=************:13031, stable=false}] @ namespace:[fulfillmen-shop].
2025-07-06 22:00:21 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-06 22:00:21 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-06 22:00:21 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-06 22:00:21 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-06 22:00:21 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-06 22:00:23 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-06 22:00:23 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-06 22:00:23 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-06 22:00:23 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-06 22:00:46 INFO  [background-preinit] [tid::uId::ip::os::browser:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-06 22:00:46 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Starting BootstrapApplication using Java 21.0.5 with PID 15881 (/Users/<USER>/work/fulfillmen/fulfillmen-shop/fulfillmen-shop-bootstrap/target/classes started by yzsama in /Users/<USER>/work/fulfillmen/fulfillmen-workspace)
2025-07-06 22:00:46 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Running with Spring Boot v3.3.11, Spring v6.1.19
2025-07-06 22:00:46 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - The following 1 profile is active: "local"
2025-07-06 22:00:47 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-06 22:00:47 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-06 22:00:47 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 25 ms. Found 0 Redis repository interfaces.
2025-07-06 22:00:47 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.s.a.dao.SaTokenDaoRedissionConfiguration - [Fulfillmen Starter] - Auto Configuration 'SaToken-Dao-Redis' completed initialization.
2025-07-06 22:00:48 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-06 22:00:48 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-06 22:00:48 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.d.m.a.MybatisPlusAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'MyBatis Plus' completed initialization.
2025-07-06 22:00:48 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - 多租户拦截器已配置，忽略表: [tenant_commission_config, tenants, regions, tenant_plan_relation, sys_alibaba_category, subregions, sys_users, tenant_domains, sys_option, openapi_account, pdc_product_mapping, tenant_plans, tenants_info, tenant_files, sys_config, openapi_account_permission, openapi_interface, tenant_locales]
2025-07-06 22:00:48 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.d.m.a.i.MyBatisPlusIdGeneratorConfiguration - [Fulfillmen Starter] - Auto Configuration 'MyBatis Plus-IdGenerator-CosId' completed initialization.
2025-07-06 22:00:48 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.FulfillmenWebMvcConfig - Default locale initialized to: en_US
2025-07-06 22:00:48 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.FulfillmenWebMvcConfig - MessageSource configured with basenames: [i18n/messages, i18n/openapi-message]
2025-07-06 22:00:48 WARN  [main] [tid::uId::ip::os::browser:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-06 22:00:48 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-06 22:00:48 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2194 ms
2025-07-06 22:00:48 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - === 过滤器配置信息 ===
2025-07-06 22:00:48 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 租户过滤器: enabled=true, order=-2147483638
2025-07-06 22:00:48 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - MDC 过滤器: enabled=true, order=-2147483628
2025-07-06 22:00:48 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 过滤器优先级说明:
过滤器执行顺序（数值越小优先级越高）：

1. TRACE_FILTER     (-2147483648) - 链路跟踪过滤器，生成 TraceId
2. TENANT_FILTER    (-2147483638) - 租户过滤器，设置租户上下文
3. MDC_FILTER       (-2147483628) - MDC 过滤器，设置日志上下文
4. XSS_FILTER       (-2147483548) - XSS 过滤器，安全防护
5. CORS_FILTER      (-2147483538) - CORS 过滤器，跨域处理
6. LOG_FILTER       (2147483637) - 日志过滤器，记录请求响应

注意：
- 链路跟踪过滤器优先级最高，确保 TraceId 在整个请求生命周期中可用
- 租户过滤器在链路跟踪之后，为后续过滤器提供租户上下文
- MDC 过滤器在租户过滤器之后，可以获取到租户信息并设置到日志上下文
- 安全相关过滤器（XSS、CORS）在业务过滤器之前执行
- 日志过滤器优先级最低，记录完整的请求响应信息

2025-07-06 22:00:48 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - ===================
2025-07-06 22:00:48 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 租户过滤器已注册 [enabled=true, order=-2147483638, urlPatterns=[/*], excludePatterns=20]
2025-07-06 22:00:48 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - MDC 过滤器已注册 [enabled=true, order=-2147483628, urlPatterns=[/*], features=IP标准化,浏览器信息,操作系统信息]
2025-07-06 22:00:48 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.trace.TraceAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web-Trace' completed initialization.
2025-07-06 22:00:49 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.l.i.autoconfigure.LogAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Log-interceptor' completed initialization.
2025-07-06 22:00:49 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?nayasource\.com
2025-07-06 22:00:49 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?nayasource\.com
2025-07-06 22:00:49 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http://localhost:[0-9]+
2025-07-06 22:00:49 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http://localhost:[0-9]+
2025-07-06 22:00:49 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?aliyuncs\.com
2025-07-06 22:00:49 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?aliyuncs\.com
2025-07-06 22:00:49 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?sealoshzh\.site
2025-07-06 22:00:49 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?sealoshzh\.site
2025-07-06 22:00:49 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 检测到 allowCredentials=true 且使用通配符配置，这可能导致CORS错误
2025-07-06 22:00:49 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 建议：1) 设置 allowCredentials=false，或 2) 使用具体的域名列表替换通配符
2025-07-06 22:00:49 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 当前策略：保持 allowCredentials=true，但建议检查配置
2025-07-06 22:00:49 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 跨域配置初始化完成 [常规域名: 0, 正则域名: 4, 允许凭证: true, 缓存时间: 3600s]
2025-07-06 22:00:49 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.w.a.cors.CorsConfigurationValidator - 
🔍 CORS配置分析报告:
==================================================
 1. ⚠️  安全建议: 生产环境中allowCredentials=true时建议明确指定允许的HTTP方法
 2. ⚠️  安全建议: 生产环境中allowCredentials=true时建议明确指定允许的请求头
 3. 🔒 安全建议: 生产环境建议明确指定允许的HTTP方法，避免使用 '*'
 4. ✅ 最佳实践: 正在使用正则表达式匹配，这是推荐的域名配置方式
==================================================
📖 更多信息请参考: fulfillmen-starter-web/CORS-CONFIG-EXAMPLE.md

2025-07-06 22:00:49 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-07-06 22:00:49 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-07-06 22:00:49 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.r.autoconfigure.RedissonAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Redisson' completed initialization.
2025-07-06 22:00:49 INFO  [main] [tid::uId::ip::os::browser:] org.redisson.Version - Redisson 3.45.1
2025-07-06 22:00:49 INFO  [redisson-netty-1-4] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-06 22:00:49 INFO  [redisson-netty-1-19] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-06 22:00:49 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-06 22:00:49 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 货币汇率缓存初始化完成
2025-07-06 22:00:49 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: fixed_window
2025-07-06 22:00:49 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: sliding_window
2025-07-06 22:00:49 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: token_bucket
2025-07-06 22:00:49 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: leaky_bucket
2025-07-06 22:00:49 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已启用
2025-07-06 22:00:49 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.g.a.GraphicCaptchaAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Captcha-Graphic' completed initialization.
2025-07-06 22:00:50 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - 🎯 自动同步功能初始化: 禁用
2025-07-06 22:00:50 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.manager.strategy.SyncStrategyFactory - 同步策略工厂初始化完成，可用策略: [AUTO, DISABLED, MANUAL]
2025-07-06 22:00:50 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.sync.chain.config.ProductSyncChainConfig - 产品同步责任链配置初始化完成
2025-07-06 22:00:50 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.sync.chain.handlers.CacheUpdateHandler - 产品同步缓存初始化完成
2025-07-06 22:00:50 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl - 缓存配置详情: ProductDetailVO[本地:8min,远程:25min,容量:300]
2025-07-06 22:00:50 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl - 🎯 ProductService优化缓存配置完成:
2025-07-06 22:00:50 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── ProductDetailVO缓存: 本地8min/远程25min, 容量300, 键前缀: frontend:product:vo:
2025-07-06 22:00:50 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── 双层缓存策略: 本地内存 + Redis远程
2025-07-06 22:00:50 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── 空值缓存: 已启用，防止缓存穿透
2025-07-06 22:00:50 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   └── 同步机制: 已启用，保证多实例一致性
2025-07-06 22:00:50 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Initialized CompositeLocaleResolver with default locale: en_US and supported locales: [en_US, zh_CN, zh_TW]
2025-07-06 22:00:50 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Initialized LocaleChangeInterceptor with param name: lang
2025-07-06 22:00:50 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.autoconfigure.SpringDocAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'ApiDoc' completed initialization.
2025-07-06 22:00:50 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.s.autoconfigure.SaTokenAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'SaToken' completed initialization.
2025-07-06 22:00:50 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.mvc.WebMvcAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web MVC' completed initialization.
2025-07-06 22:00:50 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-06 22:00:50 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已注册: order=100, patterns=/**, rate=100/60s
2025-07-06 22:00:50 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Added LocaleChangeInterceptor to registry
2025-07-06 22:00:50 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.a.response.GlobalResponseAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web-Global Response' completed initialization.
2025-07-06 22:00:50 INFO  [main] [tid::uId::ip::os::browser:] c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@2dddc1b9
2025-07-06 22:00:50 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.j.autoconfigure.JetCacheAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'JetCache' completed initialization.
2025-07-06 22:00:50 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote instanceId:[InstanceId{instanceId=************:15881, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-06 22:00:50 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote machineState:[MachineState{machineId=7, lastTimeStamp=1751810450800}] - instanceId:[InstanceId{instanceId=************:15881, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-06 22:00:50 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no].
2025-07-06 22:00:50 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-06 22:00:50 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-06 22:00:50 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.order_no] jobSize:[0].
2025-07-06 22:00:50 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no].
2025-07-06 22:00:50 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2].
2025-07-06 22:00:50 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2] start.
2025-07-06 22:00:50 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.user_no] jobSize:[0].
2025-07-06 22:00:51 DEBUG [main] [tid::uId::ip::os::browser:] c.f.starter.log.interceptor.handler.LogFilter - Filter 'logFilter' configured for use
2025-07-06 22:00:51 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-06 22:00:51 INFO  [main] [tid::uId::ip::os::browser:] org.xnio - XNIO version 3.8.16.Final
2025-07-06 22:00:51 INFO  [main] [tid::uId::ip::os::browser:] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-06 22:00:51 INFO  [main] [tid::uId::ip::os::browser:] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-06 22:00:51 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-07-06 22:00:51 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-06 22:00:51 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 70 ms
2025-07-06 22:00:51 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 13 endpoints beneath base path '/actuator'
2025-07-06 22:00:51 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已注册: order=100, patterns=/**, rate=100/60s
2025-07-06 22:00:51 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Added LocaleChangeInterceptor to registry
2025-07-06 22:00:51 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-06 22:00:51 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8099 (http) with context path '/'
2025-07-06 22:00:51 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Started BootstrapApplication in 5.496 seconds (process running for 6.339)
2025-07-06 22:00:51 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - ----------------------------------------------
2025-07-06 22:00:51 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Fulfillmen Shop service started successfully.
2025-07-06 22:00:51 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - API地址：http://127.0.0.1:8080
2025-07-06 22:00:51 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - API文档：http://127.0.0.1:8080/doc.html
2025-07-06 22:00:51 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - ----------------------------------------------
2025-07-06 22:00:51 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 开始初始化汇率缓存...
2025-07-06 22:00:51 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 开始定时刷新汇率缓存...
2025-07-06 22:00:51 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> USD
2025-07-06 22:00:51 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-USD = 0.1396
2025-07-06 22:00:51 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> USD
2025-07-06 22:00:51 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-USD = 0.1396
2025-07-06 22:00:52 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> EUR
2025-07-06 22:00:52 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-EUR = 0.1185
2025-07-06 22:00:52 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> EUR
2025-07-06 22:00:52 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-EUR = 0.1185
2025-07-06 22:00:52 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> JPY
2025-07-06 22:00:52 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-JPY = 20.1073
2025-07-06 22:00:52 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> JPY
2025-07-06 22:00:52 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-JPY = 20.1073
2025-07-06 22:00:52 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> KRW
2025-07-06 22:00:52 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> KRW
2025-07-06 22:00:52 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-KRW = 190.1975
2025-07-06 22:00:52 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-KRW = 190.1975
2025-07-06 22:00:52 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> INR
2025-07-06 22:00:52 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> INR
2025-07-06 22:00:52 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-INR = 11.9314
2025-07-06 22:00:52 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-INR = 11.9314
2025-07-06 22:00:52 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.domain.util.CurrencyConversionUtils - 批量更新汇率缓存，共 5 条记录
2025-07-06 22:00:52 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.domain.util.CurrencyConversionUtils - 批量更新汇率缓存，共 5 条记录
2025-07-06 22:00:52 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - CNY基础汇率缓存更新完成: 成功 5/5 条记录
2025-07-06 22:00:52 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 汇率缓存初始化完成，缓存条目数: 5
2025-07-06 22:00:52 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - CNY基础汇率缓存更新完成: 成功 5/5 条记录
2025-07-06 22:00:52 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 定时刷新汇率缓存完成，缓存条目数: 5
2025-07-06 22:00:53 INFO  [RMI TCP Connection(5)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-06 22:00:53 INFO  [RMI TCP Connection(4)-127.0.0.1] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-06 22:00:53 INFO  [RMI TCP Connection(4)-127.0.0.1] [tid::uId::ip::os::browser:] org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-06 22:00:53 INFO  [RMI TCP Connection(4)-127.0.0.1] [tid::uId::ip::os::browser:] org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-06 22:00:53 INFO  [RMI TCP Connection(5)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@36121d3c
2025-07-06 22:00:53 INFO  [RMI TCP Connection(5)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-06 22:01:25 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] c.f.shop.manager.service.TenantResolverService - [1094019126391660544]:[0] 从缓存中获取租户ID: 10000
2025-07-06 22:01:25 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094019126391660544]:[0] 租户缓存刷新成功: 10000, 延长时间: 7200秒
2025-07-06 22:01:25 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094019126391660544]:[0] 租户缓存命中: 10000
2025-07-06 22:01:25 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094019126391660544]:[0] 缓存命中: 租户ID=10000, 访问次数=3
2025-07-06 22:01:25 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094019126391660544]:[0] 设置增强租户上下文: tenantId=10000, tenantName=Fulfillmen
2025-07-06 22:01:25 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094019126391660544]:[0] 过滤器设置当前请求的租户ID: 10000 (URI: /api/products/***************)
2025-07-06 22:01:25 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094019126391660544]:[0] [RegexCors] 正则规则 http://localhost:[0-9]+ 匹配来源: http://localhost:5173
2025-07-06 22:01:25 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094019126391660544]:[0] [RegexCors] 正则匹配成功: http://localhost:5173
2025-07-06 22:01:25 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.config.satoken.SaExtensionInterceptor - [1094019126391660544]:[0] auth check result: true
2025-07-06 22:01:25 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094019126391660544]:[0] [GET] /api/products/***************
2025-07-06 22:01:25 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1094019126391660544]:[0] 全局限流检查通过: key=rate_limit:ip:0:0:0:0:0:0:0:1, current=1/100
2025-07-06 22:01:25 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.controller.ProductController - [1094019126391660544]:[0] 获取商品详情: productId=***************
2025-07-06 22:01:25 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.ProductServiceImpl - [1094019126391660544]:[0] 获取商品详情VO: 传入ID=***************
2025-07-06 22:01:25 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.ProductServiceImpl - [1094019126391660544]:[0] ID未找到对应SPU记录，将其视为platformProductId: ***************
2025-07-06 22:01:25 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.ProductServiceImpl - [1094019126391660544]:[0] 缓存未命中，通过ProductSyncService获取: platformProductId=***************
2025-07-06 22:01:25 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1094019126391660544]:[0] 获取或同步产品数据: platformProductId=***************
2025-07-06 22:01:25 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1094019126391660544]:[0] 获取或同步产品数据: platformProductId=***************
2025-07-06 22:01:25 DEBUG [ForkJoinPool.commonPool-worker-1] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - 忽略表的多租户处理: pdc_product_mapping
2025-07-06 22:01:25 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1094019126391660544]:[0] TzProductSpu不存在，自动同步: platformProductId=***************
2025-07-06 22:01:25 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1094019126391660544]:[0] 开始同步产品数据，platformProductId: ***************
2025-07-06 22:01:25 DEBUG [ForkJoinPool.commonPool-worker-1] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - 数据库不存在，从API获取商品详情, id: ***************
2025-07-06 22:01:25 DEBUG [ForkJoinPool.commonPool-worker-1] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - 从API获取商品详情, offerId: *************** , platformProductId: ************
2025-07-06 22:01:25 DEBUG [ForkJoinPool.commonPool-worker-2] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - 忽略表的多租户处理: pdc_product_mapping
2025-07-06 22:01:25 DEBUG [ForkJoinPool.commonPool-worker-2] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - 数据库不存在，从API获取商品详情, id: ***************
2025-07-06 22:01:25 DEBUG [ForkJoinPool.commonPool-worker-2] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - 从API获取商品详情, offerId: *************** , platformProductId: ************
2025-07-06 22:01:25 INFO  [ForkJoinPool.commonPool-worker-1] [tid::uId::ip::os::browser:] c.fulfillmen.support.alibaba.sign.AlibabaSignature - 签名因子: param2/1/com.alibaba.fenxiao.crossborder/product.search.queryProductDetail/8390330_aop_timestamp1751810485905access_tokenb49dfbf3-c3d7-4dfa-9b50-2d3300bad6c2offerDetailParam{"offerId":************,"country":"en","outMemberId":null} 签名: 4F9A5BECF6F692C9AB215C9C8462C512A2E1D205
2025-07-06 22:01:25 INFO  [ForkJoinPool.commonPool-worker-2] [tid::uId::ip::os::browser:] c.fulfillmen.support.alibaba.sign.AlibabaSignature - 签名因子: param2/1/com.alibaba.fenxiao.crossborder/product.search.queryProductDetail/8390330_aop_timestamp1751810485911access_tokenb49dfbf3-c3d7-4dfa-9b50-2d3300bad6c2offerDetailParam{"offerId":************,"country":"en","outMemberId":null} 签名: 768386E1F9CE2EBBE7CA17605721B33830EBCE6C
2025-07-06 22:01:26 DEBUG [reactor-http-nio-14] [tid::uId::ip::os::browser:] c.f.s.manager.support.alibaba.impl.ProductManager - result: GoodsDetailResponse.Result(success=true, code=200, message=null, result=GoodsDetailResponse.ProductDetail(offerId=************, categoryId=*********, categoryName=null, subject=emoji-lab系列猫与沙发/春日限定粉薄底赛车鞋休闲平底德训鞋女, subjectTrans=emoji-lab Series Cat and Sofa/Spring Limited Powder Thin-soled Racing Shoes Casual Flat German Training Shoes for Women, description=<div id="offer-template-0"></div><div style="width: 790.0px;"><img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01Q0gwGo2Dj0dmuPOs8_!!*************-0-cib.jpg" usemap="#_sdmap_0"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01DPGMyG2Dj0dkxbzy6_!!*************-0-cib.jpg" usemap="#_sdmap_1"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01ZF0NAE2Dj0doaBWPb_!!*************-0-cib.jpg" usemap="#_sdmap_2"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01kKEYAO2Dj0dmVa0RZ_!!*************-0-cib.jpg" usemap="#_sdmap_3"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01QZ3L3C2Dj0dmkJqW3_!!*************-0-cib.jpg" usemap="#_sdmap_4"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01Kc4W102Dj0dmVakB4_!!*************-0-cib.jpg" usemap="#_sdmap_5"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01Wf3XJs2Dj0dnQ2JaS_!!*************-0-cib.jpg" usemap="#_sdmap_6"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01PhGfmz2Dj0dkxZaIh_!!*************-0-cib.jpg" usemap="#_sdmap_7"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01vOOp7u2Dj0dnHJdWz_!!*************-0-cib.jpg" usemap="#_sdmap_8"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01AX1xXX2Dj0dnHHxZO_!!*************-0-cib.jpg" usemap="#_sdmap_9"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN011yqhIz2Dj0dnToF2w_!!*************-0-cib.jpg" usemap="#_sdmap_10"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01oLz4DZ2Dj0dnQ1RUR_!!*************-0-cib.jpg" usemap="#_sdmap_11"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01NQ4i2b2Dj0doaBzXg_!!*************-0-cib.jpg" usemap="#_sdmap_12"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01jKw9JX2Dj0doaCGCt_!!*************-0-cib.jpg" usemap="#_sdmap_13"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN018C7NMB2Dj0dnTpNio_!!*************-0-cib.jpg" usemap="#_sdmap_14"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01wQEB1Z2Dj0doaEPEr_!!*************-0-cib.jpg" usemap="#_sdmap_15"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN017JWhzQ2Dj0dmuPKjz_!!*************-0-cib.jpg" usemap="#_sdmap_16"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01mKY4Xb2Dj0do5Gpfs_!!*************-0-cib.jpg" usemap="#_sdmap_17"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01CmJPye2Dj0dgekC3L_!!*************-0-cib.jpg" usemap="#_sdmap_18"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01Q9WaQD2Dj0dngM3cC_!!*************-0-cib.jpg" usemap="#_sdmap_19"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01Ohy4bn2Dj0dlVddNw_!!*************-0-cib.jpg" usemap="#_sdmap_20"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN013q8m6U2Dj0dngNCJ6_!!*************-0-cib.jpg" usemap="#_sdmap_21"/></div><div id="offer-template-*************"></div><p>&nbsp;&nbsp;</p>, mainVideo=https://cloud.video.taobao.com/play/u/*************/p/1/e/6/t/1/************.mp4, detailVideo=null, productImage=GoodsDetailResponse.ProductImage(images=[https://cbu01.alicdn.com/img/ibank/O1CN01YySNns2Dj0dmVMAwk_!!*************-0-cib.jpg, https://cbu01.alicdn.com/img/ibank/O1CN01xDlFm52Dj0dng7QH9_!!*************-0-cib.jpg, https://cbu01.alicdn.com/img/ibank/O1CN01Mlate22Dj0dlVOnTk_!!*************-0-cib.jpg, https://cbu01.alicdn.com/img/ibank/O1CN01Wb1KFK2Dj0dmVLdgT_!!*************-0-cib.jpg, https://cbu01.alicdn.com/img/ibank/O1CN01xmxk2y2Dj0eq2RsEO_!!*************-0-cib.jpg], whiteImage=null), productAttribute=[GoodsDetailResponse.ProductAttribute(attributeId=364, attributeName=产品类别, value=老爹鞋, attributeNameTrans=Product category, valueTrans=Dad shoes), GoodsDetailResponse.ProductAttribute(attributeId=346, attributeName=产地, value=浙江台州, attributeNameTrans=Origin, valueTrans=Taizhou, zhejiang), GoodsDetailResponse.ProductAttribute(attributeId=973, attributeName=风格, value=休闲, attributeNameTrans=Style, valueTrans=Leisure), GoodsDetailResponse.ProductAttribute(attributeId=2531, attributeName=适合季节, value=夏季, attributeNameTrans=Suitable for the season, valueTrans=Summer), GoodsDetailResponse.ProductAttribute(attributeId=2531, attributeName=适合季节, value=春季, attributeNameTrans=Suitable for the season, valueTrans=Spring), GoodsDetailResponse.ProductAttribute(attributeId=2531, attributeName=适合季节, value=秋季, attributeNameTrans=Suitable for the season, valueTrans=Autumn), GoodsDetailResponse.ProductAttribute(attributeId=2600, attributeName=适用性别, value=女, attributeNameTrans=Applicable gender, valueTrans=Female), GoodsDetailResponse.ProductAttribute(attributeId=4921, attributeName=鞋面材质, value=人造PU, attributeNameTrans=Upper material, valueTrans=Artificial pu), GoodsDetailResponse.ProductAttribute(attributeId=8614, attributeName=流行元素, value=翻边, attributeNameTrans=Popular elements, valueTrans=Flanging), GoodsDetailResponse.ProductAttribute(attributeId=8614, attributeName=流行元素, value=镂空, attributeNameTrans=Popular elements, valueTrans=Hollow out), GoodsDetailResponse.ProductAttribute(attributeId=8614, attributeName=流行元素, value=蝴蝶结, attributeNameTrans=Popular elements, valueTrans=Bow), GoodsDetailResponse.ProductAttribute(attributeId=8614, attributeName=流行元素, value=车缝线, attributeNameTrans=Popular elements, valueTrans=Sewing thread), GoodsDetailResponse.ProductAttribute(attributeId=8614, attributeName=流行元素, value=松糕跟, attributeNameTrans=Popular elements, valueTrans=Platform heels), GoodsDetailResponse.ProductAttribute(attributeId=8614, attributeName=流行元素, value=皮革拼接, attributeNameTrans=Popular elements, valueTrans=Leather stitching), GoodsDetailResponse.ProductAttribute(attributeId=100000691, attributeName=货源类别, value=现货, attributeNameTrans=Supply category, valueTrans=Spot goods), GoodsDetailResponse.ProductAttribute(attributeId=100017842, attributeName=最快出货时间, value=1, attributeNameTrans=Fastest shipping time, valueTrans=1), GoodsDetailResponse.ProductAttribute(attributeId=100017996, attributeName=鞋头形状, value=圆头, attributeNameTrans=Toe shape, valueTrans=Round head), GoodsDetailResponse.ProductAttribute(attributeId=100017998, attributeName=鞋跟高度, value=中跟（3-5CM）, attributeNameTrans=Heel height, valueTrans=Medium heel (3-5cm)), GoodsDetailResponse.ProductAttribute(attributeId=3216, attributeName=颜色, value=苹果绿, attributeNameTrans=Color, valueTrans=Apple green), GoodsDetailResponse.ProductAttribute(attributeId=3216, attributeName=颜色, value=蝴蝶粉, attributeNameTrans=Color, valueTrans=Butterfly powder), GoodsDetailResponse.ProductAttribute(attributeId=3216, attributeName=颜色, value=苹果红, attributeNameTrans=Color, valueTrans=Apple red), GoodsDetailResponse.ProductAttribute(attributeId=450, attributeName=尺码, value=35, attributeNameTrans=Size, valueTrans=35), GoodsDetailResponse.ProductAttribute(attributeId=450, attributeName=尺码, value=36, attributeNameTrans=Size, valueTrans=36), GoodsDetailResponse.ProductAttribute(attributeId=450, attributeName=尺码, value=37, attributeNameTrans=Size, valueTrans=37), GoodsDetailResponse.ProductAttribute(attributeId=450, attributeName=尺码, value=38, attributeNameTrans=Size, valueTrans=38), GoodsDetailResponse.ProductAttribute(attributeId=450, attributeName=尺码, value=39, attributeNameTrans=Size, valueTrans=39), GoodsDetailResponse.ProductAttribute(attributeId=450, attributeName=尺码, value=40, attributeNameTrans=Size, valueTrans=40), GoodsDetailResponse.ProductAttribute(attributeId=1141, attributeName=功能, value=增高, attributeNameTrans=Function, valueTrans=Increase), GoodsDetailResponse.ProductAttribute(attributeId=1141, attributeName=功能, value=透气, attributeNameTrans=Function, valueTrans=Breathable), GoodsDetailResponse.ProductAttribute(attributeId=1141, attributeName=功能, value=平衡, attributeNameTrans=Function, valueTrans=Balance), GoodsDetailResponse.ProductAttribute(attributeId=1141, attributeName=功能, value=轻便, attributeNameTrans=Function, valueTrans=Light), GoodsDetailResponse.ProductAttribute(attributeId=2176, attributeName=品牌, value=无品牌, attributeNameTrans=Brand, valueTrans=Unbranded), GoodsDetailResponse.ProductAttribute(attributeId=2900, attributeName=图案, value=纯色, attributeNameTrans=Pattern, valueTrans=Solid color), GoodsDetailResponse.ProductAttribute(attributeId=4923, attributeName=鞋底材质, value=橡胶, attributeNameTrans=Sole material, valueTrans=Rubber), GoodsDetailResponse.ProductAttribute(attributeId=7021, attributeName=适用运动, value=通用, attributeNameTrans=Applicable sports, valueTrans=General), GoodsDetailResponse.ProductAttribute(attributeId=7108, attributeName=是否库存, value=否, attributeNameTrans=Is it in stock?, valueTrans=No), GoodsDetailResponse.ProductAttribute(attributeId=8243, attributeName=库存类型, value=整单, attributeNameTrans=Inventory type, valueTrans=Whole order), GoodsDetailResponse.ProductAttribute(attributeId=8508, attributeName=穿着方式, value=前系带, attributeNameTrans=Way of dressing, valueTrans=Front tie), GoodsDetailResponse.ProductAttribute(attributeId=8514, attributeName=是否外贸, value=否, attributeNameTrans=Whether to export, valueTrans=No), GoodsDetailResponse.ProductAttribute(attributeId=1626558, attributeName=鞋跟形状, value=平跟, attributeNameTrans=Heel shape, valueTrans=Flat), GoodsDetailResponse.ProductAttribute(attributeId=2231460, attributeName=内里材质, value=超纤, attributeNameTrans=Inner material, valueTrans=Super fiber), GoodsDetailResponse.ProductAttribute(attributeId=100017944, attributeName=开口深度, value=中口（7-11CM）, attributeNameTrans=Opening depth, valueTrans=Middle mouth (7-11cm)), GoodsDetailResponse.ProductAttribute(attributeId=100018421, attributeName=鞋底工艺, value=粘胶鞋, attributeNameTrans=Sole craftsmanship, valueTrans=Viscose shoes), GoodsDetailResponse.ProductAttribute(attributeId=100110361, attributeName=鞋垫材质, value=EVA, attributeNameTrans=Insole material, valueTrans=EVA), GoodsDetailResponse.ProductAttribute(attributeId=101271579, attributeName=质检报告, value=否, attributeNameTrans=Quality inspection report, valueTrans=No), GoodsDetailResponse.ProductAttribute(attributeId=140102779, attributeName=质检单位, value=sgs, attributeNameTrans=Quality inspection unit, valueTrans=sgs), GoodsDetailResponse.ProductAttribute(attributeId=1811, attributeName=款式, value=单鞋, attributeNameTrans=Style, valueTrans=Single shoes), GoodsDetailResponse.ProductAttribute(attributeId=10363196, attributeName=适用场景, value=日常, attributeNameTrans=Applicable scenarios, valueTrans=Daily), GoodsDetailResponse.ProductAttribute(attributeId=1957, attributeName=毛重, value=默认, attributeNameTrans=Gross weight, valueTrans=Default), GoodsDetailResponse.ProductAttribute(attributeId=100041414, attributeName=包装体积, value=默认, attributeNameTrans=Packing volume, valueTrans=Default), GoodsDetailResponse.ProductAttribute(attributeId=160540053, attributeName=上市年份季节（上市时间）, value=2025年春季, attributeNameTrans=Year and season of launch (time of launch), valueTrans=Spring 2025), GoodsDetailResponse.ProductAttribute(attributeId=171404504, attributeName=最晚发货时间, value=3天, attributeNameTrans=Latest delivery time, valueTrans=3 days), GoodsDetailResponse.ProductAttribute(attributeId=182282223, attributeName=是否跨境出口专供货源, value=否, attributeNameTrans=Whether to exclusively supply goods for cross-border export, valueTrans=No), GoodsDetailResponse.ProductAttribute(attributeId=20490, attributeName=闭合方式, value=系带, attributeNameTrans=Closing method, valueTrans=Lace up), GoodsDetailResponse.ProductAttribute(attributeId=7170, attributeName=适用年龄段, value=成年, attributeNameTrans=Applicable age group, valueTrans=Adult)], productSkuInfos=[GoodsDetailResponse.ProductSkuInfo(amountOnSale=1000, price=null, jxhyPrice=null, skuId=*************, specId=34e26a498df930e77ae7c6c5399cdcb4, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=苹果绿, valueTrans=Apple green, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01UI8Aot2Dj0dldD6o6_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=35, valueTrans=35, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=998, price=null, jxhyPrice=null, skuId=*************, specId=1bf10b0b0b7cd9f66cd76a1521687208, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=苹果绿, valueTrans=Apple green, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01UI8Aot2Dj0dldD6o6_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=36, valueTrans=36, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=1000, price=null, jxhyPrice=null, skuId=*************, specId=1925e981490d0d39626ea5553b413b27, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=苹果绿, valueTrans=Apple green, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01UI8Aot2Dj0dldD6o6_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=37, valueTrans=37, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=1000, price=null, jxhyPrice=null, skuId=*************, specId=ac303599e7005fb651337f47f7af39f4, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=苹果绿, valueTrans=Apple green, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01UI8Aot2Dj0dldD6o6_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=38, valueTrans=38, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=999, price=null, jxhyPrice=null, skuId=*************, specId=a29f39727d547f464974084658ec4722, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=苹果绿, valueTrans=Apple green, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01UI8Aot2Dj0dldD6o6_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=39, valueTrans=39, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=1000, price=null, jxhyPrice=null, skuId=*************, specId=c54d7885c6062376641412f9ecdd388e, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=苹果绿, valueTrans=Apple green, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01UI8Aot2Dj0dldD6o6_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=40, valueTrans=40, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=1000, price=null, jxhyPrice=null, skuId=*************, specId=eba49acb79261e9e8179577f511c8bf5, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=蝴蝶粉, valueTrans=Butterfly powder, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01QRF4GD2Dj0do5EkRK_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=35, valueTrans=35, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=999, price=null, jxhyPrice=null, skuId=*************, specId=4aae85b73690227c0272892177c1f87d, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=蝴蝶粉, valueTrans=Butterfly powder, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01QRF4GD2Dj0do5EkRK_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=36, valueTrans=36, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=1000, price=null, jxhyPrice=null, skuId=*************, specId=22073a3fccc70c2511392e4547040f42, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=蝴蝶粉, valueTrans=Butterfly powder, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01QRF4GD2Dj0do5EkRK_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=37, valueTrans=37, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=1000, price=null, jxhyPrice=null, skuId=*************, specId=312e28fd5a8d2192cd6cc3953bbc61a3, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=蝴蝶粉, valueTrans=Butterfly powder, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01QRF4GD2Dj0do5EkRK_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=38, valueTrans=38, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=1000, price=null, jxhyPrice=null, skuId=*************, specId=671856155b822d9539914d2e69712d57, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=蝴蝶粉, valueTrans=Butterfly powder, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01QRF4GD2Dj0do5EkRK_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=39, valueTrans=39, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=1000, price=null, jxhyPrice=null, skuId=*************, specId=d4d0ef81960bf3a881022c49b93905ad, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=蝴蝶粉, valueTrans=Butterfly powder, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01QRF4GD2Dj0do5EkRK_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=40, valueTrans=40, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=1000, price=null, jxhyPrice=null, skuId=*************, specId=443788b7289646b81c54c7b8c9b3502d, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=苹果红, valueTrans=Apple red, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01N5JWe52Dj0dngJluy_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=35, valueTrans=35, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=1000, price=null, jxhyPrice=null, skuId=*************, specId=7f1f235dc728cd8a4eb14012504dabf9, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=苹果红, valueTrans=Apple red, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01N5JWe52Dj0dngJluy_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=36, valueTrans=36, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=999, price=null, jxhyPrice=null, skuId=*************, specId=dbf9cd9e90dea825f1c3cb27ebacb582, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=苹果红, valueTrans=Apple red, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01N5JWe52Dj0dngJluy_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=37, valueTrans=37, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=1000, price=null, jxhyPrice=null, skuId=*************, specId=44a3869259e14b2c91311a9103606f3d, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=苹果红, valueTrans=Apple red, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01N5JWe52Dj0dngJluy_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=38, valueTrans=38, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=1000, price=null, jxhyPrice=null, skuId=*************, specId=cd579e6cc5e526b2acdb1ebfae1643bb, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=苹果红, valueTrans=Apple red, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01N5JWe52Dj0dngJluy_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=39, valueTrans=39, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=1000, price=null, jxhyPrice=null, skuId=*************, specId=7dfe4aa75292b97ce35969ea81d491eb, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=苹果红, valueTrans=Apple red, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01N5JWe52Dj0dngJluy_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=40, valueTrans=40, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58))], productSaleInfo=GoodsDetailResponse.ProductSaleInfo(amountOnSale=17995, priceRangeList=[GoodsDetailResponse.PriceRange(startQuantity=1, price=58.0, promotionPrice=null), GoodsDetailResponse.PriceRange(startQuantity=50, price=57.0, promotionPrice=null), GoodsDetailResponse.PriceRange(startQuantity=100, price=56.0, promotionPrice=null)], quoteType=2, unitInfo=GoodsDetailResponse.UnitInfo(unit=双, transUnit=Double), fenxiaoSaleInfo=GoodsDetailResponse.FenxiaoSaleInfo(onePieceFreePostage=false, startQuantity=1, onePiecePrice=null, offerPrice=null), consignPrice=null, jxhyPrice=null), productShippingInfo=GoodsDetailResponse.ProductShippingInfo(sendGoodsAddressText=浙江省台州市, weight=null, width=null, height=null, length=null, shippingTimeGuarantee=null, skuShippingInfoList=[GoodsDetailResponse.SkuShippingInfo(specId=34e26a498df930e77ae7c6c5399cdcb4, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=1bf10b0b0b7cd9f66cd76a1521687208, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=1925e981490d0d39626ea5553b413b27, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=ac303599e7005fb651337f47f7af39f4, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=a29f39727d547f464974084658ec4722, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=c54d7885c6062376641412f9ecdd388e, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=eba49acb79261e9e8179577f511c8bf5, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=4aae85b73690227c0272892177c1f87d, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=22073a3fccc70c2511392e4547040f42, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=312e28fd5a8d2192cd6cc3953bbc61a3, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=671856155b822d9539914d2e69712d57, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=d4d0ef81960bf3a881022c49b93905ad, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=443788b7289646b81c54c7b8c9b3502d, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=7f1f235dc728cd8a4eb14012504dabf9, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=dbf9cd9e90dea825f1c3cb27ebacb582, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=44a3869259e14b2c91311a9103606f3d, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=cd579e6cc5e526b2acdb1ebfae1643bb, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=7dfe4aa75292b97ce35969ea81d491eb, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980)], skuShippingDetails=[GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.746, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.754, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.731, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.704, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.785, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.789, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.769, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.721, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.698, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.751, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.728, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.733, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.727, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.772, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.759, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.763, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.748, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.759, aiWeightAccuracy=78%, pkgSizeSource=商家自填)], pkgSizeSource=null), isJxhy=false, sellerOpenId=BBBYBpUti9qFcgbDqB-D60ghw, minOrderQuantity=1, batchNumber=null, status=published, tagInfoList=[GoodsDetailResponse.TagInfoList(key=isOnePsale, value=true), GoodsDetailResponse.TagInfoList(key=isSupportMix, value=true), GoodsDetailResponse.TagInfoList(key=isOnePsaleFreePostage, value=false), GoodsDetailResponse.TagInfoList(key=noReason7DReturn, value=false), GoodsDetailResponse.TagInfoList(key=1688_yx, value=false)], traceInfo=object_id@************^object_type@offer, sellerMixSetting=null, productCargoNumber=null, sellerDataInfo=GoodsDetailResponse.SellerDataInfo(tradeMedalLevel=3, compositeServiceScore=4.0, logisticsExperienceScore=3.0, disputeComplaintScore=3.0, offerExperienceScore=2.0, consultingExperienceScore=3.0, repeatPurchasePercent=0.4284276513730927, afterSalesExperienceScore=3.6, collect30DayWithin48HPercent=1.0, qualityRefundWithin30Day=0.008968609865470852), soldOut=6, channelPrice=null, promotionModel=null, tradeScore=5.0, topCategoryId=1038378, secondCategoryId=125372002, thirdCategoryId=*********, sellingPoint=null, offerIdentities=[tp_member], createDate=2025-04-14 18:31:27, isSelect=false, certificateList=[], promotionUrl=https://detail.1688.com/offer/************.html?kjSource=pc))
2025-07-06 22:01:26 INFO  [ForkJoinPool.commonPool-worker-3] [tid::uId::ip::os::browser:] c.f.support.alibaba.service.impl.ToolsServiceImpl - 开始解密旺旺昵称, openUid: BBBYBpUti9qFcgbDqB-D60ghw
2025-07-06 22:01:26 DEBUG [ForkJoinPool.commonPool-worker-2] [tid::uId::ip::os::browser:] c.f.s.d.c.product.PdcProductDetailConvertMapping - 使用预设ID: *************** 对应平台商品ID: ************
2025-07-06 22:01:26 INFO  [ForkJoinPool.commonPool-worker-3] [tid::uId::ip::os::browser:] c.fulfillmen.support.alibaba.sign.AlibabaSignature - 签名因子: param2/1/com.alibaba.account/wangwangnick.openuid.decrypt/8390330_aop_timestamp1751810486896access_tokenb49dfbf3-c3d7-4dfa-9b50-2d3300bad6c2openUidBBBYBpUti9qFcgbDqB-D60ghw 签名: 10004510F78724C7168211925422DF8CE5FB0D54
2025-07-06 22:01:26 WARN  [ForkJoinPool.commonPool-worker-3] [tid::uId::ip::os::browser:] io.micrometer.core.instrument.MeterRegistry - This Gauge has been already registered (MeterId{name='alibaba.api.concurrent.requests', tags=[]}), the registration will be ignored. Note that subsequent logs will be logged at debug level.
2025-07-06 22:01:26 DEBUG [reactor-http-nio-9] [tid::uId::ip::os::browser:] c.f.s.manager.support.alibaba.impl.ProductManager - result: GoodsDetailResponse.Result(success=true, code=200, message=null, result=GoodsDetailResponse.ProductDetail(offerId=************, categoryId=*********, categoryName=null, subject=emoji-lab系列猫与沙发/春日限定粉薄底赛车鞋休闲平底德训鞋女, subjectTrans=emoji-lab Series Cat and Sofa/Spring Limited Powder Thin-soled Racing Shoes Casual Flat German Training Shoes for Women, description=<div id="offer-template-0"></div><div style="width: 790.0px;"><img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01Q0gwGo2Dj0dmuPOs8_!!*************-0-cib.jpg" usemap="#_sdmap_0"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01DPGMyG2Dj0dkxbzy6_!!*************-0-cib.jpg" usemap="#_sdmap_1"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01ZF0NAE2Dj0doaBWPb_!!*************-0-cib.jpg" usemap="#_sdmap_2"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01kKEYAO2Dj0dmVa0RZ_!!*************-0-cib.jpg" usemap="#_sdmap_3"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01QZ3L3C2Dj0dmkJqW3_!!*************-0-cib.jpg" usemap="#_sdmap_4"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01Kc4W102Dj0dmVakB4_!!*************-0-cib.jpg" usemap="#_sdmap_5"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01Wf3XJs2Dj0dnQ2JaS_!!*************-0-cib.jpg" usemap="#_sdmap_6"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01PhGfmz2Dj0dkxZaIh_!!*************-0-cib.jpg" usemap="#_sdmap_7"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01vOOp7u2Dj0dnHJdWz_!!*************-0-cib.jpg" usemap="#_sdmap_8"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01AX1xXX2Dj0dnHHxZO_!!*************-0-cib.jpg" usemap="#_sdmap_9"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN011yqhIz2Dj0dnToF2w_!!*************-0-cib.jpg" usemap="#_sdmap_10"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01oLz4DZ2Dj0dnQ1RUR_!!*************-0-cib.jpg" usemap="#_sdmap_11"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01NQ4i2b2Dj0doaBzXg_!!*************-0-cib.jpg" usemap="#_sdmap_12"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01jKw9JX2Dj0doaCGCt_!!*************-0-cib.jpg" usemap="#_sdmap_13"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN018C7NMB2Dj0dnTpNio_!!*************-0-cib.jpg" usemap="#_sdmap_14"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01wQEB1Z2Dj0doaEPEr_!!*************-0-cib.jpg" usemap="#_sdmap_15"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN017JWhzQ2Dj0dmuPKjz_!!*************-0-cib.jpg" usemap="#_sdmap_16"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01mKY4Xb2Dj0do5Gpfs_!!*************-0-cib.jpg" usemap="#_sdmap_17"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01CmJPye2Dj0dgekC3L_!!*************-0-cib.jpg" usemap="#_sdmap_18"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01Q9WaQD2Dj0dngM3cC_!!*************-0-cib.jpg" usemap="#_sdmap_19"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01Ohy4bn2Dj0dlVddNw_!!*************-0-cib.jpg" usemap="#_sdmap_20"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN013q8m6U2Dj0dngNCJ6_!!*************-0-cib.jpg" usemap="#_sdmap_21"/></div><div id="offer-template-*************"></div><p>&nbsp;&nbsp;</p>, mainVideo=https://cloud.video.taobao.com/play/u/*************/p/1/e/6/t/1/************.mp4, detailVideo=null, productImage=GoodsDetailResponse.ProductImage(images=[https://cbu01.alicdn.com/img/ibank/O1CN01YySNns2Dj0dmVMAwk_!!*************-0-cib.jpg, https://cbu01.alicdn.com/img/ibank/O1CN01xDlFm52Dj0dng7QH9_!!*************-0-cib.jpg, https://cbu01.alicdn.com/img/ibank/O1CN01Mlate22Dj0dlVOnTk_!!*************-0-cib.jpg, https://cbu01.alicdn.com/img/ibank/O1CN01Wb1KFK2Dj0dmVLdgT_!!*************-0-cib.jpg, https://cbu01.alicdn.com/img/ibank/O1CN01xmxk2y2Dj0eq2RsEO_!!*************-0-cib.jpg], whiteImage=null), productAttribute=[GoodsDetailResponse.ProductAttribute(attributeId=364, attributeName=产品类别, value=老爹鞋, attributeNameTrans=Product category, valueTrans=Dad shoes), GoodsDetailResponse.ProductAttribute(attributeId=346, attributeName=产地, value=浙江台州, attributeNameTrans=Origin, valueTrans=Taizhou, zhejiang), GoodsDetailResponse.ProductAttribute(attributeId=973, attributeName=风格, value=休闲, attributeNameTrans=Style, valueTrans=Leisure), GoodsDetailResponse.ProductAttribute(attributeId=2531, attributeName=适合季节, value=夏季, attributeNameTrans=Suitable for the season, valueTrans=Summer), GoodsDetailResponse.ProductAttribute(attributeId=2531, attributeName=适合季节, value=春季, attributeNameTrans=Suitable for the season, valueTrans=Spring), GoodsDetailResponse.ProductAttribute(attributeId=2531, attributeName=适合季节, value=秋季, attributeNameTrans=Suitable for the season, valueTrans=Autumn), GoodsDetailResponse.ProductAttribute(attributeId=2600, attributeName=适用性别, value=女, attributeNameTrans=Applicable gender, valueTrans=Female), GoodsDetailResponse.ProductAttribute(attributeId=4921, attributeName=鞋面材质, value=人造PU, attributeNameTrans=Upper material, valueTrans=Artificial pu), GoodsDetailResponse.ProductAttribute(attributeId=8614, attributeName=流行元素, value=翻边, attributeNameTrans=Popular elements, valueTrans=Flanging), GoodsDetailResponse.ProductAttribute(attributeId=8614, attributeName=流行元素, value=镂空, attributeNameTrans=Popular elements, valueTrans=Hollow out), GoodsDetailResponse.ProductAttribute(attributeId=8614, attributeName=流行元素, value=蝴蝶结, attributeNameTrans=Popular elements, valueTrans=Bow), GoodsDetailResponse.ProductAttribute(attributeId=8614, attributeName=流行元素, value=车缝线, attributeNameTrans=Popular elements, valueTrans=Sewing thread), GoodsDetailResponse.ProductAttribute(attributeId=8614, attributeName=流行元素, value=松糕跟, attributeNameTrans=Popular elements, valueTrans=Platform heels), GoodsDetailResponse.ProductAttribute(attributeId=8614, attributeName=流行元素, value=皮革拼接, attributeNameTrans=Popular elements, valueTrans=Leather stitching), GoodsDetailResponse.ProductAttribute(attributeId=100000691, attributeName=货源类别, value=现货, attributeNameTrans=Supply category, valueTrans=Spot goods), GoodsDetailResponse.ProductAttribute(attributeId=100017842, attributeName=最快出货时间, value=1, attributeNameTrans=Fastest shipping time, valueTrans=1), GoodsDetailResponse.ProductAttribute(attributeId=100017996, attributeName=鞋头形状, value=圆头, attributeNameTrans=Toe shape, valueTrans=Round head), GoodsDetailResponse.ProductAttribute(attributeId=100017998, attributeName=鞋跟高度, value=中跟（3-5CM）, attributeNameTrans=Heel height, valueTrans=Medium heel (3-5cm)), GoodsDetailResponse.ProductAttribute(attributeId=3216, attributeName=颜色, value=苹果绿, attributeNameTrans=Color, valueTrans=Apple green), GoodsDetailResponse.ProductAttribute(attributeId=3216, attributeName=颜色, value=蝴蝶粉, attributeNameTrans=Color, valueTrans=Butterfly powder), GoodsDetailResponse.ProductAttribute(attributeId=3216, attributeName=颜色, value=苹果红, attributeNameTrans=Color, valueTrans=Apple red), GoodsDetailResponse.ProductAttribute(attributeId=450, attributeName=尺码, value=35, attributeNameTrans=Size, valueTrans=35), GoodsDetailResponse.ProductAttribute(attributeId=450, attributeName=尺码, value=36, attributeNameTrans=Size, valueTrans=36), GoodsDetailResponse.ProductAttribute(attributeId=450, attributeName=尺码, value=37, attributeNameTrans=Size, valueTrans=37), GoodsDetailResponse.ProductAttribute(attributeId=450, attributeName=尺码, value=38, attributeNameTrans=Size, valueTrans=38), GoodsDetailResponse.ProductAttribute(attributeId=450, attributeName=尺码, value=39, attributeNameTrans=Size, valueTrans=39), GoodsDetailResponse.ProductAttribute(attributeId=450, attributeName=尺码, value=40, attributeNameTrans=Size, valueTrans=40), GoodsDetailResponse.ProductAttribute(attributeId=1141, attributeName=功能, value=增高, attributeNameTrans=Function, valueTrans=Increase), GoodsDetailResponse.ProductAttribute(attributeId=1141, attributeName=功能, value=透气, attributeNameTrans=Function, valueTrans=Breathable), GoodsDetailResponse.ProductAttribute(attributeId=1141, attributeName=功能, value=平衡, attributeNameTrans=Function, valueTrans=Balance), GoodsDetailResponse.ProductAttribute(attributeId=1141, attributeName=功能, value=轻便, attributeNameTrans=Function, valueTrans=Light), GoodsDetailResponse.ProductAttribute(attributeId=2176, attributeName=品牌, value=无品牌, attributeNameTrans=Brand, valueTrans=Unbranded), GoodsDetailResponse.ProductAttribute(attributeId=2900, attributeName=图案, value=纯色, attributeNameTrans=Pattern, valueTrans=Solid color), GoodsDetailResponse.ProductAttribute(attributeId=4923, attributeName=鞋底材质, value=橡胶, attributeNameTrans=Sole material, valueTrans=Rubber), GoodsDetailResponse.ProductAttribute(attributeId=7021, attributeName=适用运动, value=通用, attributeNameTrans=Applicable sports, valueTrans=General), GoodsDetailResponse.ProductAttribute(attributeId=7108, attributeName=是否库存, value=否, attributeNameTrans=Is it in stock?, valueTrans=No), GoodsDetailResponse.ProductAttribute(attributeId=8243, attributeName=库存类型, value=整单, attributeNameTrans=Inventory type, valueTrans=Whole order), GoodsDetailResponse.ProductAttribute(attributeId=8508, attributeName=穿着方式, value=前系带, attributeNameTrans=Way of dressing, valueTrans=Front tie), GoodsDetailResponse.ProductAttribute(attributeId=8514, attributeName=是否外贸, value=否, attributeNameTrans=Whether to export, valueTrans=No), GoodsDetailResponse.ProductAttribute(attributeId=1626558, attributeName=鞋跟形状, value=平跟, attributeNameTrans=Heel shape, valueTrans=Flat), GoodsDetailResponse.ProductAttribute(attributeId=2231460, attributeName=内里材质, value=超纤, attributeNameTrans=Inner material, valueTrans=Super fiber), GoodsDetailResponse.ProductAttribute(attributeId=100017944, attributeName=开口深度, value=中口（7-11CM）, attributeNameTrans=Opening depth, valueTrans=Middle mouth (7-11cm)), GoodsDetailResponse.ProductAttribute(attributeId=100018421, attributeName=鞋底工艺, value=粘胶鞋, attributeNameTrans=Sole craftsmanship, valueTrans=Viscose shoes), GoodsDetailResponse.ProductAttribute(attributeId=100110361, attributeName=鞋垫材质, value=EVA, attributeNameTrans=Insole material, valueTrans=EVA), GoodsDetailResponse.ProductAttribute(attributeId=101271579, attributeName=质检报告, value=否, attributeNameTrans=Quality inspection report, valueTrans=No), GoodsDetailResponse.ProductAttribute(attributeId=140102779, attributeName=质检单位, value=sgs, attributeNameTrans=Quality inspection unit, valueTrans=sgs), GoodsDetailResponse.ProductAttribute(attributeId=1811, attributeName=款式, value=单鞋, attributeNameTrans=Style, valueTrans=Single shoes), GoodsDetailResponse.ProductAttribute(attributeId=10363196, attributeName=适用场景, value=日常, attributeNameTrans=Applicable scenarios, valueTrans=Daily), GoodsDetailResponse.ProductAttribute(attributeId=1957, attributeName=毛重, value=默认, attributeNameTrans=Gross weight, valueTrans=Default), GoodsDetailResponse.ProductAttribute(attributeId=100041414, attributeName=包装体积, value=默认, attributeNameTrans=Packing volume, valueTrans=Default), GoodsDetailResponse.ProductAttribute(attributeId=160540053, attributeName=上市年份季节（上市时间）, value=2025年春季, attributeNameTrans=Year and season of launch (time of launch), valueTrans=Spring 2025), GoodsDetailResponse.ProductAttribute(attributeId=171404504, attributeName=最晚发货时间, value=3天, attributeNameTrans=Latest delivery time, valueTrans=3 days), GoodsDetailResponse.ProductAttribute(attributeId=182282223, attributeName=是否跨境出口专供货源, value=否, attributeNameTrans=Whether to exclusively supply goods for cross-border export, valueTrans=No), GoodsDetailResponse.ProductAttribute(attributeId=20490, attributeName=闭合方式, value=系带, attributeNameTrans=Closing method, valueTrans=Lace up), GoodsDetailResponse.ProductAttribute(attributeId=7170, attributeName=适用年龄段, value=成年, attributeNameTrans=Applicable age group, valueTrans=Adult)], productSkuInfos=[GoodsDetailResponse.ProductSkuInfo(amountOnSale=1000, price=null, jxhyPrice=null, skuId=*************, specId=34e26a498df930e77ae7c6c5399cdcb4, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=苹果绿, valueTrans=Apple green, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01UI8Aot2Dj0dldD6o6_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=35, valueTrans=35, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=998, price=null, jxhyPrice=null, skuId=*************, specId=1bf10b0b0b7cd9f66cd76a1521687208, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=苹果绿, valueTrans=Apple green, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01UI8Aot2Dj0dldD6o6_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=36, valueTrans=36, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=1000, price=null, jxhyPrice=null, skuId=*************, specId=1925e981490d0d39626ea5553b413b27, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=苹果绿, valueTrans=Apple green, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01UI8Aot2Dj0dldD6o6_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=37, valueTrans=37, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=1000, price=null, jxhyPrice=null, skuId=*************, specId=ac303599e7005fb651337f47f7af39f4, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=苹果绿, valueTrans=Apple green, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01UI8Aot2Dj0dldD6o6_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=38, valueTrans=38, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=999, price=null, jxhyPrice=null, skuId=*************, specId=a29f39727d547f464974084658ec4722, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=苹果绿, valueTrans=Apple green, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01UI8Aot2Dj0dldD6o6_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=39, valueTrans=39, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=1000, price=null, jxhyPrice=null, skuId=*************, specId=c54d7885c6062376641412f9ecdd388e, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=苹果绿, valueTrans=Apple green, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01UI8Aot2Dj0dldD6o6_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=40, valueTrans=40, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=1000, price=null, jxhyPrice=null, skuId=*************, specId=eba49acb79261e9e8179577f511c8bf5, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=蝴蝶粉, valueTrans=Butterfly powder, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01QRF4GD2Dj0do5EkRK_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=35, valueTrans=35, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=999, price=null, jxhyPrice=null, skuId=*************, specId=4aae85b73690227c0272892177c1f87d, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=蝴蝶粉, valueTrans=Butterfly powder, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01QRF4GD2Dj0do5EkRK_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=36, valueTrans=36, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=1000, price=null, jxhyPrice=null, skuId=*************, specId=22073a3fccc70c2511392e4547040f42, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=蝴蝶粉, valueTrans=Butterfly powder, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01QRF4GD2Dj0do5EkRK_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=37, valueTrans=37, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=1000, price=null, jxhyPrice=null, skuId=*************, specId=312e28fd5a8d2192cd6cc3953bbc61a3, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=蝴蝶粉, valueTrans=Butterfly powder, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01QRF4GD2Dj0do5EkRK_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=38, valueTrans=38, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=1000, price=null, jxhyPrice=null, skuId=*************, specId=671856155b822d9539914d2e69712d57, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=蝴蝶粉, valueTrans=Butterfly powder, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01QRF4GD2Dj0do5EkRK_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=39, valueTrans=39, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=1000, price=null, jxhyPrice=null, skuId=*************, specId=d4d0ef81960bf3a881022c49b93905ad, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=蝴蝶粉, valueTrans=Butterfly powder, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01QRF4GD2Dj0do5EkRK_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=40, valueTrans=40, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=1000, price=null, jxhyPrice=null, skuId=*************, specId=443788b7289646b81c54c7b8c9b3502d, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=苹果红, valueTrans=Apple red, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01N5JWe52Dj0dngJluy_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=35, valueTrans=35, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=1000, price=null, jxhyPrice=null, skuId=*************, specId=7f1f235dc728cd8a4eb14012504dabf9, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=苹果红, valueTrans=Apple red, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01N5JWe52Dj0dngJluy_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=36, valueTrans=36, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=999, price=null, jxhyPrice=null, skuId=*************, specId=dbf9cd9e90dea825f1c3cb27ebacb582, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=苹果红, valueTrans=Apple red, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01N5JWe52Dj0dngJluy_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=37, valueTrans=37, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=1000, price=null, jxhyPrice=null, skuId=*************, specId=44a3869259e14b2c91311a9103606f3d, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=苹果红, valueTrans=Apple red, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01N5JWe52Dj0dngJluy_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=38, valueTrans=38, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=1000, price=null, jxhyPrice=null, skuId=*************, specId=cd579e6cc5e526b2acdb1ebfae1643bb, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=苹果红, valueTrans=Apple red, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01N5JWe52Dj0dngJluy_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=39, valueTrans=39, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=1000, price=null, jxhyPrice=null, skuId=*************, specId=7dfe4aa75292b97ce35969ea81d491eb, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=苹果红, valueTrans=Apple red, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01N5JWe52Dj0dngJluy_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=450, attributeName=尺码, attributeNameTrans=Size, value=40, valueTrans=40, skuImageUrl=null)], cargoNumber=爱尚&M50, promotionPrice=null, consignPrice=58.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=58))], productSaleInfo=GoodsDetailResponse.ProductSaleInfo(amountOnSale=17995, priceRangeList=[GoodsDetailResponse.PriceRange(startQuantity=1, price=58.0, promotionPrice=null), GoodsDetailResponse.PriceRange(startQuantity=50, price=57.0, promotionPrice=null), GoodsDetailResponse.PriceRange(startQuantity=100, price=56.0, promotionPrice=null)], quoteType=2, unitInfo=GoodsDetailResponse.UnitInfo(unit=双, transUnit=Double), fenxiaoSaleInfo=GoodsDetailResponse.FenxiaoSaleInfo(onePieceFreePostage=false, startQuantity=1, onePiecePrice=null, offerPrice=null), consignPrice=null, jxhyPrice=null), productShippingInfo=GoodsDetailResponse.ProductShippingInfo(sendGoodsAddressText=浙江省台州市, weight=null, width=null, height=null, length=null, shippingTimeGuarantee=null, skuShippingInfoList=[GoodsDetailResponse.SkuShippingInfo(specId=34e26a498df930e77ae7c6c5399cdcb4, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=1bf10b0b0b7cd9f66cd76a1521687208, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=1925e981490d0d39626ea5553b413b27, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=ac303599e7005fb651337f47f7af39f4, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=a29f39727d547f464974084658ec4722, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=c54d7885c6062376641412f9ecdd388e, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=eba49acb79261e9e8179577f511c8bf5, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=4aae85b73690227c0272892177c1f87d, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=22073a3fccc70c2511392e4547040f42, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=312e28fd5a8d2192cd6cc3953bbc61a3, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=671856155b822d9539914d2e69712d57, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=d4d0ef81960bf3a881022c49b93905ad, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=443788b7289646b81c54c7b8c9b3502d, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=7f1f235dc728cd8a4eb14012504dabf9, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=dbf9cd9e90dea825f1c3cb27ebacb582, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=44a3869259e14b2c91311a9103606f3d, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=cd579e6cc5e526b2acdb1ebfae1643bb, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980), GoodsDetailResponse.SkuShippingInfo(specId=7dfe4aa75292b97ce35969ea81d491eb, skuId=*************, width=0.0, length=0.0, height=0.0, weight=980)], skuShippingDetails=[GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.746, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.754, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.731, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.704, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.785, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.789, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.769, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.721, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.698, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.751, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.728, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.733, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.727, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.772, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.759, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.763, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.748, aiWeightAccuracy=78%, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.98, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=0.759, aiWeightAccuracy=78%, pkgSizeSource=商家自填)], pkgSizeSource=null), isJxhy=false, sellerOpenId=BBBYBpUti9qFcgbDqB-D60ghw, minOrderQuantity=1, batchNumber=null, status=published, tagInfoList=[GoodsDetailResponse.TagInfoList(key=isOnePsale, value=true), GoodsDetailResponse.TagInfoList(key=isSupportMix, value=true), GoodsDetailResponse.TagInfoList(key=isOnePsaleFreePostage, value=false), GoodsDetailResponse.TagInfoList(key=noReason7DReturn, value=false), GoodsDetailResponse.TagInfoList(key=1688_yx, value=false)], traceInfo=object_id@************^object_type@offer, sellerMixSetting=null, productCargoNumber=null, sellerDataInfo=GoodsDetailResponse.SellerDataInfo(tradeMedalLevel=3, compositeServiceScore=4.0, logisticsExperienceScore=3.0, disputeComplaintScore=3.0, offerExperienceScore=2.0, consultingExperienceScore=3.0, repeatPurchasePercent=0.4284276513730927, afterSalesExperienceScore=3.6, collect30DayWithin48HPercent=1.0, qualityRefundWithin30Day=0.008968609865470852), soldOut=6, channelPrice=null, promotionModel=null, tradeScore=5.0, topCategoryId=1038378, secondCategoryId=125372002, thirdCategoryId=*********, sellingPoint=null, offerIdentities=[tp_member], createDate=2025-04-14 18:31:27, isSelect=false, certificateList=[], promotionUrl=https://detail.1688.com/offer/************.html?kjSource=pc))
2025-07-06 22:01:26 DEBUG [ForkJoinPool.commonPool-worker-1] [tid::uId::ip::os::browser:] c.f.s.d.c.product.PdcProductDetailConvertMapping - 使用预设ID: *************** 对应平台商品ID: ************
2025-07-06 22:01:26 INFO  [ForkJoinPool.commonPool-worker-4] [tid::uId::ip::os::browser:] c.f.support.alibaba.service.impl.ToolsServiceImpl - 开始解密旺旺昵称, openUid: BBBYBpUti9qFcgbDqB-D60ghw
2025-07-06 22:01:26 INFO  [ForkJoinPool.commonPool-worker-4] [tid::uId::ip::os::browser:] c.fulfillmen.support.alibaba.sign.AlibabaSignature - 签名因子: param2/1/com.alibaba.account/wangwangnick.openuid.decrypt/8390330_aop_timestamp1751810486930access_tokenb49dfbf3-c3d7-4dfa-9b50-2d3300bad6c2openUidBBBYBpUti9qFcgbDqB-D60ghw 签名: F08B5928B7263EC1B0178ACC92857BBB91811663
2025-07-06 22:01:26 DEBUG [ForkJoinPool.commonPool-worker-2] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.MetaInfoHashUtils - 计算metaInfoHash [pdcProductMappingId=*************** => platformProductId=************]: hash=92eb4326ae705b7c3e3530ab9fd571c5, metaInfo长度=24880
2025-07-06 22:01:26 DEBUG [ForkJoinPool.commonPool-worker-1] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.MetaInfoHashUtils - 计算metaInfoHash [pdcProductMappingId=*************** => platformProductId=************]: hash=92eb4326ae705b7c3e3530ab9fd571c5, metaInfo长度=24880
2025-07-06 22:01:26 DEBUG [ForkJoinPool.commonPool-worker-2] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - 忽略表的多租户处理: pdc_product_mapping
2025-07-06 22:01:26 DEBUG [ForkJoinPool.commonPool-worker-2] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - 忽略表的多租户处理: pdc_product_mapping
2025-07-06 22:01:26 DEBUG [ForkJoinPool.commonPool-worker-2] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - 商品详情同步到数据库完成, id: ***************
2025-07-06 22:01:26 DEBUG [ForkJoinPool.commonPool-worker-1] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - 忽略表的多租户处理: pdc_product_mapping
2025-07-06 22:01:27 DEBUG [ForkJoinPool.commonPool-worker-2] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - 商品详情更新到缓存完成, id: ***************
2025-07-06 22:01:27 DEBUG [ForkJoinPool.commonPool-worker-2] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - 自动同步功能已关闭，跳过事件发布
2025-07-06 22:01:27 DEBUG [ForkJoinPool.commonPool-worker-1] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - 忽略表的多租户处理: pdc_product_mapping
2025-07-06 22:01:27 DEBUG [ForkJoinPool.commonPool-worker-1] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - 商品详情同步到数据库完成, id: ***************
2025-07-06 22:01:27 DEBUG [ForkJoinPool.commonPool-worker-1] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - 商品详情更新到缓存完成, id: ***************
2025-07-06 22:01:27 DEBUG [ForkJoinPool.commonPool-worker-1] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - 自动同步功能已关闭，跳过事件发布
2025-07-06 22:01:27 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1094019126391660544]:[0] SPU创建成功，spuId: 715191447678465
2025-07-06 22:01:27 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1094019126391660544]:[0] 多规格SKU创建成功，数量: 18
2025-07-06 22:01:27 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1094019126391660544]:[0] 产品数据获取成功: platformProductId=***************, spuId=715191447678465
2025-07-06 22:01:27 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.ProductServiceImpl - [1094019126391660544]:[0] ✅ 产品数据验证通过: platformProductId=***************, SKU数量=18
2025-07-06 22:01:27 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.ProductServiceImpl - [1094019126391660544]:[0] ✅ 更新多级缓存完成: platformProductId=***************
2025-07-06 22:01:27 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.ProductServiceImpl - [1094019126391660544]:[0] ✅ 商品详情VO转换完成: platformProductId=***************, spuId=715191447678465
2025-07-06 22:01:27 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094019126391660544]:[0] [GET] /api/products/*************** 200 1406ms
2025-07-06 22:01:27 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.fulfillmen.shop.common.context.UserContextHolder - [1094019126391660544]:[0] 清除用户上下文和 MDC 信息
2025-07-06 22:01:27 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094019126391660544]:[0] 清理增强租户上下文
2025-07-06 22:01:27 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094019126391660544]:[0] 过滤器清理租户上下文完成
2025-07-06 22:06:05 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-06 22:06:05 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-06 22:06:05 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-06 22:06:05 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-06 22:06:05 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:06:05 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:06:05 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:06:05 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:06:05 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:06:05 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:06:05 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:06:05 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:06:05 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:06:05 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:06:05 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:06:05 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:06:05 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-06 22:06:05 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-06 22:06:05 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-06 22:06:05 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Revert Remote [MachineState{machineId=7, lastTimeStamp=1751810765157}] instanceId:[InstanceId{instanceId=************:15881, stable=false}] @ namespace:[fulfillmen-shop].
2025-07-06 22:06:05 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-06 22:06:05 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-06 22:06:05 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-06 22:06:05 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-06 22:06:05 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-06 22:06:07 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-06 22:06:07 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-06 22:06:07 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-06 22:06:07 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-06 22:06:09 INFO  [background-preinit] [tid::uId::ip::os::browser:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-06 22:06:09 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Starting BootstrapApplication using Java 21.0.5 with PID 20642 (/Users/<USER>/work/fulfillmen/fulfillmen-shop/fulfillmen-shop-bootstrap/target/classes started by yzsama in /Users/<USER>/work/fulfillmen/fulfillmen-workspace)
2025-07-06 22:06:09 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Running with Spring Boot v3.3.11, Spring v6.1.19
2025-07-06 22:06:09 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - The following 1 profile is active: "local"
2025-07-06 22:06:10 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-06 22:06:10 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-06 22:06:10 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-07-06 22:06:10 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.s.a.dao.SaTokenDaoRedissionConfiguration - [Fulfillmen Starter] - Auto Configuration 'SaToken-Dao-Redis' completed initialization.
2025-07-06 22:06:11 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-06 22:06:11 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-06 22:06:11 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.d.m.a.MybatisPlusAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'MyBatis Plus' completed initialization.
2025-07-06 22:06:11 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - 多租户拦截器已配置，忽略表: [tenant_commission_config, tenants, regions, tenant_plan_relation, sys_alibaba_category, subregions, sys_users, tenant_domains, sys_option, openapi_account, pdc_product_mapping, tenant_plans, tenants_info, tenant_files, sys_config, openapi_account_permission, openapi_interface, tenant_locales]
2025-07-06 22:06:11 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.d.m.a.i.MyBatisPlusIdGeneratorConfiguration - [Fulfillmen Starter] - Auto Configuration 'MyBatis Plus-IdGenerator-CosId' completed initialization.
2025-07-06 22:06:11 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.FulfillmenWebMvcConfig - Default locale initialized to: en_US
2025-07-06 22:06:11 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.FulfillmenWebMvcConfig - MessageSource configured with basenames: [i18n/messages, i18n/openapi-message]
2025-07-06 22:06:11 WARN  [main] [tid::uId::ip::os::browser:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-06 22:06:11 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-06 22:06:11 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2445 ms
2025-07-06 22:06:11 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - === 过滤器配置信息 ===
2025-07-06 22:06:11 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 租户过滤器: enabled=true, order=-2147483638
2025-07-06 22:06:11 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - MDC 过滤器: enabled=true, order=-2147483628
2025-07-06 22:06:11 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 过滤器优先级说明:
过滤器执行顺序（数值越小优先级越高）：

1. TRACE_FILTER     (-2147483648) - 链路跟踪过滤器，生成 TraceId
2. TENANT_FILTER    (-2147483638) - 租户过滤器，设置租户上下文
3. MDC_FILTER       (-2147483628) - MDC 过滤器，设置日志上下文
4. XSS_FILTER       (-2147483548) - XSS 过滤器，安全防护
5. CORS_FILTER      (-2147483538) - CORS 过滤器，跨域处理
6. LOG_FILTER       (2147483637) - 日志过滤器，记录请求响应

注意：
- 链路跟踪过滤器优先级最高，确保 TraceId 在整个请求生命周期中可用
- 租户过滤器在链路跟踪之后，为后续过滤器提供租户上下文
- MDC 过滤器在租户过滤器之后，可以获取到租户信息并设置到日志上下文
- 安全相关过滤器（XSS、CORS）在业务过滤器之前执行
- 日志过滤器优先级最低，记录完整的请求响应信息

2025-07-06 22:06:11 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - ===================
2025-07-06 22:06:11 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 租户过滤器已注册 [enabled=true, order=-2147483638, urlPatterns=[/*], excludePatterns=20]
2025-07-06 22:06:11 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - MDC 过滤器已注册 [enabled=true, order=-2147483628, urlPatterns=[/*], features=IP标准化,浏览器信息,操作系统信息]
2025-07-06 22:06:11 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.trace.TraceAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web-Trace' completed initialization.
2025-07-06 22:06:12 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.l.i.autoconfigure.LogAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Log-interceptor' completed initialization.
2025-07-06 22:06:12 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?nayasource\.com
2025-07-06 22:06:12 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?nayasource\.com
2025-07-06 22:06:12 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http://localhost:[0-9]+
2025-07-06 22:06:12 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http://localhost:[0-9]+
2025-07-06 22:06:12 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?aliyuncs\.com
2025-07-06 22:06:12 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?aliyuncs\.com
2025-07-06 22:06:12 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?sealoshzh\.site
2025-07-06 22:06:12 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?sealoshzh\.site
2025-07-06 22:06:12 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 检测到 allowCredentials=true 且使用通配符配置，这可能导致CORS错误
2025-07-06 22:06:12 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 建议：1) 设置 allowCredentials=false，或 2) 使用具体的域名列表替换通配符
2025-07-06 22:06:12 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 当前策略：保持 allowCredentials=true，但建议检查配置
2025-07-06 22:06:12 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 跨域配置初始化完成 [常规域名: 0, 正则域名: 4, 允许凭证: true, 缓存时间: 3600s]
2025-07-06 22:06:12 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.w.a.cors.CorsConfigurationValidator - 
🔍 CORS配置分析报告:
==================================================
 1. ⚠️  安全建议: 生产环境中allowCredentials=true时建议明确指定允许的HTTP方法
 2. ⚠️  安全建议: 生产环境中allowCredentials=true时建议明确指定允许的请求头
 3. 🔒 安全建议: 生产环境建议明确指定允许的HTTP方法，避免使用 '*'
 4. ✅ 最佳实践: 正在使用正则表达式匹配，这是推荐的域名配置方式
==================================================
📖 更多信息请参考: fulfillmen-starter-web/CORS-CONFIG-EXAMPLE.md

2025-07-06 22:06:12 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-07-06 22:06:12 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-07-06 22:06:12 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.r.autoconfigure.RedissonAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Redisson' completed initialization.
2025-07-06 22:06:12 INFO  [main] [tid::uId::ip::os::browser:] org.redisson.Version - Redisson 3.45.1
2025-07-06 22:06:12 INFO  [redisson-netty-1-5] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-06 22:06:12 INFO  [redisson-netty-1-19] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-06 22:06:12 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-06 22:06:12 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 货币汇率缓存初始化完成
2025-07-06 22:06:12 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: fixed_window
2025-07-06 22:06:12 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: sliding_window
2025-07-06 22:06:12 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: token_bucket
2025-07-06 22:06:12 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: leaky_bucket
2025-07-06 22:06:12 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已启用
2025-07-06 22:06:12 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.g.a.GraphicCaptchaAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Captcha-Graphic' completed initialization.
2025-07-06 22:06:12 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - 🎯 自动同步功能初始化: 禁用
2025-07-06 22:06:13 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.manager.strategy.SyncStrategyFactory - 同步策略工厂初始化完成，可用策略: [AUTO, DISABLED, MANUAL]
2025-07-06 22:06:13 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.sync.chain.config.ProductSyncChainConfig - 产品同步责任链配置初始化完成
2025-07-06 22:06:13 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.sync.chain.handlers.CacheUpdateHandler - 产品同步缓存初始化完成
2025-07-06 22:06:13 WARN  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'productServiceImpl' defined in file [/Users/<USER>/work/fulfillmen/fulfillmen-shop/fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/target/classes/com/fulfillmen/shop/frontend/service/impl/ProductServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 1: No qualifying bean of type 'com.fulfillmen.shop.manager.service.IProductSyncService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Qualifier("chainBasedProductSyncServiceImpl")}
2025-07-06 22:06:13 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-06 22:06:13 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-06 22:06:13 ERROR [main] [tid::uId::ip::os::browser:] org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'productServiceImpl' defined in file [/Users/<USER>/work/fulfillmen/fulfillmen-shop/fulfillmen-shop-system/fulfillmen-shop-frontend/frontend-service/target/classes/com/fulfillmen/shop/frontend/service/impl/ProductServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 1: No qualifying bean of type 'com.fulfillmen.shop.manager.service.IProductSyncService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Qualifier("chainBasedProductSyncServiceImpl")}
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:795)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1364)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1353)
	at com.fulfillmen.shop.BootstrapApplication.main(BootstrapApplication.java:47)
Caused by: org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.fulfillmen.shop.manager.service.IProductSyncService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Qualifier("chainBasedProductSyncServiceImpl")}
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.raiseNoMatchingBeanFound(DefaultListableBeanFactory.java:1894)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1411)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1358)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
	... 19 common frames omitted
2025-07-06 22:08:25 INFO  [background-preinit] [tid::uId::ip::os::browser:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-06 22:08:25 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Starting BootstrapApplication using Java 21.0.5 with PID 22608 (/Users/<USER>/work/fulfillmen/fulfillmen-shop/fulfillmen-shop-bootstrap/target/classes started by yzsama in /Users/<USER>/work/fulfillmen/fulfillmen-workspace)
2025-07-06 22:08:25 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Running with Spring Boot v3.3.11, Spring v6.1.19
2025-07-06 22:08:25 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - The following 1 profile is active: "local"
2025-07-06 22:08:26 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-06 22:08:26 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-06 22:08:26 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 23 ms. Found 0 Redis repository interfaces.
2025-07-06 22:08:26 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.s.a.dao.SaTokenDaoRedissionConfiguration - [Fulfillmen Starter] - Auto Configuration 'SaToken-Dao-Redis' completed initialization.
2025-07-06 22:08:26 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-06 22:08:26 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-06 22:08:26 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.d.m.a.MybatisPlusAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'MyBatis Plus' completed initialization.
2025-07-06 22:08:26 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - 多租户拦截器已配置，忽略表: [tenant_commission_config, tenants, regions, tenant_plan_relation, sys_alibaba_category, subregions, sys_users, tenant_domains, sys_option, openapi_account, pdc_product_mapping, tenant_plans, tenants_info, tenant_files, sys_config, openapi_account_permission, openapi_interface, tenant_locales]
2025-07-06 22:08:26 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.d.m.a.i.MyBatisPlusIdGeneratorConfiguration - [Fulfillmen Starter] - Auto Configuration 'MyBatis Plus-IdGenerator-CosId' completed initialization.
2025-07-06 22:08:27 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.FulfillmenWebMvcConfig - Default locale initialized to: en_US
2025-07-06 22:08:27 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.FulfillmenWebMvcConfig - MessageSource configured with basenames: [i18n/messages, i18n/openapi-message]
2025-07-06 22:08:27 WARN  [main] [tid::uId::ip::os::browser:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-06 22:08:27 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-06 22:08:27 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2143 ms
2025-07-06 22:08:27 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - === 过滤器配置信息 ===
2025-07-06 22:08:27 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 租户过滤器: enabled=true, order=-2147483638
2025-07-06 22:08:27 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - MDC 过滤器: enabled=true, order=-2147483628
2025-07-06 22:08:27 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 过滤器优先级说明:
过滤器执行顺序（数值越小优先级越高）：

1. TRACE_FILTER     (-2147483648) - 链路跟踪过滤器，生成 TraceId
2. TENANT_FILTER    (-2147483638) - 租户过滤器，设置租户上下文
3. MDC_FILTER       (-2147483628) - MDC 过滤器，设置日志上下文
4. XSS_FILTER       (-2147483548) - XSS 过滤器，安全防护
5. CORS_FILTER      (-2147483538) - CORS 过滤器，跨域处理
6. LOG_FILTER       (2147483637) - 日志过滤器，记录请求响应

注意：
- 链路跟踪过滤器优先级最高，确保 TraceId 在整个请求生命周期中可用
- 租户过滤器在链路跟踪之后，为后续过滤器提供租户上下文
- MDC 过滤器在租户过滤器之后，可以获取到租户信息并设置到日志上下文
- 安全相关过滤器（XSS、CORS）在业务过滤器之前执行
- 日志过滤器优先级最低，记录完整的请求响应信息

2025-07-06 22:08:27 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - ===================
2025-07-06 22:08:27 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 租户过滤器已注册 [enabled=true, order=-2147483638, urlPatterns=[/*], excludePatterns=20]
2025-07-06 22:08:27 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - MDC 过滤器已注册 [enabled=true, order=-2147483628, urlPatterns=[/*], features=IP标准化,浏览器信息,操作系统信息]
2025-07-06 22:08:27 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.trace.TraceAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web-Trace' completed initialization.
2025-07-06 22:08:27 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.l.i.autoconfigure.LogAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Log-interceptor' completed initialization.
2025-07-06 22:08:27 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?nayasource\.com
2025-07-06 22:08:27 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?nayasource\.com
2025-07-06 22:08:27 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http://localhost:[0-9]+
2025-07-06 22:08:27 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http://localhost:[0-9]+
2025-07-06 22:08:27 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?aliyuncs\.com
2025-07-06 22:08:27 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?aliyuncs\.com
2025-07-06 22:08:27 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?sealoshzh\.site
2025-07-06 22:08:27 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?sealoshzh\.site
2025-07-06 22:08:27 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 检测到 allowCredentials=true 且使用通配符配置，这可能导致CORS错误
2025-07-06 22:08:27 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 建议：1) 设置 allowCredentials=false，或 2) 使用具体的域名列表替换通配符
2025-07-06 22:08:27 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 当前策略：保持 allowCredentials=true，但建议检查配置
2025-07-06 22:08:27 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 跨域配置初始化完成 [常规域名: 0, 正则域名: 4, 允许凭证: true, 缓存时间: 3600s]
2025-07-06 22:08:27 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.w.a.cors.CorsConfigurationValidator - 
🔍 CORS配置分析报告:
==================================================
 1. ⚠️  安全建议: 生产环境中allowCredentials=true时建议明确指定允许的HTTP方法
 2. ⚠️  安全建议: 生产环境中allowCredentials=true时建议明确指定允许的请求头
 3. 🔒 安全建议: 生产环境建议明确指定允许的HTTP方法，避免使用 '*'
 4. ✅ 最佳实践: 正在使用正则表达式匹配，这是推荐的域名配置方式
==================================================
📖 更多信息请参考: fulfillmen-starter-web/CORS-CONFIG-EXAMPLE.md

2025-07-06 22:08:27 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-07-06 22:08:27 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-07-06 22:08:27 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.r.autoconfigure.RedissonAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Redisson' completed initialization.
2025-07-06 22:08:27 INFO  [main] [tid::uId::ip::os::browser:] org.redisson.Version - Redisson 3.45.1
2025-07-06 22:08:28 INFO  [redisson-netty-1-5] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-06 22:08:28 INFO  [redisson-netty-1-19] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-06 22:08:28 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-06 22:08:28 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 货币汇率缓存初始化完成
2025-07-06 22:08:28 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: fixed_window
2025-07-06 22:08:28 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: sliding_window
2025-07-06 22:08:28 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: token_bucket
2025-07-06 22:08:28 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: leaky_bucket
2025-07-06 22:08:28 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已启用
2025-07-06 22:08:28 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.g.a.GraphicCaptchaAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Captcha-Graphic' completed initialization.
2025-07-06 22:08:28 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - 🎯 自动同步功能初始化: 禁用
2025-07-06 22:08:28 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.sync.chain.handlers.CacheUpdateHandler - 产品同步缓存初始化完成
2025-07-06 22:08:28 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.manager.strategy.SyncStrategyFactory - 同步策略工厂初始化完成，可用策略: [MANUAL, AUTO, DISABLED]
2025-07-06 22:08:28 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.sync.chain.config.ProductSyncChainConfig - 产品同步责任链配置初始化完成
2025-07-06 22:08:28 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl - 缓存配置详情: ProductDetailVO[本地:8min,远程:25min,容量:300]
2025-07-06 22:08:28 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl - 🎯 ProductService优化缓存配置完成:
2025-07-06 22:08:28 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── ProductDetailVO缓存: 本地8min/远程25min, 容量300, 键前缀: frontend:product:vo:
2025-07-06 22:08:28 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── 双层缓存策略: 本地内存 + Redis远程
2025-07-06 22:08:28 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── 空值缓存: 已启用，防止缓存穿透
2025-07-06 22:08:28 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   └── 同步机制: 已启用，保证多实例一致性
2025-07-06 22:08:28 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Initialized CompositeLocaleResolver with default locale: en_US and supported locales: [en_US, zh_CN, zh_TW]
2025-07-06 22:08:28 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Initialized LocaleChangeInterceptor with param name: lang
2025-07-06 22:08:28 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.autoconfigure.SpringDocAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'ApiDoc' completed initialization.
2025-07-06 22:08:28 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.s.autoconfigure.SaTokenAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'SaToken' completed initialization.
2025-07-06 22:08:28 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.mvc.WebMvcAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web MVC' completed initialization.
2025-07-06 22:08:28 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-06 22:08:28 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已注册: order=100, patterns=/**, rate=100/60s
2025-07-06 22:08:28 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Added LocaleChangeInterceptor to registry
2025-07-06 22:08:29 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.a.response.GlobalResponseAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web-Global Response' completed initialization.
2025-07-06 22:08:29 INFO  [main] [tid::uId::ip::os::browser:] c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@2bef09c0
2025-07-06 22:08:29 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.j.autoconfigure.JetCacheAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'JetCache' completed initialization.
2025-07-06 22:08:29 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote instanceId:[InstanceId{instanceId=************:22608, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-06 22:08:29 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote machineState:[MachineState{machineId=7, lastTimeStamp=1751810909244}] - instanceId:[InstanceId{instanceId=************:22608, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-06 22:08:29 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no].
2025-07-06 22:08:29 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-06 22:08:29 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-06 22:08:29 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.order_no] jobSize:[0].
2025-07-06 22:08:29 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no].
2025-07-06 22:08:29 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2].
2025-07-06 22:08:29 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2] start.
2025-07-06 22:08:29 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.user_no] jobSize:[0].
2025-07-06 22:08:30 DEBUG [main] [tid::uId::ip::os::browser:] c.f.starter.log.interceptor.handler.LogFilter - Filter 'logFilter' configured for use
2025-07-06 22:08:30 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-06 22:08:30 INFO  [main] [tid::uId::ip::os::browser:] org.xnio - XNIO version 3.8.16.Final
2025-07-06 22:08:30 INFO  [main] [tid::uId::ip::os::browser:] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-06 22:08:30 INFO  [main] [tid::uId::ip::os::browser:] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-06 22:08:30 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-07-06 22:08:30 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-06 22:08:30 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 56 ms
2025-07-06 22:08:30 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 13 endpoints beneath base path '/actuator'
2025-07-06 22:08:30 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已注册: order=100, patterns=/**, rate=100/60s
2025-07-06 22:08:30 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Added LocaleChangeInterceptor to registry
2025-07-06 22:08:30 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-06 22:08:30 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8099 (http) with context path '/'
2025-07-06 22:08:30 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Started BootstrapApplication in 5.593 seconds (process running for 6.351)
2025-07-06 22:08:30 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - ----------------------------------------------
2025-07-06 22:08:30 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Fulfillmen Shop service started successfully.
2025-07-06 22:08:30 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - API地址：http://127.0.0.1:8080
2025-07-06 22:08:30 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - API文档：http://127.0.0.1:8080/doc.html
2025-07-06 22:08:30 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - ----------------------------------------------
2025-07-06 22:08:30 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 开始初始化汇率缓存...
2025-07-06 22:08:30 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 开始定时刷新汇率缓存...
2025-07-06 22:08:30 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> USD
2025-07-06 22:08:30 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-USD = 0.1396
2025-07-06 22:08:30 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> USD
2025-07-06 22:08:30 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-USD = 0.1396
2025-07-06 22:08:30 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> EUR
2025-07-06 22:08:30 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> EUR
2025-07-06 22:08:30 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-EUR = 0.1185
2025-07-06 22:08:30 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-EUR = 0.1185
2025-07-06 22:08:30 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> JPY
2025-07-06 22:08:30 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-JPY = 20.1073
2025-07-06 22:08:30 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> JPY
2025-07-06 22:08:30 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-JPY = 20.1073
2025-07-06 22:08:30 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> KRW
2025-07-06 22:08:30 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-KRW = 190.1975
2025-07-06 22:08:30 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> KRW
2025-07-06 22:08:30 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-KRW = 190.1975
2025-07-06 22:08:31 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> INR
2025-07-06 22:08:31 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-INR = 11.9314
2025-07-06 22:08:31 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> INR
2025-07-06 22:08:31 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-INR = 11.9314
2025-07-06 22:08:31 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.domain.util.CurrencyConversionUtils - 批量更新汇率缓存，共 5 条记录
2025-07-06 22:08:31 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - CNY基础汇率缓存更新完成: 成功 5/5 条记录
2025-07-06 22:08:31 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 定时刷新汇率缓存完成，缓存条目数: 5
2025-07-06 22:08:31 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.domain.util.CurrencyConversionUtils - 批量更新汇率缓存，共 5 条记录
2025-07-06 22:08:31 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - CNY基础汇率缓存更新完成: 成功 5/5 条记录
2025-07-06 22:08:31 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 汇率缓存初始化完成，缓存条目数: 5
2025-07-06 22:08:31 INFO  [RMI TCP Connection(8)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-06 22:08:31 INFO  [RMI TCP Connection(7)-127.0.0.1] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-06 22:08:31 INFO  [RMI TCP Connection(7)-127.0.0.1] [tid::uId::ip::os::browser:] org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-06 22:08:31 INFO  [RMI TCP Connection(7)-127.0.0.1] [tid::uId::ip::os::browser:] org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-06 22:08:31 INFO  [RMI TCP Connection(8)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@6096c29b
2025-07-06 22:08:31 INFO  [RMI TCP Connection(8)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-06 22:08:36 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] c.f.shop.manager.service.TenantResolverService - [1094020932530266112]:[0] 从缓存中获取租户ID: 10000
2025-07-06 22:08:36 DEBUG [XNIO-1 task-3] [tid::uId::ip::os::browser:] c.f.shop.manager.service.TenantResolverService - [1094020932530266113]:[0] 从缓存中获取租户ID: 10000
2025-07-06 22:08:36 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094020932530266113]:[0] 租户缓存刷新成功: 10000, 延长时间: 7200秒
2025-07-06 22:08:36 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094020932530266112]:[0] 租户缓存刷新成功: 10000, 延长时间: 7200秒
2025-07-06 22:08:36 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094020932530266113]:[0] 租户缓存命中: 10000
2025-07-06 22:08:36 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094020932530266112]:[0] 租户缓存命中: 10000
2025-07-06 22:08:36 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094020932530266113]:[0] 缓存命中: 租户ID=10000, 访问次数=3
2025-07-06 22:08:36 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094020932530266112]:[0] 缓存命中: 租户ID=10000, 访问次数=3
2025-07-06 22:08:36 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094020932530266113]:[0] 设置增强租户上下文: tenantId=10000, tenantName=Fulfillmen
2025-07-06 22:08:36 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094020932530266112]:[0] 设置增强租户上下文: tenantId=10000, tenantName=Fulfillmen
2025-07-06 22:08:36 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094020932530266113]:[0] 过滤器设置当前请求的租户ID: 10000 (URI: /api/home/<USER>
2025-07-06 22:08:36 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094020932530266112]:[0] 过滤器设置当前请求的租户ID: 10000 (URI: /api/home/<USER>
2025-07-06 22:08:36 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094020932530266113]:[0] [RegexCors] 正则规则 http://localhost:[0-9]+ 匹配来源: http://localhost:5173
2025-07-06 22:08:36 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094020932530266112]:[0] [RegexCors] 正则规则 http://localhost:[0-9]+ 匹配来源: http://localhost:5173
2025-07-06 22:08:36 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094020932530266113]:[0] [RegexCors] 正则匹配成功: http://localhost:5173
2025-07-06 22:08:36 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094020932530266112]:[0] [RegexCors] 正则匹配成功: http://localhost:5173
2025-07-06 22:08:36 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.config.satoken.SaExtensionInterceptor - [1094020932530266112]:[0] auth check result: true
2025-07-06 22:08:36 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.config.satoken.SaExtensionInterceptor - [1094020932530266113]:[0] auth check result: true
2025-07-06 22:08:36 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094020932530266112]:[0] [GET] /api/home/<USER>
2025-07-06 22:08:36 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094020932530266113]:[0] [GET] /api/home/<USER>
2025-07-06 22:08:36 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1094020932530266113]:[0] 全局限流检查通过: key=rate_limit:ip:0:0:0:0:0:0:0:1, current=1/100
2025-07-06 22:08:36 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1094020932530266112]:[0] 全局限流检查通过: key=rate_limit:ip:0:0:0:0:0:0:0:1, current=1/100
2025-07-06 22:08:36 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.HomeServiceImpl - [1094020932530266112]:[0] 搜索电子产品（带缓存和同步入库）
2025-07-06 22:08:36 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.HomeServiceImpl - [1094020932530266113]:[0] 加载热门商品列表（带缓存和同步入库）
2025-07-06 22:08:36 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094020932530266113]:[0] 开始获取推荐商品数据，语言: EN, 页码: 1, 每页大小: 20
2025-07-06 22:08:36 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094020932530266113]:[0] 推荐商品缓存获取开始，语言: EN, 页码: 1, 强制刷新: false
2025-07-06 22:08:36 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094020932530266112]:[0] 命中完整缓存, key: search:65d43bf503e3410204fa882720c56e62
2025-07-06 22:08:36 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094020932530266112]:[0] 缓存统计 - Key: search:65d43bf503e3410204fa882720c56e62, Hit: true
2025-07-06 22:08:36 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094020932530266113]:[0] 命中推荐商品缓存, key: recommend:EN:1:20
2025-07-06 22:08:36 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094020932530266113]:[0] 缓存统计 - Key: recommend:EN:1:20, Hit: true
2025-07-06 22:08:36 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.HomeServiceImpl - [1094020932530266112]:[0] 成功搜索电子产品，数量: 10
2025-07-06 22:08:36 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.HomeServiceImpl - [1094020932530266113]:[0] 成功加载热门商品，数量: 20，ID同步状态：正常
2025-07-06 22:08:36 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094020932530266113]:[0] [GET] /api/home/<USER>
2025-07-06 22:08:36 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094020932530266112]:[0] [GET] /api/home/<USER>
2025-07-06 22:08:36 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.fulfillmen.shop.common.context.UserContextHolder - [1094020932530266113]:[0] 清除用户上下文和 MDC 信息
2025-07-06 22:08:36 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.fulfillmen.shop.common.context.UserContextHolder - [1094020932530266112]:[0] 清除用户上下文和 MDC 信息
2025-07-06 22:08:36 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094020932530266112]:[0] 清理增强租户上下文
2025-07-06 22:08:36 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094020932530266113]:[0] 清理增强租户上下文
2025-07-06 22:08:36 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094020932530266112]:[0] 过滤器清理租户上下文完成
2025-07-06 22:08:36 DEBUG [XNIO-1 task-3] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094020932530266113]:[0] 过滤器清理租户上下文完成
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid::uId::ip::os::browser:] c.f.shop.manager.service.TenantResolverService - [1094020966273441792]:[0] 从缓存中获取租户ID: 10000
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094020966273441792]:[0] 租户缓存刷新成功: 10000, 延长时间: 7200秒
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094020966273441792]:[0] 租户缓存命中: 10000
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094020966273441792]:[0] 缓存命中: 租户ID=10000, 访问次数=3
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094020966273441792]:[0] 设置增强租户上下文: tenantId=10000, tenantName=Fulfillmen
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094020966273441792]:[0] 过滤器设置当前请求的租户ID: 10000 (URI: /api/products/715190800408067)
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094020966273441792]:[0] [RegexCors] 正则规则 http://localhost:[0-9]+ 匹配来源: http://localhost:5173
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094020966273441792]:[0] [RegexCors] 正则匹配成功: http://localhost:5173
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.config.satoken.SaExtensionInterceptor - [1094020966273441792]:[0] auth check result: true
2025-07-06 22:08:44 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094020966273441792]:[0] [GET] /api/products/715190800408067
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1094020966273441792]:[0] 全局限流检查通过: key=rate_limit:ip:0:0:0:0:0:0:0:1, current=2/100
2025-07-06 22:08:44 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.controller.ProductController - [1094020966273441792]:[0] 获取商品详情: productId=715190800408067
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.ProductServiceImpl - [1094020966273441792]:[0] 获取商品详情VO: 传入ID=715190800408067
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.ProductServiceImpl - [1094020966273441792]:[0] ID未找到对应SPU记录，将其视为platformProductId: 715190800408067
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.ProductServiceImpl - [1094020966273441792]:[0] 缓存未命中，通过ProductSyncService获取: platformProductId=715190800408067
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.s.impl.ChainBasedProductSyncServiceImpl - [1094020966273441792]:[0] 获取产品数据（超时控制，责任链模式）: platformProductId=715190800408067
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.s.impl.ChainBasedProductSyncServiceImpl - [1094020966273441792]:[0] 获取或同步产品数据（责任链模式）: platformProductId=715190800408067
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.s.sync.chain.core.ProductSyncChainBuilder - [1094020966273441792]:[0] 根据同步类型构建处理器链: STANDARD
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.s.sync.chain.core.ProductSyncChainBuilder - [1094020966273441792]:[0] 构建标准同步处理器链
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.s.sync.chain.core.ProductSyncChainBuilder - [1094020966273441792]:[0] 验证处理器: CacheUpdateHandler
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.s.sync.chain.core.ProductSyncChainBuilder - [1094020966273441792]:[0] 处理器链验证通过，长度: 1
2025-07-06 22:08:44 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.s.sync.chain.core.ProductSyncChainBuilder - [1094020966273441792]:[0] 处理器链: CacheUpdateHandler
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.s.s.chain.core.AbstractProductSyncHandler - [1094020966273441792]:[0] 开始执行处理器: CacheUpdateHandler, platformProductId: 715190800408067
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.s.sync.chain.handlers.CacheUpdateHandler - [1094020966273441792]:[0] 开始更新缓存: platformProductId=715190800408067
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.s.s.chain.core.AbstractProductSyncHandler - [1094020966273441792]:[0] 处理器执行完成: CacheUpdateHandler, 耗时: 1ms, 成功: true
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.s.impl.ChainBasedProductSyncServiceImpl - [1094020966273441792]:[0] 同步成功: platformProductId=715190800408067, 耗时=1ms
2025-07-06 22:08:44 ERROR [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.ProductServiceImpl - [1094020966273441792]:[0] ❌ 无法获取产品信息: platformProductId=715190800408067
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.common.resolver.CompositeLocaleResolver - [1094020966273441792]:[0] Parsed locale string 'en-US' to: en_US
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.common.resolver.CompositeLocaleResolver - [1094020966273441792]:[0] Resolved locale from Accept-Language header: en_US
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] com.fulfillmen.shop.common.util.I18nMessageUtils - [1094020966273441792]:[0] LocaleContextHolder.getLocale() returned: en_US, system default: en_US
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] com.fulfillmen.shop.common.util.I18nMessageUtils - [1094020966273441792]:[0] Using DEFAULT_LOCALE: en_US
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.common.exception.BusinessExceptionI18n - [1094020966273441792]:[0] 创建国际化异常: errorCode=null, i18nKey=validation.product.not.found, locale=en_US, args=[715190800408067]
2025-07-06 22:08:44 WARN  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.ProductServiceImpl - [1094020966273441792]:[0] ❌ 获取商品详情VO失败: platformProductId=715190800408067, 错误码=null, 错误信息=[null] validation.product.not.found (args: [715190800408067])
2025-07-06 22:08:44 WARN  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.c.exception.handler.GlobalExceptionHandler - [1094020966273441792]:[0] Business I18n Exception - Method: GET, Path: /api/products/715190800408067, Message: [null] validation.product.not.found (args: [715190800408067]), ErrorCode: null, I18nKey: validation.product.not.found, Locale: en_US
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.c.exception.handler.GlobalExceptionHandler - [1094020966273441792]:[0] Successfully resolved i18n message: key=validation.product.not.found, locale=en_US, message=Product not found: 715190800408067
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] com.fulfillmen.shop.common.util.I18nMessageUtils - [1094020966273441792]:[0] Cleared current locale context
2025-07-06 22:08:44 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094020966273441792]:[0] [GET] /api/products/715190800408067 200 182ms
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.fulfillmen.shop.common.context.UserContextHolder - [1094020966273441792]:[0] 清除用户上下文和 MDC 信息
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094020966273441792]:[0] 清理增强租户上下文
2025-07-06 22:08:44 DEBUG [XNIO-1 task-3] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094020966273441792]:[0] 过滤器清理租户上下文完成
2025-07-06 22:15:00 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-06 22:08:28,230 to 2025-07-06 22:15:00,003
cache                      |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
categories.                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.             |      0.03|100.00%|            10|            10|             0|             0|        0.0|          0
currency.rate._local       |      0.03| 10.00%|            10|             1|             0|             0|        0.0|          0
currency.rate._remote      |      0.02|100.00%|             9|             9|             0|             0|        0.0|          0
frontend:product:vo:       |      0.00|  0.00%|             1|             0|             0|             0|        0.0|          0
frontend:product:vo:_local |      0.00|  0.00%|             1|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote|      0.00|  0.00%|             1|             0|             0|             0|        0.0|          0
pdc:product:               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:        |      0.01|100.00%|             2|             2|             0|             0|        0.0|          0
pdc:product:search:_local  |      0.01|  0.00%|             2|             0|             0|             0|        0.0|          0
pdc:product:search:_remote |      0.01|100.00%|             2|             2|             0|             0|        0.0|          0
product:detail:            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
product:detail:_local      |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
product:detail:_remote     |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
product:dto:               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
product:dto:_local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
product:dto:_remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

