2025-07-06 22:28:42 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-06 22:28:42 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-06 22:28:42 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-06 22:28:42 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-06 22:28:42 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:28:42 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:28:42 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:28:42 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:28:42 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:28:42 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:28:42 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:28:42 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:28:42 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:28:42 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:28:42 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:28:42 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-06 22:28:42 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-06 22:28:42 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-06 22:28:42 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-06 22:28:42 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Revert Remote [MachineState{machineId=7, lastTimeStamp=1751812122994}] instanceId:[InstanceId{instanceId=************:22608, stable=false}] @ namespace:[fulfillmen-shop].
2025-07-06 22:28:42 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-06 22:28:42 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-06 22:28:42 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-06 22:28:42 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-06 22:28:42 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-06 22:28:43 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-06 22:28:43 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-06 22:28:43 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-06 22:28:43 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
