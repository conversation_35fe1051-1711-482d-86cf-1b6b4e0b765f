# 租户上下文增强 - TenantFilter 集成

## 功能概述

本文档描述了如何在 `fulfillmen-shop` 项目中通过 `TenantFilter` 实现租户上下文增强功能，包括 Redis 缓存集成和完整租户信息的加载。

## 架构设计

### 整体架构

```
HTTP Request → TenantFilter → Cache/Database → Enhanced Context → Business Logic
```

### 核心组件

1. **TenantFilter** - 过滤器级别的租户上下文处理
2. **EnhancedTenantContext** - 增强的租户上下文数据结构
3. **TenantCacheService** - 租户缓存服务（Redis）
4. **TenantDataLoader** - 租户数据加载服务
5. **EnhancedTenantContextHolder** - 线程本地存储

## 关键特性

### 1. 多级缓存架构

- **ThreadLocal**: 当前请求的租户上下文
- **Redis 缓存**: 跨请求的租户信息缓存
- **数据库**: 最终的数据源

### 2. 智能缓存策略

- **默认缓存时间**: 4小时
- **15分钟刷新策略**: 最近15分钟内有访问则延长缓存2小时
- **访问统计**: 记录访问次数和最后访问时间
- **自动清理**: 过期缓存自动清理

### 3. 完整租户信息

增强的租户上下文包含：
- 租户基础信息（用户名、邮箱、状态等）
- 租户详细信息（公司信息、WMS配置等）
- 套餐信息（限制、过期时间等）
- 自定义域名列表
- OSS文件存储路径
- 租户佣金规则
- 仓库列表
- 本地化配置

## 使用方法

### 1. 获取当前租户基本信息

```java
// 获取租户ID
String tenantId = EnhancedTenantContextHolder.getCurrentTenantId();

// 获取租户名称
String tenantName = EnhancedTenantContextHolder.getCurrentTenantName();

// 获取主域名
String primaryDomain = EnhancedTenantContextHolder.getCurrentPrimaryDomain();
```

### 2. 获取租户仓库信息

```java
// 获取默认仓库
EnhancedTenantContext.TenantWarehouseInfo defaultWarehouse =
    EnhancedTenantContextHolder.getCurrentDefaultWarehouse();

if (defaultWarehouse != null) {
    String warehouseName = defaultWarehouse.getName();
    String warehouseCode = defaultWarehouse.getWarehouseCode();
    String address = defaultWarehouse.getAddress();
}
```

### 3. 获取完整租户上下文

```java
Optional<EnhancedTenantContext> contextOpt =
    EnhancedTenantContextHolder.getEnhancedTenantContext();

if (contextOpt.isPresent()) {
    EnhancedTenantContext context = contextOpt.get();

    // 获取基础信息
    EnhancedTenantContext.TenantBasicInfo basicInfo = context.getBasicInfo();

    // 获取套餐信息
    EnhancedTenantContext.TenantPlanInfo planInfo = context.getPlanInfo();

    // 获取仓库列表
    List<EnhancedTenantContext.TenantWarehouseInfo> warehouses = context.getWarehouses();

    // 获取域名列表
    List<EnhancedTenantContext.TenantDomainInfo> domains = context.getDomains();
}
```

### 4. 检查套餐状态

```java
// 检查套餐是否过期
boolean isExpired = EnhancedTenantContextHolder.isCurrentTenantPlanExpired();

if (isExpired) {
    // 处理套餐过期逻辑
    throw new BusinessException("租户套餐已过期");
}
```

### 5. 在业务服务中使用

```java
@Service
public class OrderService {

    public void createOrder(OrderRequest request) {
        // 获取当前租户的默认仓库
        EnhancedTenantContext.TenantWarehouseInfo warehouse =
            EnhancedTenantContextHolder.getCurrentDefaultWarehouse();

        if (warehouse == null) {
            throw new BusinessException("租户未配置默认仓库");
        }

        // 检查套餐是否过期
        if (EnhancedTenantContextHolder.isCurrentTenantPlanExpired()) {
            throw new BusinessException("租户套餐已过期，无法创建订单");
        }

        // 使用仓库信息创建订单
        Order order = new Order();
        order.setWarehouseId(warehouse.getId());
        order.setWarehouseName(warehouse.getName());
        // ... 其他业务逻辑
    }
}
```

## 配置说明

### 1. 过滤器配置

`TenantFilter` 通过 `TenantFilterConfig` 自动注册，无需额外配置。

### 2. 缓存配置

缓存服务使用 `RedisUtils`，确保 Redis 配置正确：

```yaml
spring:
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
```

### 3. 数据库配置

确保相关的租户表结构正确：
- `tenants` - 租户基础信息
- `tenants_info` - 租户详细信息
- `tenant_plans` - 套餐信息
- `tenant_plan_relation` - 租户套餐关系
- `tenant_domains` - 自定义域名
- `tenant_warehouse` - 仓库信息
- `tenant_locales` - 本地化配置

## 性能优化

### 1. 缓存策略

- **缓存预热**: 可以在系统启动时预加载活跃租户的信息
- **缓存穿透**: 对于不存在的租户，也进行短期缓存
- **缓存雪崩**: 随机化缓存过期时间

### 2. 数据库优化

- **索引优化**: 确保租户相关查询有合适的索引
- **连接池**: 合理配置数据库连接池大小
- **只读副本**: 租户信息查询可以使用只读副本

### 3. 内存管理

- **ThreadLocal 清理**: 过滤器保证了 ThreadLocal 的清理
- **缓存大小**: 监控缓存大小，防止内存溢出

## 监控和日志

### 1. 日志记录

```java
// 过滤器会记录以下日志
log.debug("过滤器设置当前请求的租户ID: {} (URI: {})", tenantId, request.getRequestURI());
log.debug("缓存命中: 租户ID={}, 访问次数={}", tenantId, context.getCacheMetadata().getAccessCount());
log.debug("数据库加载成功并缓存: 租户ID={}, 租户名称={}", tenantId, context.getTenantName());
```

### 2. 监控指标

建议监控以下指标：
- 缓存命中率
- 数据库加载耗时
- 租户上下文访问频率
- 缓存大小和过期情况

### 3. 异常处理

系统对异常情况有完善的处理：
- 缓存异常不影响业务流程
- 数据库加载失败时使用基础租户ID功能
- 租户信息不存在时有相应的日志记录

## 最佳实践

### 1. 业务代码中的使用

```java
// 好的实践
Optional<EnhancedTenantContext> contextOpt =
    EnhancedTenantContextHolder.getEnhancedTenantContext();

if (contextOpt.isPresent()) {
    EnhancedTenantContext context = contextOpt.get();
    // 使用 context 进行业务处理
} else {
    // 处理租户上下文不存在的情况
    log.warn("租户上下文不存在，使用默认逻辑");
}
```

### 2. 异常处理

```java
try {
    EnhancedTenantContext.TenantWarehouseInfo warehouse =
        EnhancedTenantContextHolder.getCurrentDefaultWarehouse();

    if (warehouse == null) {
        throw new BusinessException("租户未配置默认仓库");
    }

    // 业务逻辑
} catch (BusinessException e) {
    // 业务异常处理
    throw e;
} catch (Exception e) {
    // 系统异常处理
    log.error("获取租户仓库信息失败", e);
    throw new SystemException("系统异常，请稍后重试");
}
```

### 3. 性能考虑

- 避免在循环中频繁获取租户上下文
- 对于批量操作，一次性获取租户信息
- 考虑使用缓存来减少数据库访问

## 故障排除

### 1. 常见问题

**问题**: 租户上下文为空
**原因**: 可能是租户ID解析失败或数据库中不存在该租户
**解决**: 检查请求头、域名配置和数据库数据

**问题**: 缓存不生效
**原因**: Redis 连接问题或配置错误
**解决**: 检查 Redis 连接和配置，查看相关日志

**问题**: 性能问题
**原因**: 缓存未命中导致频繁数据库查询
**解决**: 检查缓存配置，考虑预热策略

### 2. 调试技巧

- 开启 DEBUG 日志查看租户上下文的设置和清理过程
- 使用 Redis 监控工具查看缓存命中情况
- 检查数据库慢查询日志

## 总结

通过 `TenantFilter` 集成的租户上下文增强功能提供了：

1. **完整的租户信息**: 包含所有必要的业务数据
2. **高效的缓存策略**: 15分钟刷新机制平衡性能和数据一致性
3. **简单的使用方式**: 通过静态方法即可获取租户信息
4. **健壮的异常处理**: 确保系统稳定性
5. **良好的扩展性**: 可以轻松添加新的租户信息类型

这个增强功能为多租户系统提供了强大的基础设施支持，大大简化了业务代码的复杂性。
