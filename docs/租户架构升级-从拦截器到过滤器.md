# 租户架构升级：从拦截器到过滤器

## 概述

本次更新将租户处理机制从 **Spring MVC 拦截器** 升级到 **Servlet 过滤器** 级别，提供更早的请求处理时机和更全面的请求覆盖。

## 变更内容

### 1. 新增组件

#### `TenantFilter` - 租户过滤器
- **路径**: `com.fulfillmen.shop.config.filter.TenantFilter`
- **功能**: 在 Servlet 容器级别处理租户上下文的设置和清理
- **优势**:
  - 执行时机更早，在所有 Spring MVC 组件之前
  - 能处理所有 HTTP 请求，包括静态资源
  - 不依赖于 Spring MVC 的映射机制

#### `TenantFilterConfig` - 过滤器配置
- **路径**: `com.fulfillmen.shop.config.tenant.TenantFilterConfig`
- **功能**: 注册和配置租户过滤器
- **特性**:
  - 设置过滤器执行优先级
  - 配置 URL 模式匹配
  - 管理过滤器生命周期

### 2. 修改内容

#### `FulfillmenWebMvcConfig` - 移除拦截器注册
- 注释掉了 `TenantInterceptor` 的注入和注册
- 避免重复处理租户上下文
- 保持其他拦截器功能不变

#### `TenantInterceptor` - 标记为废弃
- 添加 `@Deprecated` 注解
- 添加文档说明替代方案
- 保留代码以防需要回滚

## 技术优势

### 1. 执行时机更早
```
请求流程：
客户端请求 → Servlet 过滤器 → Spring MVC 拦截器 → Controller
            ↑
           租户过滤器在此处理
```

### 2. 更全面的请求覆盖
- **过滤器**: 处理所有 HTTP 请求
- **拦截器**: 仅处理 Spring MVC 映射的请求

### 3. 独立的异常处理
- 过滤器级别的异常处理更加稳定
- 不会影响其他 Spring MVC 组件

## 配置说明

### 过滤器优先级
```java
registration.setOrder(Ordered.HIGHEST_PRECEDENCE + 10);
```
- 高优先级执行
- 但在一些基础过滤器之后

### 忽略的 URL 模式
```java
private static final Set<String> IGNORED_URL_PATTERNS = new HashSet<>(Arrays.asList(
    "/error", "/error/**",
    "/health", "/health/**",
    "/actuator/**",
    "/swagger-ui/**",
    "/v3/api-docs/**",
    "/webjars/**",
    "/favicon.ico",
    "/static/**",
    "/public/**",
    "/assets/**",
    "/*.js", "/*.css", "/*.png", "/*.jpg", "/*.jpeg", "/*.gif", "/*.ico", "/*.svg"
));
```

## 租户解析逻辑

租户解析逻辑保持不变，仍然使用 `TenantResolverService`：

1. **开发环境检查** - localhost 请求使用默认租户
2. **自定义域名解析** - 查询 `tenant_domains` 表
3. **子域名解析** - 从子域名提取租户标识
4. **请求头获取** - 从 `X-Tenant-Id` 请求头
5. **请求参数获取** - 从 `tenantId` 参数
6. **URL 路径解析** - 从 `/tenant/{tenantId}/` 路径

## 使用方式

### 客户端请求示例

```bash
# 方式1: 使用请求头
curl -H "X-Tenant-Id: 10001" http://localhost:8080/api/products

# 方式2: 使用自定义域名
curl http://shop.nayasource.com/api/products

# 方式3: 使用子域名
curl http://tenant1.example.com/api/products
```

### 日志输出
```
2025-01-03 14:30:00 DEBUG [http-nio-8080-exec-1] c.f.s.c.tenant.TenantFilter [10001] - 过滤器设置当前请求的租户ID: 10001 (URI: /api/products)
2025-01-03 14:30:00 DEBUG [http-nio-8080-exec-1] c.f.s.c.tenant.TenantFilter [] - 过滤器清理租户上下文完成
```

## 兼容性说明

### 向后兼容
- 所有现有的租户解析逻辑保持不变
- 客户端调用方式无需修改
- 数据库设计无需变更

### 代码兼容
- `TenantInterceptor` 类保留但标记为 `@Deprecated`
- 可以在测试期间快速回滚到拦截器模式
- 不影响现有的租户服务和上下文操作

## 性能优化

### 1. 减少重复处理
- 避免拦截器和过滤器重复处理租户上下文
- 减少不必要的 ThreadLocal 操作

### 2. 更精确的路径匹配
- 使用 `AntPathMatcher` 进行精确的路径匹配
- 避免对静态资源的不必要处理

### 3. 异常处理优化
- 过滤器级别的异常处理更加稳定
- 不会影响整个请求处理流程

## 监控和调试

### 日志配置
```yaml
logging:
  level:
    com.fulfillmen.shop.config.filter.TenantFilter: DEBUG
    com.fulfillmen.shop.config.tenant.TenantFilterConfig: DEBUG
```

### 关键监控指标
- 租户 ID 解析成功率
- 过滤器执行时间
- 上下文清理完成率

## 未来规划

1. **完全移除拦截器** - 在确认过滤器稳定后完全移除 `TenantInterceptor`
2. **性能优化** - 进一步优化过滤器的执行效率
3. **功能扩展** - 在过滤器级别添加更多租户相关功能

## 总结

本次升级将租户处理从拦截器级别提升到过滤器级别，提供了：
- ✅ 更早的执行时机
- ✅ 更全面的请求覆盖
- ✅ 更稳定的异常处理
- ✅ 更好的性能表现
- ✅ 完整的向后兼容性

这个架构变更为多租户系统提供了更加稳定和高效的基础设施。
