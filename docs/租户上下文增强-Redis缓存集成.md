# 租户上下文增强 - Redis 缓存集成

## 概述

本次更新为租户上下文添加了 Redis 缓存支持，实现了包含完整租户信息的增强上下文，并提供了智能的缓存刷新策略。

## 主要功能

### 1. 增强的租户上下文数据结构

`EnhancedTenantContext` 包含以下完整的租户信息：

- **租户基础信息** - 用户名、邮箱、状态等
- **租户详细信息** - 公司信息、联系方式、WMS配置等
- **套餐信息** - 当前套餐、限制、过期时间等
- **自定义域名列表** - 主域名、SSL证书等
- **OSS文件存储路径** - 文件存储配置
- **租户佣金规则** - 固定费率或阶梯费率
- **仓库列表** - 仓库信息、默认仓库等
- **本地化配置** - 语言、货币、时区等
- **缓存元数据** - 访问统计、版本控制等

### 2. 智能缓存策略

- **默认缓存时间**: 4小时
- **命中刷新策略**: 15分钟内有访问则延长缓存2小时
- **自动过期清理**: 定时清理过期缓存项
- **访问统计**: 记录访问次数和响应时间

### 3. 多级缓存架构

```
ThreadLocal (当前请求)
    ↓
Redis 缓存 (分布式)
    ↓
内存缓存 (回退方案)
    ↓
数据库查询 (最后手段)
```

## 核心组件

### 1. `EnhancedTenantContext`
完整的租户信息数据类，包含所有租户相关配置。

### 2. `TenantCacheService`
租户缓存服务接口，定义缓存操作规范。

### 3. `DefaultTenantCacheService`
默认内存缓存实现，作为 Redis 的回退方案。

### 4. `DefaultTenantContext`
增强的租户上下文管理器，支持缓存集成。

## 使用方法

### 1. 基础使用

```java
@Service
public class YourBusinessService {

    @Autowired
    private DefaultTenantContext tenantContext;

    public void businessMethod() {
        // 获取当前租户ID
        String tenantId = tenantContext.getCurrentTenantId();

        // 获取完整租户信息
        Optional<EnhancedTenantContext> context = tenantContext.getEnhancedContext();
        if (context.isPresent()) {
            EnhancedTenantContext tenant = context.get();

            // 获取租户基础信息
            String tenantName = tenant.getTenantName();

            // 获取默认仓库
            TenantWarehouseInfo warehouse = tenant.getDefaultWarehouse();

            // 检查套餐是否过期
            boolean expired = tenant.isPlanExpired();

            // 获取主域名
            String domain = tenant.getPrimaryDomain();
        }
    }
}
```

### 2. 缓存管理

```java
@Service
public class TenantManagementService {

    @Autowired
    private DefaultTenantContext tenantContext;

    // 设置租户信息到缓存
    public void cacheTenantInfo(String tenantId, EnhancedTenantContext context) {
        tenantContext.setEnhancedContextToCache(context);
    }

    // 刷新租户缓存
    public void refreshTenantCache(String tenantId, EnhancedTenantContext updatedContext) {
        tenantContext.refreshEnhancedContextCache(updatedContext);
    }

    // 清除租户缓存
    public void clearTenantCache(String tenantId) {
        tenantContext.removeEnhancedContextCache(tenantId);
    }
}
```

### 3. 便捷方法

```java
@Service
public class OrderService {

    @Autowired
    private DefaultTenantContext tenantContext;

    public void createOrder() {
        // 快速获取租户基础信息
        Optional<TenantBasicInfo> basicInfo = tenantContext.getTenantBasicInfo();

        // 快速获取套餐信息
        Optional<TenantPlanInfo> planInfo = tenantContext.getTenantPlanInfo();

        // 快速获取默认仓库
        Optional<TenantWarehouseInfo> warehouse = tenantContext.getDefaultWarehouse();

        // 快速获取主域名
        Optional<String> domain = tenantContext.getPrimaryDomain();

        // 检查套餐是否过期
        boolean planExpired = tenantContext.isPlanExpired();
    }
}
```

### 4. 在过滤器中设置上下文

```java
@Component
public class TenantDataLoader {

    @Autowired
    private TenantService tenantService; // 你的租户数据服务

    @Autowired
    private DefaultTenantContext tenantContext;

    public void loadTenantData(String tenantId) {
        // 首先检查缓存
        Optional<EnhancedTenantContext> cached = tenantContext.getEnhancedContext();
        if (cached.isPresent()) {
            return; // 缓存命中，直接返回
        }

        // 从数据库加载完整租户信息
        EnhancedTenantContext context = buildEnhancedContext(tenantId);

        // 设置到缓存和 ThreadLocal
        tenantContext.setEnhancedContextToCache(context);
    }

    private EnhancedTenantContext buildEnhancedContext(String tenantId) {
        // 从各个表查询数据并组装 EnhancedTenantContext
        return EnhancedTenantContext.builder()
            .basicInfo(loadBasicInfo(tenantId))
            .detailInfo(loadDetailInfo(tenantId))
            .planInfo(loadPlanInfo(tenantId))
            .domains(loadDomains(tenantId))
            .fileStoragePaths(loadFileStoragePaths(tenantId))
            .commissionInfo(loadCommissionInfo(tenantId))
            .warehouses(loadWarehouses(tenantId))
            .localeInfo(loadLocaleInfo(tenantId))
            .build();
    }
}
```

## 配置说明

### 1. Redis 缓存配置

如果项目中已集成 Redis，系统会自动使用 Redis 缓存（需要实现 `redisTenantCacheService`）。

### 2. 内存缓存配置

如果没有 Redis，系统会自动回退到内存缓存：

```java
@Configuration
public class TenantCacheConfig {

    // 可以通过配置调整缓存参数
    @Bean
    @ConditionalOnMissingBean(name = "redisTenantCacheService")
    public TenantCacheService defaultTenantCacheService() {
        return new DefaultTenantCacheService();
    }
}
```

## 缓存策略详解

### 1. 15分钟命中策略

```java
// 如果距离上次访问时间在15分钟内，则延长缓存时间
default boolean shouldRefreshCache(EnhancedTenantContext context) {
    LocalDateTime lastAccess = context.getCacheMetadata().getLastAccessTime();
    Duration timeSinceLastAccess = Duration.between(lastAccess, LocalDateTime.now());
    return timeSinceLastAccess.compareTo(REFRESH_THRESHOLD) <= 0;
}
```

### 2. 缓存生命周期

1. **初始设置**: 缓存4小时
2. **访问检测**: 每次访问检查是否在15分钟内
3. **时间延长**: 符合条件则延长2小时
4. **自动清理**: 定时清理过期条目

### 3. 统计信息

```java
@RestController
public class TenantCacheController {

    @Autowired
    private TenantCacheService cacheService;

    @GetMapping("/tenant/cache/stats")
    public CacheStatistics getCacheStats() {
        return cacheService.getStatistics();
    }
}
```

## 最佳实践

### 1. 缓存预热

在租户登录时预加载租户信息到缓存：

```java
@EventListener
public void onTenantLogin(TenantLoginEvent event) {
    String tenantId = event.getTenantId();
    tenantDataLoader.loadTenantData(tenantId);
}
```

### 2. 数据一致性

当租户信息发生变更时，及时更新缓存：

```java
@Service
public class TenantUpdateService {

    public void updateTenantInfo(String tenantId, TenantUpdateRequest request) {
        // 更新数据库
        tenantRepository.updateTenant(tenantId, request);

        // 刷新缓存
        EnhancedTenantContext updatedContext = buildEnhancedContext(tenantId);
        tenantContext.refreshEnhancedContextCache(updatedContext);
    }
}
```

### 3. 异常处理

优雅处理缓存异常，确保业务不受影响：

```java
public Optional<EnhancedTenantContext> getTenantContextSafely(String tenantId) {
    try {
        return tenantContext.getEnhancedContext();
    } catch (Exception e) {
        log.warn("获取租户缓存失败，回退到数据库查询", e);
        return Optional.ofNullable(loadFromDatabase(tenantId));
    }
}
```

### 4. 监控和告警

监控缓存命中率和响应时间：

```java
@Component
public class CacheMonitor {

    @Scheduled(fixedRate = 60000) // 每分钟检查
    public void monitorCache() {
        CacheStatistics stats = cacheService.getStatistics();

        double errorRate = (double) stats.getTotalErrors() / stats.getTotalOperations();
        if (errorRate > 0.1) { // 错误率超过10%
            // 发送告警
            alertService.sendAlert("租户缓存错误率过高: " + errorRate);
        }
    }
}
```

## 性能优化

### 1. 缓存预加载

- 在系统启动时预加载热点租户数据
- 在租户登录时异步加载租户信息

### 2. 分级缓存

- ThreadLocal: 单请求内快速访问
- Redis: 分布式共享缓存
- 内存: 本地回退缓存

### 3. 批量操作

对于批量操作，可以预加载多个租户的信息：

```java
public void batchProcessOrders(List<String> tenantIds) {
    // 预加载所有租户信息
    Map<String, EnhancedTenantContext> tenantContexts = new HashMap<>();
    for (String tenantId : tenantIds) {
        tenantContexts.put(tenantId, loadTenantContext(tenantId));
    }

    // 批量处理
    for (String tenantId : tenantIds) {
        EnhancedTenantContext context = tenantContexts.get(tenantId);
        processOrder(tenantId, context);
    }
}
```

## 注意事项

1. **内存使用**: 内存缓存会占用JVM堆内存，注意监控内存使用
2. **数据一致性**: 及时清理或更新缓存，避免脏数据
3. **异常处理**: 缓存异常不应影响业务正常流程
4. **安全考虑**: 缓存中的敏感信息要注意保护
5. **容量控制**: 避免缓存无限增长，定期清理过期数据

## 迁移指南

对于现有项目，迁移步骤：

1. **更新依赖**: 确保包含新的租户上下文组件
2. **替换调用**: 将 `tenantContext.getCurrentTenantId()` 替换为增强方法
3. **数据加载**: 实现 `EnhancedTenantContext` 的数据组装逻辑
4. **测试验证**: 验证缓存功能和性能表现
5. **监控告警**: 添加缓存相关的监控指标

---

*此增强功能向后兼容，现有的租户ID获取方式仍然有效。建议逐步迁移到增强上下文以获得更好的性能和功能。*
