<template>
    <div class="bg-gray-50 py-8 min-h-screen">
        <div class="w-full max-w-[85%] mx-auto px-4 lg:px-6 xl:px-8">
            <!-- 步骤指示器 -->
            <CheckoutStepIndicator :current-step="2" />

            <div class="checkout-content">
                <!-- 加载状态 -->
                <div v-if="loading" class="flex justify-center items-center py-12">
                    <div class="loading-spinner"></div>
                    <p class="ml-4 text-gray-600">{{ t('common.loading') }}</p>
                </div>

                <!-- 空状态/无效访问 -->
                <div v-else-if="showEmptyState" class="empty-state-container">
                    <div class="empty-state-content">
                        <i class="fas fa-exclamation-circle empty-state-icon"></i>
                        <h3 class="empty-state-title">{{ t('checkout.invalidAccessTitle') }}</h3>
                        <p class="empty-state-message">{{ t('checkout.invalidAccessMessage') }}</p>
                        <el-button type="primary" @click="router.push('/')" class="empty-state-action">
                            {{ t('common.goShopping') }}
                        </el-button>
                    </div>
                </div>

                <!-- 订单内容 -->
                <template v-else>
                    <!-- 左侧：订单信息 -->
                    <div class="checkout-main">
                        <!-- 数据来源指示 -->
                        <div v-if="false" class="checkout-section mb-4">
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <div class="flex items-center">
                                    <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                                    <span class="text-blue-700 font-medium">{{ t('checkout.orderPreviewMode', '订单预览模式') }}</span>
                                </div>
                                <p class="text-blue-600 text-sm mt-1">{{ t('checkout.orderPreviewDescription', '正在处理您的立即购买订单') }}</p>
                            </div>
                        </div>

                        <!-- 订单预览倒计时 -->
                        <div v-if="isOrderPreview && orderPreviewData" class="checkout-section mb-4">
                            <div
                                :class="[
                                    'border rounded-lg p-4 transition-colors duration-300',
                                    isExpired ? 'bg-red-50 border-red-200' : isExpiringSoon ? 'bg-yellow-50 border-yellow-200' : 'bg-green-50 border-green-200',
                                ]"
                            >
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <i
                                            :class="[
                                                'mr-2',
                                                isExpired
                                                    ? 'fas fa-exclamation-triangle text-red-500'
                                                    : isExpiringSoon
                                                    ? 'fas fa-clock text-yellow-500'
                                                    : 'fas fa-check-circle text-green-500',
                                            ]"
                                        ></i>
                                        <span :class="['font-medium', isExpired ? 'text-red-700' : isExpiringSoon ? 'text-yellow-700' : 'text-green-700']">
                                            {{ isExpired ? t('checkout.previewExpired', '预览已过期') : t('checkout.previewValid', '预览有效') }}
                                        </span>
                                    </div>

                                    <div v-if="!isExpired" class="text-right">
                                        <div :class="['text-lg font-mono font-bold', isExpiringSoon ? 'text-yellow-600' : 'text-green-600']">
                                            {{ formatCountdown(countdown) }}
                                        </div>
                                        <div class="text-xs text-gray-500">{{ t('checkout.remainingTime', '剩余时间') }}</div>
                                    </div>

                                    <div v-else class="text-right">
                                        <div class="text-red-600 font-medium">{{ t('checkout.expired', '已过期') }}</div>
                                        <div class="text-xs text-gray-500">{{ t('checkout.pleaseRefreshPreview', '请刷新预览') }}</div>
                                    </div>
                                </div>

                                <div v-if="isExpired" class="mt-3 pt-3 border-t border-red-200">
                                    <p class="text-red-600 text-sm">{{ t('checkout.expiredMessage', '订单预览已过期，请重新进行预览以获取最新价格和库存信息') }}</p>
                                    <el-button type="danger" size="small" @click="handleRefreshPreview" class="mt-2">
                                        {{ t('checkout.refreshPreview', '刷新预览') }}
                                    </el-button>
                                </div>
                            </div>
                        </div>

                        <!-- 收货地址信息 -->
                        <div v-if="false" class="checkout-section address-section">
                            <h3 class="section-title">{{ t('checkout.shippingAddress') }}</h3>
                            <div class="address-form">
                                <el-form :model="addressForm" label-position="top">
                                    <el-row :gutter="20">
                                        <el-col :span="12">
                                            <el-form-item :label="t('checkout.fullName')" required>
                                                <el-input v-model="addressForm.fullName" :disabled="loading" />
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item :label="t('checkout.phoneNumber')" required>
                                                <el-input v-model="addressForm.phone" :disabled="loading" />
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-form-item :label="t('checkout.address')" required>
                                        <el-input v-model="addressForm.address" :disabled="loading" />
                                    </el-form-item>
                                    <el-row :gutter="20">
                                        <el-col :span="8">
                                            <el-form-item :label="t('checkout.city')" required>
                                                <el-input v-model="addressForm.city" :disabled="loading" />
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item :label="t('checkout.state')" required>
                                                <el-input v-model="addressForm.state" :disabled="loading" />
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item :label="t('checkout.zipCode')" required>
                                                <el-input v-model="addressForm.zipCode" :disabled="loading" />
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </el-form>
                            </div>
                        </div>

                        <!-- 支付方式 -->
                        <div v-if="false" class="checkout-section payment-section">
                            <h3 class="section-title">{{ t('checkout.paymentMethod') }}</h3>
                            <div class="payment-options">
                                <el-radio-group v-model="paymentMethod" :disabled="loading">
                                    <div class="payment-option-card" :class="{ selected: paymentMethod === 'creditCard' }">
                                        <el-radio label="creditCard">{{ t('checkout.creditCard') }}</el-radio>
                                        <img src="@/assets/payment-icons.png" alt="Credit Card" class="payment-icon" />
                                    </div>
                                    <div class="payment-option-card" :class="{ selected: paymentMethod === 'paypal' }">
                                        <el-radio label="paypal">PayPal</el-radio>
                                        <img src="@/assets/payment-icons.png" alt="PayPal" class="payment-icon" />
                                    </div>
                                    <div class="payment-option-card" :class="{ selected: paymentMethod === 'bankTransfer' }">
                                        <el-radio label="bankTransfer">{{ t('checkout.bankTransfer') }}</el-radio>
                                        <img src="@/assets/payment-icons.png" alt="Bank Transfer" class="payment-icon" />
                                    </div>
                                </el-radio-group>
                            </div>
                        </div>

                        <!-- 买家留言 -->
                        <div v-if="isOrderPreview" class="checkout-section order-note-section">
                            <h3 class="section-title">{{ $t('checkout.orderNotes') }}</h3>
                            <div class="order-note-form">
                                <el-input
                                    v-model="buyerMessage"
                                    type="textarea"
                                    :placeholder="t('checkout.orderNotePlaceholder', '请输入您的特殊要求或备注信息')"
                                    :rows="3"
                                    maxlength="200"
                                    show-word-limit
                                    :disabled="loading"
                                />
                            </div>
                        </div>

                        <!-- 订单商品 -->
                        <div class="checkout-section order-items-section">
                            <h3 class="section-title">{{ t('checkout.orderItems') }}</h3>
                            <div class="order-items-grouped">
                                <div v-for="(productGroup, spuId) in groupedProducts" :key="spuId" class="product-group">
                                    <!-- 产品组头部 -->
                                    <div class="product-group-header">
                                        <div class="product-main-info">
                                            <div class="product-main-image">
                                                <img
                                                    :src="getSafeImageUrl(productGroup.mainImage, SMALL_PRODUCT_PLACEHOLDER)"
                                                    :alt="productGroup.productName"
                                                    class="main-image"
                                                    @error="onImageError"
                                                />
                                            </div>
                                            <div class="product-basic-info">
                                                <h4 class="product-name">{{ productGroup.productName }}</h4>
                                                <div class="product-summary">
                                                    <span class="total-quantity">{{ t('checkout.totalQuantity') }}: {{ productGroup.totalQuantity }}</span>
                                                    <span class="total-amount">{{ t('checkout.totalAmount') }}: {{ formatPrice(productGroup.totalAmount) }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- SKU列表 -->
                                    <div class="sku-items-list">
                                        <div v-for="item in productGroup.skuItems" :key="item.skuId" class="sku-item">
                                            <div class="sku-image-container">
                                                <img
                                                    :src="getSafeImageUrl(item.skuImage || item.image, SMALL_PRODUCT_PLACEHOLDER)"
                                                    :alt="item.skuSpecs ? formatItemAttributes(item) : 'SKU'"
                                                    class="sku-image"
                                                    @error="onImageError"
                                                />
                                            </div>
                                            <div class="sku-info">
                                                <p class="sku-specs">{{ formatItemAttributes(item) }}</p>
                                                <div class="sku-details">
                                                    <span class="sku-quantity">{{ t('checkout.quantity') }}: {{ item.quantity }}</span>
                                                    <span class="sku-unit-price">{{ t('checkout.unitPrice') }}: {{ formatPrice(item.price) }}</span>
                                                </div>
                                            </div>
                                            <div class="sku-subtotal">
                                                <span class="subtotal-amount">{{ formatPrice(item.price * item.quantity) }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧：订单摘要 -->
                    <div class="checkout-summary-container">
                        <div class="checkout-summary">
                            <h3 class="summary-title">{{ t('checkout.orderSummary') }}</h3>

                            <div class="summary-content">
                                <div class="summary-row">
                                    <span>{{ t('checkout.subtotal', '商品总额') }}</span>
                                    <span class="summary-value">{{ formatPrice(subtotalAmount) }}</span>
                                </div>

                                <div class="summary-row">
                                    <span>{{ t('checkout.shippingFee', '运费') }}</span>
                                    <span class="summary-value">{{ formatPrice(shippingFee) }}</span>
                                </div>

                                <div class="summary-row">
                                    <span>{{ t('checkout.tax', '服务费') }} (15%)</span>
                                    <span class="summary-value">{{ formatPrice(taxAmount) }}</span>
                                </div>

                                <div class="summary-divider"></div>

                                <div class="summary-row total-row">
                                    <span>{{ t('checkout.total', '订单总额') }}</span>
                                    <span class="summary-value total-value">{{ formatPrice(totalAmount) }}</span>
                                </div>
                            </div>

                            <div class="checkout-actions">
                                <el-button @click="goBack" plain :disabled="loading">
                                    {{ t('common.back', '返回') }}
                                </el-button>
                                <el-button type="primary" @click="placeOrder" :disabled="!canPlaceOrder" :loading="loading">
                                    {{ t('checkout.submitOrder', '提交订单') }}
                                </el-button>
                            </div>

                            <div class="terms-agreement">
                                <el-checkbox v-model="termsAgreed" :disabled="loading">
                                    {{ t('checkout.termsAgreement') }}
                                    <el-link type="primary" @click.prevent>{{ t('checkout.termsAndConditions') }}</el-link>
                                </el-checkbox>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { type OrderPreviewResponse, orderApi } from '@/api/modules/order'
    import CheckoutStepIndicator from '@/components/common/CheckoutStepIndicator.vue'
    import { useCartStore } from '@/stores/cart'
    import { useCurrencyStore } from '@/stores/currency'
    import { useUserStore } from '@/stores/user'
    import { createSmallImageErrorHandler, getSafeImageUrl, SMALL_PRODUCT_PLACEHOLDER } from '@/utils/imageUtils'
    import { formatPriceWithSymbol } from '@/utils/priceUtils'
    import { ElMessage } from 'element-plus'
    import { computed, onMounted, onUnmounted, ref } from 'vue'
    import { useI18n } from 'vue-i18n'
    import { useRoute, useRouter } from 'vue-router'

    const router = useRouter()
    const route = useRoute()
    const cartStore = useCartStore()
    const userStore = useUserStore()
    const currencyStore = useCurrencyStore()
    const { t, locale } = useI18n()

    // 检查当前是否为中文环境
    const isChinese = computed(() => locale.value.startsWith('zh'))

    // 图片错误处理
    const onImageError = createSmallImageErrorHandler()

    // 响应式数据
    const loading = ref(false)
    const orderPreviewData = ref<OrderPreviewResponse | null>(null)
    const paymentMethod = ref('creditCard')
    const termsAgreed = ref(false)
    const buyerMessage = ref('') // 添加买家留言
    const showEmptyState = ref(false)

    // 倒计时相关状态
    const countdown = ref(0) // 剩余秒数
    const countdownTimer = ref<NodeJS.Timeout | null>(null)
    const isExpired = ref(false)
    const isExpiringSoon = ref(false) // 剩余时间少于5分钟时为true

    // 货币相关
    const useUSD = computed(() => currencyStore.getCurrentCurrency === 'USD')

    // 是否为订单预览模式
    const isOrderPreview = computed(() => !!orderPreviewData.value)

    // 费用相关常量
    const defaultShippingFee = ref(10)
    const freeShippingThreshold = ref(100)
    const defaultTaxRate = 0.15

    // 收货地址表单
    const addressForm = ref({
        fullName: '',
        phone: '',
        address: '',
        city: '',
        state: '',
        zipCode: '',
    })

    // 价格格式化函数
    const formatPrice = (price: number): string => {
        return formatPriceWithSymbol(price)
    }

    // 选中的商品列表 - 根据当前模式返回不同数据
    const selectedItems = computed(() => {
        if (orderPreviewData.value) {
            // 订单预览模式：使用预览数据
            return orderPreviewData.value.productItems.map((item: any) => ({
                id: item.skuId,
                skuId: item.skuId,
                spuId: item.spuId,
                name: item.productTitle,
                nameTranslated: item.productTitleEn,
                image: item.productImageUrl,
                skuImage: item.skuImage,
                price: useUSD.value ? item.unitPriceUsd : item.unitPrice,
                priceDisplay: formatPriceWithSymbol(useUSD.value ? item.unitPriceUsd : item.unitPrice),
                quantity: item.orderedQuantity,
                subtotal: useUSD.value ? item.lineTotalAmountUsd : item.lineTotalAmount,
                skuSpecs: item.skuSpecs,
                available: item.available,
                stock: 999, // 预览时不显示库存限制
                minOrderQuantity: 1,
            }))
        }
        return []
    })

    // 按产品分组的商品数据
    const groupedProducts = computed(() => {
        const groups: Record<string, any> = {}

        selectedItems.value.forEach((item: any) => {
            const spuId = item.spuId || item.platformProductId || 'unknown'

            if (!groups[spuId]) {
                groups[spuId] = {
                    spuId,
                    productName: getProductName(item),
                    mainImage: item.image,
                    totalQuantity: 0,
                    totalAmount: 0,
                    skuItems: [],
                }
            }

            groups[spuId].totalQuantity += item.quantity
            groups[spuId].totalAmount += item.price * item.quantity
            groups[spuId].skuItems.push(item)
        })

        return groups
    })

    // 价格相关计算属性
    const subtotalAmount = computed(() =>
        orderPreviewData.value?.priceDetails ? (useUSD.value ? orderPreviewData.value.priceDetails.merchandiseAmountUsd : orderPreviewData.value.priceDetails.merchandiseAmount) : 0
    )
    const shippingFee = computed(() =>
        orderPreviewData.value?.priceDetails ? (useUSD.value ? orderPreviewData.value.priceDetails.shippingAmountUsd : orderPreviewData.value.priceDetails.shippingAmount) : 0
    )
    const taxAmount = computed(() =>
        orderPreviewData.value?.priceDetails ? (useUSD.value ? orderPreviewData.value.priceDetails.serviceFeeUsd : orderPreviewData.value.priceDetails.serviceFee) : 0
    )
    const totalAmount = computed(() =>
        orderPreviewData.value?.priceDetails ? (useUSD.value ? orderPreviewData.value.priceDetails.totalAmountUsd : orderPreviewData.value.priceDetails.totalAmount) : 0
    )

    // 是否可以下单
    const canPlaceOrder = computed(() => {
        // 基本条件：有商品、同意条款、不在加载中、预览未过期
        const basicConditions = selectedItems.value.length > 0 && termsAgreed.value && !loading.value && !isExpired.value
        // 订单预览模式，还需要检查令牌是否有效
        return basicConditions && orderPreviewData.value?.idempotentToken
    })

    // 倒计时相关函数
    const startCountdown = (createTime: string, expiryTime: string) => {
        // 清除现有定时器
        if (countdownTimer.value) {
            clearInterval(countdownTimer.value)
        }

        const calculateRemainingTime = () => {
            const now = new Date().getTime()
            const expiry = new Date(expiryTime).getTime()
            const remaining = Math.max(0, Math.floor((expiry - now) / 1000))

            countdown.value = remaining
            isExpired.value = remaining <= 0
            isExpiringSoon.value = remaining > 0 && remaining <= 5 * 60 // 5分钟内即将过期

            // 如果过期，清除定时器并禁用提交按钮
            if (remaining <= 0) {
                if (countdownTimer.value) {
                    clearInterval(countdownTimer.value)
                    countdownTimer.value = null
                }
            }

            return remaining
        }

        // 立即计算一次
        const remaining = calculateRemainingTime()

        // 如果还没过期，启动定时器
        if (remaining > 0) {
            countdownTimer.value = setInterval(() => {
                calculateRemainingTime()
            }, 1000)
        }
    }

    const formatCountdown = (seconds: number): string => {
        if (seconds <= 0) return '00:00'

        const minutes = Math.floor(seconds / 60)
        const remainingSeconds = seconds % 60
        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
    }

    const handleRefreshPreview = () => {
        // 返回产品详情页重新预览
        goBack()
    }

    // 获取订单预览数据 - 支持两种方式：sessionStorage或后端接口
    const fetchOrderPreview = async (previewId: string) => {
        try {
            loading.value = true

            // 优先从sessionStorage获取预览数据
            const storedData = sessionStorage.getItem(previewId)
            if (storedData) {
                const previewData = JSON.parse(storedData)
                orderPreviewData.value = previewData

                // 启动倒计时
                if (previewData.createTime && previewData.tokenExpiryTime) {
                    startCountdown(previewData.createTime, previewData.tokenExpiryTime)
                }
                return
            }

            // 如果sessionStorage中没有，并且previewId看起来像令牌，尝试从后端获取
            if (previewId.length > 16 && !previewId.startsWith('preview_')) {
                try {
                    const previewData = await orderApi.getPreviewByToken(previewId)
                    orderPreviewData.value = previewData

                    // 启动倒计时
                    if (previewData.createTime && previewData.tokenExpiryTime) {
                        startCountdown(previewData.createTime, previewData.tokenExpiryTime)
                    }
                    return
                } catch (error) {
                    console.warn('从后端获取预览数据失败:', error)
                }
            }

            console.warn('未找到订单预览数据，previewId:', previewId)
            ElMessage.warning(t('checkout.previewDataExpired', '订单预览数据已过期，将使用购物车数据'))
            showEmptyState.value = true
        } catch (error) {
            console.error('获取订单预览失败:', error)
            ElMessage.error(t('checkout.previewDataError', '获取订单预览失败'))
            showEmptyState.value = true
        } finally {
            loading.value = false
        }
    }

    // 在组件挂载时检查数据来源
    onMounted(async () => {
        // 检查用户是否登录
        if (!userStore.isLoggedIn) {
            ElMessage.warning(t('auth.loginRequired'))
            router.push({ path: '/login', query: { redirect: route.fullPath } })
            return
        }

        // 检查是否是订单预览模式
        const previewId = route.query.previewId as string
        const token = route.query.token as string

        if (previewId || token) {
            await fetchOrderPreview(previewId || token)
        } else {
            showEmptyState.value = true
        }
    })

    // 获取商品名称
    const getProductName = (item: any): string => {
        if (!item) return ''
        // 订单预览模式 和 购物车模式 统一处理
        return !isChinese.value ? item.nameTranslated || item.name : item.name
    }

    // 格式化商品属性
    const formatItemAttributes = (item: any): string => {
        const standardText = !isChinese.value ? 'Standard' : '标准款'
        if (!item) return standardText

        // 订单预览模式 - 处理skuSpecs数据
        if (item.skuSpecs && Array.isArray(item.skuSpecs) && item.skuSpecs.length > 0) {
            const validSpecs = item.skuSpecs.filter((spec: any) => spec && spec.attrKey && spec.attrValue)
            if (validSpecs.length > 0) {
                return validSpecs
                    .map((spec: any) => {
                        const key = !isChinese.value ? spec.attrKeyTrans || spec.attrKey : spec.attrKey
                        const value = !isChinese.value ? spec.attrValueTrans || spec.attrValue : spec.attrValue
                        return `${key}: ${value}`
                    })
                    .join(', ')
            }
        }
        return standardText
    }

    // 返回购物车
    const goBack = () => {
        // 订单预览模式返回产品详情页
        const productId = route.query.productId as string
        if (productId) {
            router.push(`/products/${productId}`)
        } else {
            router.push('/')
        }
    }

    // 创建订单
    const placeOrder = async () => {
        if (!canPlaceOrder.value) return

        try {
            loading.value = true

            if (orderPreviewData.value?.idempotentToken) {
                // 订单预览模式：使用令牌提交订单
                const submitData = {
                    idempotentToken: orderPreviewData.value.idempotentToken,
                    productList: orderPreviewData.value.productItems.map(item => ({
                        skuId: item.skuId,
                        productQuantity: item.orderedQuantity,
                    })),
                    buyerMessage: buyerMessage.value || undefined,
                }

                console.log('使用令牌提交订单:', submitData)
                const orderResult = await orderApi.submitOrder(submitData)
                console.log('订单提交结果:', orderResult)

                // 检查订单是否提交成功
                // 新格式：直接检查success字段或purchaseOrderNo字段
                const isSuccess = orderResult?.success === true || (orderResult?.purchaseOrderNo && orderResult?.purchaseOrderId)

                if (isSuccess) {
                    ElMessage.success(orderResult.message || t('checkout.orderPlaced', '订单提交成功'))

                    // 直接保存后端返回的 OrderSubmitResponse 数据到localStorage供OrderSuccessView使用
                    localStorage.setItem('lastOrderSubmit', JSON.stringify(orderResult))

                    // 清理订单预览数据
                    if (route.query.previewId) {
                        sessionStorage.removeItem(route.query.previewId as string)
                    }

                    // 重定向到订单确认页面，传递订单号
                    router.push({
                        path: '/order-success',
                        query: {
                            orderNo: orderResult.purchaseOrderNo,
                            orderId: orderResult.purchaseOrderId?.toString(),
                        },
                    })
                } else {
                    throw new Error(orderResult?.message || '订单提交失败：响应格式错误')
                }
            } else {
                throw new Error('无效的订单会话，请重试')
            }
        } catch (error: any) {
            console.error('订单提交失败:', error)

            // 处理令牌过期的情况
            if (error.message?.includes('TOKEN_EXPIRED') || error.message?.includes('TOKEN_NOT_FOUND')) {
                ElMessage.error(t('checkout.tokenExpired', '订单预览已过期，请重新预览'))
                // 重新跳转到产品详情页
                goBack()
            } else {
                ElMessage.error(error.message || t('checkout.orderFailed', '订单提交失败'))
            }
        } finally {
            loading.value = false
        }
    }

    // 组件销毁时清理定时器
    onUnmounted(() => {
        if (countdownTimer.value) {
            clearInterval(countdownTimer.value)
            countdownTimer.value = null
        }
    })
</script>

<style scoped lang="scss">
    // Loading样式
    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }

    .checkout-page {
        padding: 2rem 0;
        min-height: calc(100vh - 200px);
        background-color: #f8f9fa;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    .checkout-content {
        display: flex;
        gap: 2rem;
        margin-top: 2rem;

        @media (max-width: 1024px) {
            flex-direction: column;
            gap: 1.5rem;
        }
    }

    .checkout-main {
        flex: 1;
        min-width: 0;

        @media (max-width: 768px) {
            width: 100%;
        }
    }

    .checkout-section {
        background: white;
        border-radius: 8px;
        margin-bottom: 1.5rem;
        padding: 1.5rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

        @media (max-width: 768px) {
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: #333;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eee;

            @media (max-width: 768px) {
                font-size: 1.1rem;
                margin-bottom: 1rem;
            }
        }
    }

    .payment-options {
        display: flex;
        flex-direction: column;
        gap: 1rem;

        .payment-option-card {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            border: 1px solid #eee;
            border-radius: 6px;
            transition: all 0.3s ease;

            &:hover {
                border-color: #ccc;
                background-color: #fafafa;
            }

            &.selected {
                border-color: #ffda44;
                background-color: #fffbea;
            }

            .payment-icon {
                width: 40px;
                height: 25px;
                object-fit: contain;
            }
        }
    }

    .order-items {
        .order-item-card {
            display: flex;
            padding: 1rem;
            border-bottom: 1px solid #f0f0f0;

            &:last-child {
                border-bottom: none;
            }

            .item-image {
                width: 60px;
                height: 60px;
                border-radius: 4px;
                overflow: hidden;
                margin-right: 1rem;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            .item-details {
                flex: 1;

                .item-name {
                    font-weight: 600;
                    margin-bottom: 0.25rem;
                    color: #333;
                }

                .item-sku {
                    font-size: 0.813rem;
                    color: #666;
                    margin-bottom: 0.25rem;
                }

                .item-price-qty {
                    font-size: 0.875rem;
                    color: #666;
                }
            }

            .item-total {
                font-weight: 600;
                color: #f56c6c;
                display: flex;
                align-items: center;
            }
        }
    }

    .checkout-summary-container {
        width: 350px;
        flex-shrink: 0;

        @media (max-width: 1024px) {
            width: 100%;
            order: -1;
        }

        @media (max-width: 768px) {
            width: 100%;
        }

        .checkout-summary {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            position: sticky;
            top: 20px;

            @media (max-width: 1024px) {
                position: static;
            }

            @media (max-width: 768px) {
                padding: 1rem;
            }

            .summary-title {
                font-size: 1.25rem;
                font-weight: 600;
                margin-bottom: 1.5rem;
                color: #333;
                padding-bottom: 1rem;
                border-bottom: 1px solid #eee;
            }

            .summary-content {
                margin-bottom: 1.5rem;

                .summary-row {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 1rem;
                    color: #666;

                    &.total-row {
                        font-size: 1.125rem;
                        font-weight: 600;
                        color: #333;
                        margin-bottom: 0;
                    }
                }

                .summary-divider {
                    height: 1px;
                    background-color: #eee;
                    margin: 1rem 0;
                }

                .summary-value {
                    font-weight: 600;
                    color: #666;
                }

                .total-value {
                    color: #f56c6c;
                    font-size: 1.25rem;
                }
            }

            .checkout-actions {
                display: flex;
                gap: 1rem;
                margin-bottom: 1.5rem;

                .el-button {
                    flex: 1;
                }
            }

            .terms-agreement {
                font-size: 0.875rem;
                color: #666;
            }
        }
    }

    /* New Order Item Styles */
    .order-items-grouped {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    .product-group {
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        overflow: hidden;
        background: #ffffff;
    }

    .product-group-header {
        background: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
        padding: 1rem;
    }

    .product-main-info {
        display: flex;
        align-items: center;
        gap: 1rem;

        @media (max-width: 768px) {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.75rem;
        }
    }

    .product-main-image {
        width: 80px;
        height: 80px;
        border-radius: 6px;
        overflow: hidden;
        background-color: #f0f0f0;
        flex-shrink: 0;

        @media (max-width: 768px) {
            width: 60px;
            height: 60px;
        }
    }

    .main-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .product-basic-info {
        flex: 1;
    }

    .product-name {
        font-size: 1.1rem;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 0.5rem;
        line-height: 1.4;
    }

    .product-summary {
        display: flex;
        gap: 1.5rem;
        font-size: 0.9rem;
        color: #6b7280;

        @media (max-width: 768px) {
            flex-direction: column;
            gap: 0.5rem;
            font-size: 0.8rem;
        }
    }

    .total-quantity,
    .total-amount {
        font-weight: 500;
    }

    .sku-items-list {
        padding: 0;
    }

    .sku-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        border-bottom: 1px solid #f3f4f6;

        @media (max-width: 768px) {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.75rem;
            padding: 0.75rem;
        }

        @media (max-width: 480px) {
            padding: 0.5rem;
        }
    }

    .sku-item:last-child {
        border-bottom: none;
    }

    .sku-image-container {
        width: 60px;
        height: 60px;
        border-radius: 4px;
        overflow: hidden;
        background-color: #f0f0f0;
        flex-shrink: 0;

        @media (max-width: 768px) {
            width: 50px;
            height: 50px;
        }
    }

    .sku-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .sku-info {
        flex: 1;
        min-width: 0;

        @media (max-width: 768px) {
            width: 100%;
        }
    }

    .sku-specs {
        font-size: 0.9rem;
        color: #374151;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }

    .sku-details {
        display: flex;
        gap: 1rem;
        font-size: 0.8rem;
        color: #6b7280;

        @media (max-width: 768px) {
            flex-direction: column;
            gap: 0.25rem;
            font-size: 0.75rem;
        }
    }

    .sku-quantity,
    .sku-unit-price {
        font-weight: 500;
    }

    .sku-subtotal {
        text-align: right;
        font-weight: 600;
        color: #1f2937;
        font-size: 1rem;
        min-width: 100px;

        @media (max-width: 768px) {
            text-align: left;
            width: 100%;
            font-size: 0.9rem;
        }
    }

    .subtotal-amount {
        display: block;
    }

    /* 买家留言区域样式 */
    .order-note-section {
        .order-note-form {
            :deep(.el-textarea) {
                .el-textarea__inner {
                    border-radius: 6px;
                    border: 1px solid #e5e7eb;
                    transition: border-color 0.3s ease;
                    font-size: 0.875rem;
                    line-height: 1.5;

                    &:focus {
                        border-color: #3b82f6;
                        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                    }
                }

                .el-input__count {
                    color: #6b7280;
                    font-size: 0.75rem;
                }
            }
        }
    }

    .empty-state-container {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 4rem 1rem;
        text-align: center;
    }

    .empty-state-content {
        max-width: 450px;
    }

    .empty-state-icon {
        font-size: 3rem;
        color: #f56c6c;
        margin-bottom: 1.5rem;
    }

    .empty-state-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0.75rem;
    }

    .empty-state-message {
        color: #666;
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .empty-state-action {
        padding: 0.75rem 2rem;
        font-size: 1rem;
    }
</style>
