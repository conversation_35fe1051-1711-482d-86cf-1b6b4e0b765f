<template>
    <div class="cart-view flex flex-col min-h-screen bg-gray-50 py-8">
        <div class="w-full max-w-[85%] mx-auto px-4 lg:px-6 xl:px-8">
            <CheckoutStepIndicator :current-step="1" />
        </div>

        <!-- 未登录提示 -->
        <div v-if="!userStore.isLoggedIn" class="flex-grow w-full max-w-[85%] mx-auto px-4 lg:px-6 xl:px-8">
            <div class="bg-white rounded-lg p-8 shadow-sm">
                <div class="flex flex-col justify-center items-center py-12 text-center">
                    <div class="mb-6">
                        <el-icon size="64" color="#909399">
                            <Lock />
                        </el-icon>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-700 mb-4">{{ t('cart.loginRequired') }}</h3>
                    <p class="text-gray-500 mb-6">{{ t('cart.loginRequiredDesc') }}</p>
                    <div class="flex gap-4">
                        <el-button type="primary" @click="goToLogin">
                            {{ t('auth.login') }}
                        </el-button>
                        <el-button @click="goToHome">
                            {{ t('common.backToHome') }}
                        </el-button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载状态 -->
        <div v-else-if="isLoading" class="flex-grow w-full max-w-[85%] mx-auto px-4 lg:px-6 xl:px-8">
            <div class="bg-white rounded-lg p-8 shadow-sm">
                <div class="flex justify-center items-center py-12">
                    <el-skeleton :rows="6" animated class="w-full max-w-4xl" />
                </div>
            </div>
        </div>

        <div v-else-if="cartStore.isEmpty" class="flex-grow w-full max-w-[85%] mx-auto px-4 lg:px-6 xl:px-8">
            <CartEmpty @go-to-home="goToHome" />
        </div>

        <!-- 购物车内容 -->
        <div v-else class="flex-grow w-full max-w-[85%] mx-auto px-4 lg:px-6 xl:px-8">
            <div class="flex flex-col bg-white rounded-lg shadow-sm">
                <div class="py-8 flex-grow">
                    <!-- 购物车标题 -->
                    <div class="text-center mb-8">
                        <div class="flex items-center justify-center space-x-3">
                            <div class="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
                                <ShoppingBasket class="w-4 h-4 text-white" />
                            </div>
                            <h1 class="text-2xl font-bold text-gray-900">{{ t('cart.shoppingCart') }}</h1>
                        </div>
                    </div>

                    <!-- 操作栏 -->
                    <div class="sticky top-0 z-40 rounded-lg bg-white/95 p-4 shadow-md backdrop-blur-sm transition-shadow duration-300 mb-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="flex items-center space-x-2">
                                    <div class="relative">
                                        <input type="checkbox" :checked="isAllSelected" @change="handleSelectAllChange" class="sr-only" id="select-all" />
                                        <label for="select-all" class="flex items-center cursor-pointer group">
                                            <div
                                                class="w-5 h-5 rounded-md border-2 flex items-center justify-center transition-all duration-300"
                                                :class="isAllSelected ? 'bg-purple-600 border-purple-600' : 'bg-white border-gray-300 group-hover:border-purple-400'"
                                            >
                                                <Check v-if="isAllSelected" class="w-3.5 h-3.5 text-white stroke-[3]" />
                                            </div>
                                            <span class="ml-3 text-sm font-medium text-gray-700 group-hover:text-purple-600 transition-colors">
                                                {{ t('cart.selectAll') }}
                                            </span>
                                        </label>
                                    </div>
                                </div>
                                <el-input v-model="searchQuery" :placeholder="t('cart.searchPlaceholder')" prefix-icon="el-icon-search" clearable size="small" class="w-64" />
                            </div>

                            <div class="flex items-center space-x-4">
                                <el-button
                                    v-if="userStore.isLoggedIn"
                                    size="small"
                                    :icon="RefreshIcon"
                                    @click="initCartData"
                                    :loading="isLoading"
                                    :disabled="isLoading"
                                    class="!border-purple-200 !text-purple-600 hover:!bg-purple-50"
                                >
                                    {{ t('common.button.refresh') }}
                                </el-button>

                                <div
                                    v-if="selectedItems.length > 0"
                                    class="flex items-center space-x-2 px-3 py-1.5 bg-red-50 text-red-600 rounded-lg cursor-pointer hover:bg-red-100 transition-colors"
                                    @click="removeSelectedItems"
                                >
                                    <Trash2 class="w-4 h-4" />
                                    <span class="text-sm font-medium">{{ t('cart.removeSelected') }} ({{ selectedItems.length }})</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 按商品分组的列表 -->
                    <div class="space-y-6">
                        <div v-for="(productGroup, productId) in filteredGroupedByProduct" :key="productId" class="cart-product-group">
                            <!-- 商品信息头部 -->
                            <div class="p-3 bg-gray-50/50 flex justify-between items-center border-b border-gray-200">
                                <div class="flex items-center gap-4 flex-grow min-w-0">
                                    <input
                                        type="checkbox"
                                        :checked="isProductGroupSelected(productGroup)"
                                        @change="toggleProductGroupSelection(productGroup)"
                                        class="h-5 w-5 rounded text-purple-600 focus:ring-purple-500 border-gray-300 flex-shrink-0"
                                    />
                                    <div class="flex flex-col min-w-0">
                                        <router-link
                                            :to="`/products/${productId}`"
                                            class="text-sm font-semibold text-gray-800 truncate hover:text-purple-600 transition-colors"
                                            :title="getProductDisplayName(productGroup)"
                                        >
                                            {{ getProductDisplayName(productGroup) }}
                                        </router-link>
                                        <span class="text-xs text-gray-500 mt-1">SpuId: {{ productId }}</span>
                                    </div>
                                </div>
                                <div v-if="getSelectedCountInGroup(productGroup) > 0" class="flex items-center space-x-4 text-sm ml-4 flex-shrink-0">
                                    <span class="text-gray-600">已选 {{ getSelectedCountInGroup(productGroup) }} 件</span>
                                    <span class="font-semibold text-gray-800">{{ getSubtotalForGroup(productGroup) }}</span>
                                </div>
                            </div>

                            <!-- SKU列表 -->
                            <div class="divide-y divide-gray-100">
                                <div v-for="item in productGroup.items" :key="item.id" class="product-item" :class="{ selected: item.selected }">
                                    <div class="flex flex-col lg:flex-row gap-4 p-4 relative">
                                        <div
                                            class="absolute left-0 top-0 h-full w-1 bg-purple-600 transition-transform duration-300"
                                            :class="{ 'scale-y-0': !item.selected }"
                                        ></div>
                                        <div class="flex gap-4 flex-grow items-start">
                                            <input
                                                type="checkbox"
                                                v-model="item.selected"
                                                @change="cartStore.updateSelectionState"
                                                class="h-5 w-5 rounded text-purple-600 focus:ring-purple-500 border-gray-300 mt-1 flex-shrink-0"
                                            />
                                            <div class="flex gap-4 flex-grow items-center pl-10">
                                                <div class="w-24 h-24 flex-shrink-0">
                                                    <img
                                                        :src="getSafeImageUrlLocal(item.image, SMALL_PRODUCT_PLACEHOLDER)"
                                                        :alt="getDisplayName(item)"
                                                        class="w-full h-full object-cover rounded"
                                                        @error="onImageError"
                                                    />
                                                </div>
                                                <div class="flex-grow">
                                                    <h3
                                                        class="text-lg font-semibold text-gray-800 leading-tight hover:text-purple-600 cursor-pointer"
                                                        @click="goToProductDetail(item.platformProductId)"
                                                    >
                                                        {{ getDisplayName(item) }}
                                                    </h3>
                                                    <p class="text-base text-gray-600 mt-2">{{ formatProductSpecs(item) }}</p>
                                                    <p class="text-xs text-gray-500 mt-1">SkuId: {{ item.skuId }}</p>

                                                    <!-- 采购量警示语和提示语 -->
                                                    <div class="mt-2 space-y-1">
                                                        <!-- 最小采购量警示 -->
                                                        <div
                                                            v-if="item.minOrderQuantity && item.quantity < item.minOrderQuantity"
                                                            class="flex items-center text-orange-600 text-xs bg-orange-50 px-2 py-1 rounded"
                                                        >
                                                            <svg class="w-3 h-3 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                                                <path
                                                                    fill-rule="evenodd"
                                                                    d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                                                    clip-rule="evenodd"
                                                                ></path>
                                                            </svg>
                                                            <span>{{ t('cart.minOrderWarning', { current: item.quantity, min: item.minOrderQuantity }) }}</span>
                                                        </div>

                                                        <!-- 可以下单提示 -->
                                                        <div
                                                            v-else-if="item.quantity >= (item.minOrderQuantity || 1)"
                                                            class="flex items-center text-green-600 text-xs bg-green-50 px-2 py-1 rounded"
                                                        >
                                                            <svg class="w-3 h-3 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                                                <path
                                                                    fill-rule="evenodd"
                                                                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                                                    clip-rule="evenodd"
                                                                ></path>
                                                            </svg>
                                                            <span>{{ t('cart.readyToOrder') }}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="flex flex-col justify-between items-start md:items-end flex-shrink-0">
                                            <p class="font-semibold text-lg hidden md:block">{{ formatPrice(getCurrentPrice(item)) }}</p>
                                            <div class="flex items-center gap-1 md:gap-2 mt-2 md:mt-0">
                                                <button @click="decreaseQuantity(item)" :disabled="item.quantity <= 1" class="quantity-btn">-</button>
                                                <input
                                                    type="number"
                                                    :value="item.quantity"
                                                    @input="handleQuantityInput(item, $event)"
                                                    @change="handleQuantityChange(item, $event)"
                                                    @blur="handleQuantityBlur(item, $event)"
                                                    class="w-12 h-8 text-center border-x border-gray-200 text-sm font-medium focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                                    min="1"
                                                    max="999"
                                                    :class="{ 'border-red-500': item.quantity < (item.minOrderQuantity || 1) }"
                                                />
                                                <button @click="increaseQuantity(item)" class="quantity-btn">+</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 结算面板 -->
                <div v-if="!cartStore.isEmpty" class="bottom-panel-container">
                    <div class="bottom-panel bg-white rounded-t-xl">
                        <!-- 展开的已选商品与费用明细 -->
                        <div v-if="showSelectedItems" class="selected-items-details p-4 border-b border-gray-200">
                            <div class="max-h-64 overflow-y-auto pr-4">
                                <!-- 费用明细 -->
                                <div class="space-y-2 text-sm mb-4">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">{{ t('cart.subtotal') }}</span>
                                        <span class="font-medium">{{ formatPrice(cartTotalAmount) }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">{{ t('cart.tax') }} 15%</span>
                                        <span class="font-medium">{{ formatPrice(cartTotalAmount * 0.15) }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">{{ t('cart.shippingAndServices') }}</span>
                                        <span class="font-medium text-gray-500">{{ t('cart.calculatedDuringCheckout') }}</span>
                                    </div>
                                </div>
                                <!-- 已选商品列表 -->
                                <div class="space-y-3">
                                    <div v-for="item in selectedItems" :key="item.id" class="flex items-center space-x-3">
                                        <div class="relative flex-shrink-0">
                                            <img :src="item.image" :alt="getDisplayName(item)" class="w-12 h-12 rounded-md object-cover" />
                                            <div class="absolute -top-1.5 -right-1.5 w-5 h-5 bg-purple-600 rounded-full flex items-center justify-center border-2 border-white">
                                                <span class="text-white text-xs font-bold">{{ item.quantity }}</span>
                                            </div>
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <h4 class="text-sm font-medium text-gray-800 line-clamp-1">{{ getDisplayName(item) }}</h4>
                                            <p class="text-xs text-gray-500 line-clamp-1">{{ formatProductSpecs(item) || t('cart.noSpecs') }}</p>
                                        </div>
                                        <span class="text-sm font-semibold text-gray-800 flex-shrink-0">{{ formatPrice(getCurrentPrice(item) * item.quantity) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 主操作栏 -->
                        <div class="operation-container px-4 py-3">
                            <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
                                <!-- 左侧次要操作 -->
                                <div class="flex items-center space-x-6">
                                    <button @click="goToHome" class="flex items-center space-x-2 text-gray-600 hover:text-purple-600 transition-colors text-sm">
                                        <ShoppingBasket class="w-5 h-5" />
                                        <span>{{ t('cart.continueShopping') }}</span>
                                    </button>
                                    <button @click="clearCartConfirm" class="flex items-center space-x-2 text-gray-600 hover:text-red-600 transition-colors text-sm">
                                        <Trash2 class="w-5 h-5" />
                                        <span>{{ t('cart.clearCart') }}</span>
                                    </button>
                                </div>
                                <!-- 右侧主要操作与信息 -->
                                <div class="flex items-center gap-3 md:gap-4">
                                    <div class="text-right">
                                        <div class="flex items-center justify-end gap-2">
                                            <span class="text-lg font-bold text-gray-900">{{ formatPrice(finalTotal) }}</span>
                                            <button @click="toggleSelectedItemsView" class="flex items-center text-sm text-purple-600 hover:text-purple-800">
                                                <span>{{ t('common.details') }}</span>
                                                <svg
                                                    class="w-4 h-4 transform transition-transform duration-300"
                                                    :class="{ 'rotate-180': showSelectedItems }"
                                                    fill="none"
                                                    stroke="currentColor"
                                                    viewBox="0 0 24 24"
                                                >
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                </svg>
                                            </button>
                                        </div>
                                        <p class="text-xs text-gray-500">{{ t('cart.selectedCount', { count: selectedItems.length }) }}</p>
                                    </div>
                                    <el-button
                                        type="primary"
                                        size="large"
                                        @click="handleCheckout"
                                        :disabled="selectedItems.length === 0 || isCheckoutLoading"
                                        :loading="isCheckoutLoading"
                                        class="w-full sm:w-auto min-w-[140px] !text-base !font-bold !bg-purple-600 !border-purple-600 hover:!bg-purple-700 transition-transform hover:scale-105"
                                    >
                                        {{ isCheckoutLoading ? t('cart.processing') : `${t('cart.checkout')} (${selectedItems.length})` }}
                                    </el-button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { orderApi, type OrderPreviewRequest } from '@/api/modules/order'
    import type { CartItem } from '@/stores/cart'
    import { useCartStore } from '@/stores/cart'
    import { useCurrencyStore } from '@/stores/currency'
    import { useUserStore } from '@/stores/user'
    import { createSmallImageErrorHandler, getSafeImageUrl, SMALL_PRODUCT_PLACEHOLDER } from '@/utils/imageUtils'
    import { Lock } from '@element-plus/icons-vue'
    import { ElButton, ElIcon, ElInput, ElMessage, ElMessageBox, ElSkeleton } from 'element-plus'
    import { Check, RefreshCw, ShoppingBasket, Trash2 } from 'lucide-vue-next'
    import { computed, onMounted, ref, watch } from 'vue'
    import { useI18n } from 'vue-i18n'
    import { useRouter } from 'vue-router'

    import CartEmpty from '@/components/cart/CartEmpty.vue'
    import CheckoutStepIndicator from '@/components/common/CheckoutStepIndicator.vue'

    const { t, locale } = useI18n()
    const router = useRouter()
    const cartStore = useCartStore()
    const userStore = useUserStore()
    const currencyStore = useCurrencyStore()
    const RefreshIcon = RefreshCw

    // 加载状态
    const isLoading = ref(false)
    // Checkout加载状态
    const isCheckoutLoading = ref(false)

    // 已选商品展示状态
    const showSelectedItems = ref(false)

    // 监听登录状态变化，当用户登录或登出时刷新购物车数据
    watch(
        () => userStore.isLoggedIn,
        (newValue, oldValue) => {
            if (newValue !== oldValue) {
                // 登录状态变化时刷新数据
                initCartData()
            }
        }
    )

    // 搜索查询
    const searchQuery = ref('')

    // 固定的运费 - 只有在有选中商品时才收取
    const shippingFee = computed(() => {
        return selectedItems.value.length > 0 ? 1.39 : 0
    })

    // 服务费计算（订单总额的6%）- 只有在有选中商品时才收取
    const serviceFeeRate = 0.06 // 6%

    // 计算服务费 - 基于当前货币的总金额
    const serviceFee = computed(() => {
        return selectedItems.value.length > 0 ? cartTotalAmount.value * serviceFeeRate : 0
    })

    // 计算最终总价 - 使用当前货币
    const finalTotal = computed(() => {
        const itemsTotal = selectedItems.value.reduce((sum, item) => {
            return sum + getCurrentPrice(item) * item.quantity
        }, 0)
        return itemsTotal + shippingFee.value + serviceFee.value
    })

    // 获取已选中的商品
    const selectedItems = computed(() => {
        return cartStore.selectedItems || []
    })

    // 计算购物车总金额
    const cartTotalAmount = computed(() => {
        const items = cartStore.selectedItems || []
        return items.reduce((sum, item) => {
            return sum + getCurrentPrice(item) * item.quantity
        }, 0)
    })

    // 是否为中文环境
    const isChineseLocale = computed(() => {
        return locale.value === 'zh-CN' || locale.value === 'zh'
    })

    // 按商品分组
    const filteredGroupedByProduct = computed(() => {
        return cartStore.groupedByProduct
    })

    const getProductDisplayName = (productGroup: { productName: string; productNameEn: string }) => {
        return isChineseLocale.value ? productGroup.productName : productGroup.productNameEn
    }

    const getSelectedItemsInGroup = (productGroup: { items: CartItem[] }) => {
        return productGroup.items.filter(item => item.selected)
    }

    const getSelectedCountInGroup = (productGroup: { items: CartItem[] }) => {
        return getSelectedItemsInGroup(productGroup).length
    }

    const getSubtotalForGroup = (productGroup: { items: CartItem[] }) => {
        const selectedItemsInGroup = getSelectedItemsInGroup(productGroup)
        const subtotal = selectedItemsInGroup.reduce((sum, item) => {
            return sum + getCurrentPrice(item) * item.quantity
        }, 0)
        return formatPrice(subtotal)
    }

    const isProductGroupSelected = (productGroup: { items: CartItem[] }) => {
        return productGroup.items.length > 0 && productGroup.items.every(item => item.selected)
    }

    const toggleProductGroupSelection = (productGroup: { items: CartItem[] }) => {
        const shouldSelect = !isProductGroupSelected(productGroup)
        productGroup.items.forEach(item => {
            item.selected = shouldSelect
        })
        cartStore.updateSelectionState()
    }

    // 计算是否全选
    const isAllSelected = computed({
        get: () => cartStore.isAllSelected || false,
        set: (value: boolean) => {
            cartStore.toggleAllSelection(value)
        },
    })

    // 获取商品显示名称
    const getDisplayName = (item: CartItem) => {
        return isChineseLocale.value ? item.name : item.nameTranslated || item.name
    }

    // 格式化商品规格
    const formatProductSpecs = (item: CartItem) => {
        if (!item.specs?.attr || item.specs.attr.length === 0) {
            return ''
        }

        const specsToDisplay = (isChineseLocale.value ? item.specs.attr : item.specs.attrTransEn || item.specs.attr)
            .filter(spec => spec.attrKey && spec.attrValue) // 过滤掉无效的规格
            .map(spec => `${spec.attrKey}: ${spec.attrValue}`)

        return specsToDisplay.join(' | ')
    }

    // 处理全选/取消全选
    const handleSelectAllChange = async (event: Event) => {
        const target = event.target as HTMLInputElement
        await cartStore.toggleAllSelection(target.checked)
    }

    // 清空购物车确认
    const clearCartConfirm = () => {
        ElMessageBox.confirm(t('cart.clearCartConfirmation'), t('common.warning'), {
            confirmButtonText: t('common.confirm'),
            cancelButtonText: t('common.cancel'),
            type: 'warning',
        })
            .then(() => {
                cartStore.clearCart()
            })
            .catch(() => {})
    }

    // 继续购物
    const goToHome = () => {
        router.push('/')
    }

    // 跳转到登录页面
    const goToLogin = () => {
        router.push('/login')
    }

    // 处理结算
    const handleCheckout = async () => {
        if (cartStore.selectedCount === 0) {
            ElMessage.warning(t('cart.noItemsSelectedPrompt'))
            return
        }

        try {
            isCheckoutLoading.value = true

            // 构建订单预览请求参数
            const orderPreviewRequest: OrderPreviewRequest = {
                productList: selectedItems.value.map(item => ({
                    skuId: Number(item.skuId), // 确保转换为数字类型
                    productQuantity: item.quantity,
                })),
                isShoppingCart: 1,
                shoppingCartIds: selectedItems.value.map(item => item.cartId).join(','),
            }

            console.log('发送订单预览请求:', orderPreviewRequest)

            // 调用订单预览API
            const previewResponse = await orderApi.previewOrder(orderPreviewRequest)

            console.log('订单预览响应:', previewResponse)

            // 检查预览是否成功
            if (!previewResponse.orderPreviewSummary.success) {
                if (previewResponse.errors && previewResponse.errors.length > 0) {
                    const errorMessage = previewResponse.errors[0].errorMessage
                    ElMessage.error(`订单预览失败: ${errorMessage}`)
                } else {
                    ElMessage.error('订单预览失败，请重试')
                }
                return
            }

            // 生成预览数据的唯一ID
            const previewId = `preview_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

            // 将预览数据存储到sessionStorage中
            sessionStorage.setItem(previewId, JSON.stringify(previewResponse))

            // 跳转到结账页面，携带预览ID参数
            router.push({
                path: '/checkout',
                query: {
                    previewId: previewId,
                    source: 'cart',
                    timestamp: Date.now().toString(),
                },
            })
        } catch (error) {
            console.error('订单预览失败:', error)

            // 直接显示后端返回的错误消息
            const errorMessage = error instanceof Error ? error.message : '订单预览失败，请重试'
            ElMessage.error(errorMessage)
        } finally {
            isCheckoutLoading.value = false
        }
    }

    // 移除选中的商品
    const removeSelectedItems = () => {
        if (cartStore.selectedCount === 0) return

        ElMessageBox.confirm(t('cart.removeSelectedConfirmation'), t('common.warning'), {
            confirmButtonText: t('common.confirm'),
            cancelButtonText: t('common.cancel'),
            type: 'warning',
        })
            .then(() => {
                cartStore.removeSelectedItems()
            })
            .catch(() => {})
    }

    // 处理数量减少
    const decreaseQuantity = (item: CartItem) => {
        if (item.quantity > 1) {
            cartStore.updateQuantity(item.productId, item.skuId, item.quantity - 1)
        }
    }

    // 处理数量增加
    const increaseQuantity = (item: CartItem) => {
        cartStore.updateQuantity(item.productId, item.skuId, item.quantity + 1)
    }

    // 处理数量输入 - 实时验证
    const handleQuantityInput = (item: CartItem, event: Event) => {
        const target = event.target as HTMLInputElement
        const value = parseInt(target.value)

        // 防止输入非数字字符
        if (isNaN(value) || value < 1) {
            target.value = item.quantity.toString()
            return
        }

        // 限制最大值
        if (value > 999) {
            target.value = '999'
        }
    }

    // 处理数量变更 - 当用户完成输入时触发
    const handleQuantityChange = (item: CartItem, event: Event) => {
        const target = event.target as HTMLInputElement
        const value = parseInt(target.value)

        if (isNaN(value) || value < 1) {
            target.value = item.quantity.toString()
            return
        }

        const newQuantity = Math.min(Math.max(value, 1), 999)
        cartStore.updateQuantity(item.productId, item.skuId, newQuantity)

        // 实时提示最低采购标准
        showQuantityValidationMessage(item, newQuantity)
    }

    // 处理失去焦点 - 确保数量合法
    const handleQuantityBlur = (item: CartItem, event: Event) => {
        const target = event.target as HTMLInputElement
        const value = parseInt(target.value)

        if (isNaN(value) || value < 1) {
            target.value = item.quantity.toString()
            return
        }

        const newQuantity = Math.min(Math.max(value, 1), 999)
        if (newQuantity !== item.quantity) {
            cartStore.updateQuantity(item.productId, item.skuId, newQuantity)
            showQuantityValidationMessage(item, newQuantity)
        }
    }

    // 显示数量验证消息
    const showQuantityValidationMessage = (item: CartItem, quantity: number) => {
        const minOrderQuantity = item.minOrderQuantity || 1

        if (quantity < minOrderQuantity) {
            ElMessage.warning({
                message: t('cart.minOrderWarning', {
                    current: quantity,
                    min: minOrderQuantity,
                }),
                duration: 3000,
                grouping: true,
                showClose: true,
            })
        } else {
            ElMessage.success({
                message: t('cart.updatedQuantity'),
                duration: 2000,
                grouping: true,
            })
        }
    }

    // 获取当前货币的价格
    const getCurrentPrice = (item: CartItem) => {
        if (currencyStore.currentCurrency === 'USD') {
            return item.originalPrice ? cartStore.getDisplayOriginalPrice(item) || 0 : 0
        }
        return cartStore.getDisplayPrice(item)
    }

    // 格式化价格显示
    const formatPrice = (price: number) => {
        return currencyStore.formatPriceWithSymbol(price)
    }

    // 跳转到商品详情页面
    const goToProductDetail = (productId?: string) => {
        if (productId) {
            router.push(`/products/${productId}`)
        }
    }

    // 切换已选商品展示
    const toggleSelectedItemsView = () => {
        showSelectedItems.value = !showSelectedItems.value
    }

    // 初始化购物车数据
    const initCartData = async () => {
        try {
            if (!userStore.isLoggedIn) {
                return
            }
            isLoading.value = true
            await cartStore.fetchCartList()
        } catch (error) {
            console.error('加载购物车数据失败:', error)
            ElMessage.error(t('cart.fetchFailed'))
        } finally {
            isLoading.value = false
        }
    }

    // 图片相关函数 - 使用160x160的三级降级策略
    const onImageError = createSmallImageErrorHandler()
    const getSafeImageUrlLocal = getSafeImageUrl

    onMounted(() => {
        if (userStore.isLoggedIn) initCartData()
    })
</script>

<style scoped>
    .cart-view {
        background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
    }

    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .line-clamp-1 {
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .cart-product-group {
        @apply overflow-hidden rounded-lg bg-white shadow-sm;
    }

    .product-item {
        @apply transition-colors duration-200;
        position: relative;
    }

    .product-item.selected {
        background-color: #fafaff;
    }

    .quantity-btn {
        @apply w-8 h-8 rounded-md bg-gray-100 text-gray-700 flex items-center justify-center hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-semibold;
    }

    /* 隐藏数字输入框的箭头按钮 */
    input[type='number']::-webkit-outer-spin-button,
    input[type='number']::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    input[type='number'] {
        -moz-appearance: textfield;
    }

    /* 底部结算面板样式 */
    .bottom-panel-container {
        @apply sticky bottom-0 mt-auto z-50 w-full;
        /* mt-auto 会把它推到 flex 容器的底部 */
    }

    .bottom-panel {
        @apply bg-white w-full border-t border-gray-200 shadow-[0_-4px_12px_rgba(0,0,0,0.08)];
        /* 添加一个向上的阴影，增强悬浮感 */
    }

    .selected-items-details {
        animation: slide-in-down 0.4s ease-in-out;
    }

    @keyframes slide-in-down {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    .operation-container {
        padding-left: 50px;
        padding-right: 50px;
        padding-top: 10px;
        padding-bottom: 10px;
        box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.08);
    }
</style>
