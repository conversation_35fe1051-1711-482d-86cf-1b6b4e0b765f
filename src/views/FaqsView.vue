<template>
    <div class="faqs-container">
        <!-- FAQs主要内容部分 -->
        <section>
            <div class="py-48" :style="{ backgroundImage: `url(${customerServiceBg})`, backgroundSize: 'cover', backgroundPosition: 'center' }">
                <div class="container mx-auto">
                    <h2 class="text-6xl font-bold text-white mb-8">{{ t('faqs.title') }}</h2>
                    <p class="text-lg mb-6 text-white/90 max-w-3xl">
                        {{ t('faqs.description') }}
                    </p>
                    <!-- 搜索框 -->
                    <div class="mt-8 max-w-xl">
                        <div class="relative">
                            <svg class="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="11" cy="11" r="8"></circle>
                                <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                            </svg>
                            <input
                                v-model="searchQuery"
                                type="text"
                                :placeholder="t('faqs.searchPlaceholder')"
                                class="w-full pl-12 pr-4 py-3 border border-transparent rounded-full bg-white text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <section class="container mx-auto py-12">
            <!-- 主要内容区域 -->
            <div class="flex gap-8">
                <!-- 左侧导航栏 -->
                <div class="sidebar w-80 flex-shrink-0">
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="space-y-2">
                            <template v-for="navItem in sidebarNav" :key="navItem.id">
                                <div 
                                    class="sidebar-item cursor-pointer p-3 rounded-lg hover:bg-purple-50 transition-colors duration-200"
                                    :class="{ 'bg-purple-100 text-purple-800': activeCategory === navItem.id || (navItem.children && navItem.children.some(c => c.id === activeCategory)) }"
                                    @click="navItem.children ? toggleCategory(navItem.id) : setActiveCategory(navItem.id)"
                                >
                                    <div class="flex items-center">
                                        <div v-html="navItem.icon" class="h-5 w-5 mr-3"></div>
                                        {{ t(navItem.titleKey) }}
                                        <template v-if="navItem.children">
                                            <svg v-if="isCategoryOpen(navItem.id)" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 ml-auto">
                                                <path d="M6 9l6 6 6-6"/>
                                            </svg>
                                            <svg v-else xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 ml-auto">
                                                <path d="m9 18 6-6-6-6"/>
                                            </svg>
                                        </template>
                                    </div>
                                </div>
                                <div v-if="navItem.children && isCategoryOpen(navItem.id)" class="ml-5">
                                    <div 
                                        v-for="child in navItem.children" :key="child.id"
                                        class="sidebar-item cursor-pointer p-3 rounded-lg hover:bg-purple-50 transition-colors duration-200"
                                        :class="{ 'bg-purple-100 text-purple-800': activeCategory === child.id }"
                                        @click="setActiveCategory(child.id)"
                                    >
                                        <div class="flex items-center">
                                            {{ t(child.titleKey) }}
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>

                <!-- 右侧内容展示区域 -->
                <div class="flex-1">
                    <div class="p-8">
                        <!-- 问题列表 -->
                        <div v-if="!selectedQuestion">
                            <!-- 搜索结果 -->
                            <div v-if="searchQuery" class="content-section">
                                <h3 class="text-2xl font-bold mb-6">
                                    {{ t('faqs.searchResults', { count: searchResults.length }) }}
                                </h3>
                                <div class="space-y-4">
                                    <div v-if="searchResults.length === 0" class="text-center py-12 text-gray-500">
                                        <p>{{ t('faqs.noResultsFound') }}</p>
                                    </div>
                                    <div v-for="item in searchResults" :key="item.id" class="faq-item p-9 border-b border-dashed border-black/20 cursor-pointer hover:bg-purple-50" @click="selectQuestion(item.id)">
                                        <div class="flex items-center justify-between">
                                            <h3 class="text-xl font-semibold text-gray-800">{{ t(item.titleKey) }}</h3>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6 text-gray-400"><polyline points="9 18 15 12 9 6"></polyline></svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 分类内容 -->
                            <template v-else>
                                <!-- 账户管理 -->
                                <div v-if="activeCategory === 'account-register' || activeCategory === 'account-security' || activeCategory === 'account-payment'" class="content-section">
                                    <div class="space-y-4">
                                        <div v-for="item in getFaqsByCategory(activeCategory)" :key="item.id" class="faq-item p-9 border-b border-dashed border-black/20 cursor-pointer hover:bg-purple-50" @click="selectQuestion(item.id)">
                                            <div class="flex items-center justify-between">
                                                <h3 class="text-xl font-semibold text-gray-800">{{t(item.titleKey)}}</h3>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6 text-gray-400"><polyline points="9 18 15 12 9 6"></polyline></svg>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- 代购服务 -->
                                <div v-if="activeCategory === 'purchasing-guide' || activeCategory === 'purchasing-issues'" class="content-section">
                                    <div class="space-y-4">
                                        <div v-for="item in getFaqsByCategory(activeCategory)" :key="item.id" class="faq-item p-9 border-b border-dashed border-black/20 cursor-pointer hover:bg-purple-50" @click="selectQuestion(item.id)">
                                            <div class="flex items-center justify-between">
                                                <h3 class="text-xl font-semibold text-gray-800">{{t(item.titleKey)}}</h3>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6 text-gray-400"><polyline points="9 18 15 12 9 6"></polyline></svg>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 物流配送 -->
                                <div v-if="activeCategory === 'shipping-guide' || activeCategory === 'shipping-issues'" class="content-section">
                                    <div class="space-y-4">
                                        <div v-for="item in getFaqsByCategory(activeCategory)" :key="item.id" class="faq-item p-9 border-b border-dashed border-black/20 cursor-pointer hover:bg-purple-50" @click="selectQuestion(item.id)">
                                            <div class="flex items-center justify-between">
                                                <h3 class="text-xl font-semibold text-gray-800">{{t(item.titleKey)}}</h3>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6 text-gray-400"><polyline points="9 18 15 12 9 6"></polyline></svg>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 转运服务 -->
                                <div v-if="activeCategory === 'transport-guide' || activeCategory === 'transport-issues'" class="content-section">
                                    <div class="space-y-4">
                                        <div v-for="item in getFaqsByCategory(activeCategory)" :key="item.id" class="faq-item p-9 border-b border-dashed border-black/20 cursor-pointer hover:bg-purple-50" @click="selectQuestion(item.id)">
                                            <div class="flex items-center justify-between">
                                                <h3 class="text-xl font-semibold text-gray-800">{{t(item.titleKey)}}</h3>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6 text-gray-400"><polyline points="9 18 15 12 9 6"></polyline></svg>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 客户服务 -->
                                <div v-if="activeCategory === 'service-guide' || activeCategory === 'service-issues'" class="content-section">
                                    <div class="space-y-4">
                                        <div v-for="item in getFaqsByCategory(activeCategory)" :key="item.id" class="faq-item p-9 border-b border-dashed border-black/20 cursor-pointer hover:bg-purple-50" @click="selectQuestion(item.id)">
                                            <div class="flex items-center justify-between">
                                                <h3 class="text-xl font-semibold text-gray-800">{{t(item.titleKey)}}</h3>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6 text-gray-400"><polyline points="9 18 15 12 9 6"></polyline></svg>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>

                        <!-- 问题详情 -->
                        <div v-else>
                            <!-- 面包屑导航 -->
                            <div class="flex items-center mb-4">
                                <button @click="router.push({ name: 'home' })" class="p-2 rounded-full text-gray-500 hover:bg-gray-100 hover:text-purple-800 transition-colors" :title="t('faqs.home')">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                                        <polyline points="9 22 9 12 15 12 15 22"></polyline>
                                    </svg>
                                </button>
                                <span class="mx-3 text-gray-300">|</span>
                                <div class="breadcrumb text-sm text-gray-500 flex items-center">
                                    <template v-for="(crumb, index) in breadcrumbs" :key="index">
                                        <span 
                                            :class="{ 
                                                'cursor-pointer hover:text-purple-800 transition-colors': crumb.isLink,
                                                'font-semibold text-gray-700 truncate': !crumb.isLink
                                            }"
                                            @click="crumb.action"
                                        >
                                            {{ crumb.text }}
                                        </span>
                                        <span v-if="index < breadcrumbs.length - 1" class="mx-2">/</span>
                                    </template>
                                </div>
                            </div>
                            <button @click="goBack" class="mb-8 flex items-center text-purple-800 font-semibold hover:text-purple-600 transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 mr-2"><line x1="19" y1="12" x2="5" y2="12"></line><polyline points="12 19 5 12 12 5"></polyline></svg>
                                {{ t('faqs.backToList') }}
                            </button>

                            <!-- 动态内容渲染区 -->
                            <div v-if="dynamicContentHtml" class="prose max-w-none" v-html="dynamicContentHtml"></div>

                            <!-- 静态内容详情 (所有非动态内容都在这里) -->
                            <template v-if="!isDynamicQuestion(selectedQuestion)">
                                <!-- 常见问题详情 -->
                                <div v-if="selectedQuestion === 'account-0'">
                                    <P>{{t('faqs.accountAnswer1')}}</P>
                                    <img :src="getLocalizedImagePath('clickRegister.png')" alt="clickRegister" class="w-full rounded-lg shadow-md">
                                    <img :src="getLocalizedImagePath('register.png')" alt="register" class="w-full rounded-lg shadow-md">
                                </div>
                                <div v-if="selectedQuestion === 'account-1'">
                                    <div class="space-y-4 text-gray-600 leading-relaxed">
                                        <p>{{t('faqs.accountAnswer2')}}</p>
                                        <img :src="getLocalizedImagePath('joinUserProfile.png')" alt="joinUserProfile" class="w-full rounded-lg shadow-md">
                                        <img :src="getLocalizedImagePath('changePassword.png')" alt="changePassword" class="w-full rounded-lg shadow-md">
                                    </div>
                                </div>
                                <div v-if="selectedQuestion === 'account-2'">
                                    <div class="space-y-4 text-gray-600 leading-relaxed">
                                        <p>{{t('faqs.accountAnswer3')}}</p>
                                    </div>
                                </div>
                                <div v-if="selectedQuestion === 'account-4'">
                                    <p>{{t('faqs.accountAnswer4')}}</p>
                                    <img :src="getLocalizedImagePath('userProfile.png')" alt="userProfile" class="w-full rounded-lg shadow-md">
                                </div>
                                <div v-if="selectedQuestion === 'account-payment-0'">
                                    <p class="text-gray-600 leading-relaxed">{{t('faqs.paymentAnswer1')}}</p>
                                </div>
                                <div v-if="selectedQuestion === 'account-payment-1'">
                                    <p class="text-gray-600 leading-relaxed">{{t('faqs.paymentAnswer2')}}</p>
                                </div>
                                <div v-if="selectedQuestion === 'account-payment-2'">
                                    <p class="text-gray-600 leading-relaxed">{{t('faqs.paymentAnswer3')}}</p>
                                </div>

                                <!-- 代购服务详情 -->
                                

                                <!-- 物流配送详情 -->
                                <div v-if="selectedQuestion === 'shipping-0'">
                                    <p class="text-gray-600 leading-relaxed">{{t('faqs.shippingAnswer1')}}</p>
                                </div>
                                <div v-if="selectedQuestion === 'shipping-1'">
                                    <p class="text-gray-600 leading-relaxed">{{t('faqs.shippingAnswer2')}}</p>
                                </div>
                                <div v-if="selectedQuestion === 'shipping-2'">
                                    <p class="text-gray-600 leading-relaxed">{{t('faqs.shippingAnswer3')}}</p>
                                </div>
                                 
                                <!-- 转运服务详情 -->
                                <div v-if="selectedQuestion === 'transport-0'">
                                    <p class="text-gray-600 leading-relaxed">{{t('faqs.transportAnswer1')}}</p>
                                </div>
 
                                <!-- 客户服务详情 -->
                                <div v-if="selectedQuestion === 'service-0'">
                                    <p class="text-gray-600 leading-relaxed">{{t('faqs.customerAnswer1')}}</p>
                                </div>
                                <div v-if="selectedQuestion === 'service-1'">
                                    <p class="text-gray-600 leading-relaxed">{{t('faqs.customerAnswer2')}}</p>
                                </div>
                                <div v-if="selectedQuestion === 'service-2'">
                                    <p class="text-gray-600 leading-relaxed">{{t('faqs.customerAnswer3')}}</p>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 订阅部分 -->
        <section class="subscribe-section">
            <div class="container relative z-10">
                <div class="lg:w-1/2">
                    <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">{{ t('faqs.getVoucher') }}</h2>
                    <p class="text-lg text-white opacity-90 mb-8">{{ t('faqs.emailStart') }}</p>

                    <button class="subscribe-button">
                        {{ t('faqs.getStarted') }}
                    </button>
                </div>
            </div>
        </section>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { marked } from 'marked'
import customerServiceBg from '@/assets/faqs/customerService.png'

const { t, locale } = useI18n()
const router = useRouter()

const activeCategory = ref('account-register')
const selectedQuestion = ref<string | null>(null)
const dynamicContentHtml = ref('')
const openCategories = ref<string[]>(['account'])
const searchQuery = ref('')

const sidebarNav = [
    {
        id: 'account',
        titleKey: 'faqs.account',
        icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>',
        children: [
            { id: 'account-register', titleKey: 'faqs.accountRegister' },
            { id: 'account-security', titleKey: 'faqs.accountSecurity' },
            { id: 'account-payment', titleKey: 'faqs.accountPayment' },
        ]
    },
    {
        id: 'purchasing',
        titleKey: 'faqs.purchasing',
        icon: '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><rect width="20" height="14" x="2" y="5" rx="2"/><line x1="2" x2="22" y1="10" y2="10"/></svg>',
        children: [
            { id: 'purchasing-guide', titleKey: 'faqs.purchasingGuide' },
            { id: 'purchasing-issues', titleKey: 'faqs.purchasingIssues' },
        ]
    },
    {
        id: 'shipping',
        titleKey: 'faqs.shipping',
        icon: '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/></svg>',
        children: [
            { id: 'shipping-guide', titleKey: 'faqs.shippingGuide' },
            { id: 'shipping-issues', titleKey: 'faqs.shippingIssues' },
        ]
    },
    {
        id: 'transport',
        titleKey: 'faqs.transport',
        icon: '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 3 4 7l4 4M4 7h16m-4 14 4-4-4-4M20 17H4"/></svg>',
        children: [
            { id: 'transport-guide', titleKey: 'faqs.transportGuide' },
            { id: 'transport-issues', titleKey: 'faqs.transportIssues' },
        ]
    },
    {
        id: 'service',
        titleKey: 'faqs.service',
        icon: '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="12" r="4"></circle><line x1="4.93" y1="4.93" x2="9.17" y2="9.17"></line><line x1="14.83" y1="14.83" x2="19.07" y2="19.07"></line><line x1="14.83" y1="9.17" x2="19.07" y2="4.93"></line><line x1="4.93" y1="19.07" x2="9.17" y2="14.83"></line></svg>',
        children: [
            { id: 'service-guide', titleKey: 'faqs.serviceGuide' },
            { id: 'service-issues', titleKey: 'faqs.serviceIssues' },
        ]
    }
];

const dynamicContentConfig: Record<string, any[]> = {
  'account-5': [
    { type: 'title', key: 'faqs.accountAnswer6' },
    { type: 'markdown', file: 'userCodeOfConduct.md' }
  ],
  'transport-1': [
    { type: 'markdown', titleKey: 'faqs.transportDocTitle1', file: 'forwardingPackages.md' },
    { type: 'markdown', titleKey: 'faqs.transportDocTitle2', file: 'no1688forwardingPackages.md' },
    { type: 'image', titleKey: 'faqs.transportDocTitle3', src: '1688ProductForwardingRules.png' }
  ],
  'transport-2': [
    { type: 'markdown', titleKey: 'faqs.transportDocTitle4', file: 'packageForwardServiceAgreement.md' }
  ],
  'purchasing-0': [
    { type: 'markdown', titleKey: 'faqs.purchasingDocTitle1', file: 'purchasingAnswer1.md' }
  ],
  'purchasing-1': [
    { type: 'markdown', file: 'purchasingAnswer2.md' }
  ],
  'purchasing-2': [
    { type: 'markdown', titleKey: 'faqs.purchasingDocTitle2', file: 'purchasingAnswer3.md' }
  ],
  'purchasing-3': [
    { type: 'markdown', file: 'purchasingAnswer4.md' }
  ],
  'purchasing-4': [
    { type: 'markdown', file: 'purchasingAnswer5.md' }
  ],
  'purchasing-5': [
    { type: 'markdown', file: 'purchasingAnswer6.md' }
  ],
  'purchasing-6': [
    { type: 'markdown', file: 'purchasingAnswer7.md' }
  ],
  'purchasing-7': [
    { type: 'markdown', file: 'purchasingAnswer8.md' }
  ],
  'purchasing-8': [
    { type: 'markdown', file: 'purchasingAnswer9.md' }
  ],
  'purchasing-9': [
    { type: 'markdown', file: 'purchasingAnswer10.md' }
  ],
  'purchasing-10': [
    { type: 'markdown', file: 'purchasingAnswer11.md' }
  ],
  'purchasing-11': [
    { type: 'markdown', file: 'purchasingAnswer12.md' }
  ]
};

const faqItems = [
  // 账户管理
  { id: 'account-0', category: 'account-register', titleKey: 'faqs.accountIssues1', answerKey: 'faqs.accountAnswer1', static: true },
  { id: 'account-4', category: 'account-register', titleKey: 'faqs.accountIssues4', answerKey: 'faqs.accountAnswer4', static: true },
  { id: 'account-1', category: 'account-security', titleKey: 'faqs.accountIssues2', answerKey: 'faqs.accountAnswer2', static: true },
  { id: 'account-2', category: 'account-security', titleKey: 'faqs.accountIssues3', answerKey: 'faqs.accountAnswer3', static: true },
  { id: 'account-5', category: 'account-register', titleKey: 'faqs.accountIssues6', dynamic: true },
  { id: 'account-payment-0', category: 'account-payment', titleKey: 'faqs.paymentIssues1', answerKey: 'faqs.paymentAnswer1', static: true },
  { id: 'account-payment-1', category: 'account-payment', titleKey: 'faqs.paymentIssues2', answerKey: 'faqs.paymentAnswer2', static: true },
  { id: 'account-payment-2', category: 'account-payment', titleKey: 'faqs.paymentIssues3', answerKey: 'faqs.paymentAnswer3', static: true },
  
  // 代购服务
  { id: 'purchasing-0', category: 'purchasing-guide', titleKey: 'faqs.purchasingIssues1', dynamic: true },
  { id: 'purchasing-1', category: 'purchasing-guide', titleKey: 'faqs.purchasingIssues2', dynamic: true },
  { id: 'purchasing-2', category: 'purchasing-guide', titleKey: 'faqs.purchasingIssues3', dynamic: true },
  { id: 'purchasing-3', category: 'purchasing-guide', titleKey: 'faqs.purchasingIssues4', dynamic: true },
  { id: 'purchasing-4', category: 'purchasing-guide', titleKey: 'faqs.purchasingIssues5', dynamic: true },
  { id: 'purchasing-5', category: 'purchasing-issues', titleKey: 'faqs.purchasingIssues6', dynamic: true },
  { id: 'purchasing-6', category: 'purchasing-issues', titleKey: 'faqs.purchasingIssues7', dynamic: true },
  { id: 'purchasing-7', category: 'purchasing-issues', titleKey: 'faqs.purchasingIssues8', dynamic: true },
  { id: 'purchasing-8', category: 'purchasing-issues', titleKey: 'faqs.purchasingIssues9', dynamic: true },
  { id: 'purchasing-9', category: 'purchasing-issues', titleKey: 'faqs.purchasingIssues10', dynamic: true },
  { id: 'purchasing-10', category: 'purchasing-issues', titleKey: 'faqs.purchasingIssues11', dynamic: true },
  { id: 'purchasing-11', category: 'purchasing-issues', titleKey: 'faqs.purchasingIssues12', dynamic: true },

  // 物流配送
  { id: 'shipping-0', category: 'shipping-guide', titleKey: 'faqs.shippingIssues1', answerKey: 'faqs.shippingAnswer1', static: true },
  { id: 'shipping-1', category: 'shipping-guide', titleKey: 'faqs.shippingIssues2', answerKey: 'faqs.shippingAnswer2', static: true },
  { id: 'shipping-2', category: 'shipping-guide', titleKey: 'faqs.shippingIssues3', answerKey: 'faqs.shippingAnswer3', static: true },
  
  // 转运
  { id: 'transport-0', category: 'transport-guide', titleKey: 'faqs.transportIssues1', answerKey: 'faqs.transportAnswer1', static: true },
  { id: 'transport-1', category: 'transport-guide', titleKey: 'faqs.transportIssues2', dynamic: true },
  { id: 'transport-2', category: 'transport-guide', titleKey: 'faqs.transportIssues3', dynamic: true },

  // 客户服务
  { id: 'service-0', category: 'service-guide', titleKey: 'faqs.customerIssues1', answerKey: 'faqs.customerAnswer1', static: true },
  { id: 'service-1', category: 'service-guide', titleKey: 'faqs.customerIssues2', answerKey: 'faqs.customerAnswer2', static: true },
  { id: 'service-2', category: 'service-guide', titleKey: 'faqs.customerIssues3', answerKey: 'faqs.customerAnswer3', static: true },
];

const searchResults = computed(() => {
  if (!searchQuery.value) return [];
  const query = searchQuery.value.toLowerCase();
  return faqItems.filter(item => {
    const title = t(item.titleKey).toLowerCase();
    return title.includes(query);
  });
});

const getFaqsByCategory = (category: string) => {
  if (category === 'account-issues') {
      // 这是一个示例，如果"账户问题"需要展示所有账户相关的，可以在这里聚合
      return faqItems.filter(item => item.category.startsWith('account'));
  }
  return faqItems.filter(item => item.category === category)
}

const isDynamicQuestion = (questionId: string | null) => {
    return questionId ? Object.keys(dynamicContentConfig).includes(questionId) : false;
}

// 2. 模块加载器
const docModules = import.meta.glob('/src/assets/faqs/docs/**/*.md', { as: 'raw' })

// 3. 通用渲染函数
async function renderDynamicContent(sections: any[]) {
    let combinedHtml = '';
    for (const section of sections) {
        if (section.type === 'title' && section.key) {
             combinedHtml += `<h3 class="text-3xl font-bold text-gray-800 mb-6">${t(section.key)}</h3>`;
        } else if (section.type === 'markdown' && section.file) {
            if(section.titleKey) {
                combinedHtml += `<h4 class="text-2xl font-bold text-gray-800 mt-8 mb-4 border-t pt-6 first:mt-0 first:border-t-0">${t(section.titleKey)}</h4>`;
            }
            const path = `/src/assets/faqs/docs/${locale.value}/${section.file}`
            const moduleImporter = docModules[path]
            if (moduleImporter) {
                try {
                    const mdContent = await moduleImporter()
                    combinedHtml += await marked(mdContent)
                } catch (e) {
                    console.error(`Failed to load markdown: ${path}`, e)
                    combinedHtml += `<p>Error rendering content for ${section.file}.</p>`
                }
            } else {
                console.error(`Markdown module not found: ${path}`)
                combinedHtml += `<p>Content not available for ${section.file}.</p>`
            }
        } else if (section.type === 'image' && section.src) {
            if (section.titleKey) {
                combinedHtml += `<h4 class="text-2xl font-bold text-gray-800 mt-8 mb-4 border-t pt-6">${t(section.titleKey)}</h4>`;
            }
            const imageUrl = new URL(`../assets/faqs/imgs/${locale.value}/${section.src}`, import.meta.url).href
            combinedHtml += `<img src="${imageUrl}" alt="${section.src || ''}" class="w-full rounded-lg shadow-md">`;
        }
    }
    return combinedHtml;
}

// 4. 简化的 watch 函数
watch([selectedQuestion, locale], async ([newVal]) => {
    if (newVal && isDynamicQuestion(newVal)) {
        dynamicContentHtml.value = await renderDynamicContent(dynamicContentConfig[newVal]);
    } else {
        dynamicContentHtml.value = ''; // 清空
    }
}, { immediate: true })

// 动态获取国际化图片路径的辅助函数
const getLocalizedImagePath = (imageName: string) => {
  return new URL(`../assets/faqs/imgs/${locale.value}/${imageName}`, import.meta.url).href
}

const currentQuestionTitle = computed(() => {
    if (!selectedQuestion.value) return ''
    
    const item = faqItems.find(i => i.id === selectedQuestion.value)
    return item ? t(item.titleKey) : ''
})

// 面包屑导航数据驱动
const breadcrumbs = computed(() => {
  const list: { text: string; action?: () => void; isLink: boolean }[] = [{ text: t('faqs.title'), action: goBack, isLink: true }];
  if (selectedQuestion.value) {
    
    const item = faqItems.find(i => i.id === selectedQuestion.value);
    if(item) {
        const parentNav = sidebarNav.find(nav => nav.id === item.category || (nav.children && nav.children.some(c => c.id === item.category)));
        if(parentNav) {
            list.push({ text: t(parentNav.titleKey), action: goBack, isLink: true });
        }
    }

    list.push({ text: currentQuestionTitle.value, isLink: false });
  }
  return list;
});

const selectQuestion = (id: string) => {
    selectedQuestion.value = id
}

const goBack = () => {
    selectedQuestion.value = null
}

const toggleCategory = (category: string) => {
    if (openCategories.value.includes(category)) {
        openCategories.value = openCategories.value.filter(c => c !== category);
    } else {
        openCategories.value.push(category);
    }
}

const isCategoryOpen = (category: string) => {
    return openCategories.value.includes(category);
}

const setActiveCategory = (category: string) => {
    activeCategory.value = category
    selectedQuestion.value = null // 返回列表视图
}
</script>

<style lang="scss" scoped>
.ml-13 {
    margin-left: 3.25rem;
}

.faqs-container {
    /* 移除之前的样式，使用与AboutView一致的结构 */
    background-color: white;
}

/* FAQ卡片样式 */
.faq-card {
    background-color: white;
    border-radius: 1.5rem;
    padding: 2rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(0);
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    }
}

.faq-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 9999px;
    background-color: #FBAD00;
    margin-right: 0.75rem;
    flex-shrink: 0;

    span {
        font-weight: 600;
        color: black;
    }
}

/* 订阅部分 */
.subscribe-section {
    padding: 5rem 0;
    position: relative;

    &::before {
        content: '';
        position: absolute;
        inset: 0;
        background: linear-gradient(to right, #52018D, #733CA8);
        opacity: 0.9;
        z-index: 0;
    }

    &::after {
        content: '';
        position: absolute;
        inset: 0;
        background-image: url('@/assets/about/subscribe-bg.png');
        background-size: cover;
        background-position: center;
        opacity: 0.45;
        z-index: 1;
    }
}

.subscribe-button {
    background-color: #FBAD00;
    color: black;
    font-weight: 700;
    padding: 1rem 2.5rem;
    border-radius: 9999px;
    transition: all 0.3s ease;

    &:hover {
        background-color: #e69d00;
        transform: scale(1.05);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    }
}

/* 响应式调整 */
@media (max-width: 1023px) {
    .faq-card {
        padding: 1.5rem;
        margin-top: 1rem;
    }
}

@media (max-width: 768px) {
    .ml-13 {
        margin-left: 2rem;
    }
    
    .faq-card {
        padding: 1rem;
    }
}

:deep(.prose) {
  h1, h2, h3 {
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 0.5rem;
    margin-top: 1.5rem;
    margin-bottom: 1rem;
  }

  p {
    line-height: 1.8;
    margin-bottom: 1rem;
  }

  ul, ol {
    padding-left: 1.5rem;
    margin-bottom: 1rem;
  }

  a {
    color: #52018D;
    text-decoration: underline;
    &:hover {
      color: #733CA8;
    }
  }

  strong {
    font-weight: 600;
  }
}
</style>