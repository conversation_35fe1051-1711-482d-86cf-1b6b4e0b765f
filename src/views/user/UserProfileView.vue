<template>
    <div class="user-profile-container">
        <!-- 用户资料主要内容部分 -->
        <section class="container pt-16 pb-52 px-4 sm:px-6 lg:px-8">
            <h1 class="text-4xl font-bold text-left mb-8">{{ t('user.account') }}</h1>
            <!-- 主要内容区域 -->
            <div class="flex gap-8 max-w-8xl ">
                <!-- 左侧导航栏 -->
                <div class="sidebar w-64 flex-shrink-0">
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="space-y-2">
                            <!-- 个人资料 -->
                            <div class="sidebar-item cursor-pointer p-3 rounded-lg hover:bg-purple-50 transition-colors duration-200"
                            :class="{ 'bg-purple-100 text-purple-800': activeCategory === 'profile' }"
                            @click="setActiveCategory('profile')">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                    </svg>
                                    {{ t('user.profile') }}
                                </div>
                            </div>
                            <!-- 地址 -->
                            <div class="sidebar-item cursor-pointer p-3 rounded-lg hover:bg-purple-50 transition-colors duration-200"
                            :class="{ 'bg-purple-100 text-purple-800': activeCategory === 'address' }"
                            @click="setActiveCategory('address')">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    </svg>
                                    {{ t('user.address') }}
                                </div>
                            </div>
                            <!-- 订单 -->
                            <div class="sidebar-item cursor-pointer p-3 rounded-lg hover:bg-purple-50 transition-colors duration-200"
                            :class="{ 'bg-purple-100 text-purple-800': activeCategory === 'orders' }"
                            @click="setActiveCategory('orders')">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <circle cx="8" cy="21" r="1"></circle>
                                        <circle cx="19" cy="21" r="1"></circle>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.16"/>
                                    </svg>
                                    {{ t('user.orders') }}
                                </div>
                            </div>
                            <!-- 密码管理 -->
                            <div class="sidebar-item cursor-pointer p-3 rounded-lg hover:bg-purple-50 transition-colors duration-200"
                            :class="{ 'bg-purple-100 text-purple-800': activeCategory === 'password' }"
                            @click="setActiveCategory('password')">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    </svg>
                                    {{ t('user.passwordSettings') }}
                                </div>
                            </div>
                            <!-- 历史记录 -->
                            <div class="sidebar-item cursor-pointer p-3 rounded-lg hover:bg-purple-50 transition-colors duration-200"
                            :class="{ 'bg-purple-100 text-purple-800': activeCategory === 'history' }"
                            @click="setActiveCategory('history')">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3v5h5" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 7v5l4 2" />
                                    </svg>
                                    {{ t('user.history') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 中间表单区域 -->
                <div class="flex-1">
                    <!-- 个人资料表单 -->
                    <div v-if="activeCategory === 'profile'" class="content-section">
                        <div class="bg-white rounded-lg shadow-md p-8" style="background-color: #FAF5FF;">
                            <el-form :model="userInfo" ref="userForm" label-width="120px" class="user-form">
                                <!--用户名称-->
                                <el-form-item :label="t('user.username')" class="form-item">
                                    <div class="input-wrapper">
                                        <el-input 
                                            v-model="userInfo.name" 
                                            maxlength="40"
                                            show-word-limit
                                            class="form-input"
                                        ></el-input>
                                    </div>
                                </el-form-item>
                                
                                <!--性别-->
                                <el-form-item :label="t('user.gender')" class="form-item">
                                    <el-select v-model="userInfo.gender" :placeholder="t('user.selectGender')" class="form-select">
                                        <el-option :label="t('user.male')" value="male"></el-option>
                                        <el-option :label="t('user.female')" value="female"></el-option>
                                    </el-select>
                                </el-form-item>
                                
                                <!--出生日期-->
                                <el-form-item :label="t('user.birthDate')" class="form-item">
                                    <el-date-picker 
                                        v-model="userInfo.birthDate" 
                                        type="date"
                                        class="form-date-picker"
                                    ></el-date-picker>
                                </el-form-item>
                                
                                <!--手机号-->
                                <el-form-item :label="t('user.mobile')" class="form-item">
                                    <el-input v-model="userInfo.mobile" class="form-input"></el-input>
                                </el-form-item>
                                
                                <!--邮箱-->
                                <el-form-item :label="t('user.email')" class="form-item">
                                    <el-input v-model="userInfo.email" disabled class="form-input-with-button">
                                        <template #append>
                                            <el-button type="primary" @click="openEmailDialog" class="modify-button">
                                                {{ t('user.modify') }}
                                            </el-button>
                                        </template>
                                    </el-input>
                                </el-form-item>
                                
                                <!-- 保存按钮 -->
                                <el-form-item class="form-item-submit">
                                    <el-button 
                                        type="primary" 
                                        size="large"
                                        :disabled="!isModified || isSaving" 
                                        :loading="isSaving"
                                        @click="saveChanges"
                                        class="save-button"
                                    >
                                        {{ isSaving ? t('user.saving') : t('user.saveChanges') }}
                                    </el-button>
                                </el-form-item>
                            </el-form>
                        </div>
                    </div>

                    <!-- 密码设置表单 -->
                    <div v-if="activeCategory === 'password'" class="content-section">
                        <div class="bg-white rounded-lg shadow-md p-8" style="background-color: #FAF5FF;">
                            <h3 class="font-naya text-3xl font-semibold text-gray-800 mb-8 text-left">{{ t('user.chageYourPassword') }}</h3>
                            <el-form label-width="140px" class="password-form" label-position="left">
                                <!-- 当前密码 -->
                                <el-form-item :label="t('user.currentPassword')" class="form-item">
                                    <el-input 
                                        type="password" 
                                        v-model="oldPassword" 
                                        :placeholder="t('user.enterCurrentPassword')"
                                        class="form-input"
                                        show-password
                                    ></el-input>
                                </el-form-item>
                                <!-- 新密码 -->
                                <el-form-item :label="t('user.newPassword')" class="form-item">
                                    <el-input 
                                        type="password" 
                                        v-model="newPassword" 
                                        :placeholder="t('user.enterNewPassword')"
                                        class="form-input"
                                        show-password
                                    ></el-input>
                                </el-form-item>
                                <!-- 确认密码 -->
                                <el-form-item :label="t('user.confirmPassword')" class="form-item">
                                    <el-input 
                                        type="password" 
                                        v-model="confirmPassword" 
                                        :placeholder="t('user.enterConfirmPassword')"
                                        class="form-input"
                                        show-password
                                    ></el-input>
                                </el-form-item>
                                <!-- 保存按钮 -->
                                <el-form-item class="form-item-submit">
                                    <el-button 
                                        type="primary" 
                                        size="large"
                                        @click="updatePassword"
                                        class="password-save-button"
                                        :disabled="!oldPassword || !newPassword || !confirmPassword"
                                    >
                                        {{ t('user.saveChanges') }}
                                    </el-button>
                                    <el-button 
                                        type="primary"
                                        size="large"
                                        @click="goToResetPassword"
                                        class="password-save-button forgot-password-button"
                                    >
                                        {{ t('user.resetTitle') }}
                                    </el-button>
                                </el-form-item>
                            </el-form>
                        </div>
                    </div>

                    <!-- 收货地址 -->
                    <div v-if="activeCategory === 'address'" class="content-section">
                        <div class="bg-white rounded-lg shadow-md p-8" style="background-color: #FAF5FF;">
                            <h3 class="font-naya text-3xl font-semibold text-gray-800 mb-8 text-left">{{ t('user.address') }}</h3>
                            <div class="text-center py-12">
                                <p class="text-gray-500">{{ t('user.addressEmpty') }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- 我的订单 -->
                    <div v-if="activeCategory === 'orders'" class="content-section">
                        <div class="bg-white rounded-lg shadow-md p-8" style="background-color: #FAF5FF;">
                            <h3 class="font-naya text-3xl font-semibold text-gray-800 mb-8 text-left">{{ t('user.orderTitle') }}</h3>
                            <div class="text-center py-12">
                                <p class="text-gray-500">{{ t('user.ordersEmpty') }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- 历史记录 -->
                    <div v-if="activeCategory === 'history'" class="content-section">
                        <div class="bg-white rounded-lg shadow-md p-8" style="background-color: #FAF5FF;">
                            <h3 class="font-naya text-3xl font-semibold text-gray-800 mb-8 text-left">{{ t('user.historyEmpty') }}</h3>
                        </div>
                    </div>
                </div>

                <!-- 右侧头像区域 -->
                <div v-if="activeCategory === 'profile'" class="avatar-section w-80 flex-shrink-0">
                    <div class="bg-white rounded-lg shadow-md p-6" style="background-color: #FAF5FF;">
                        <div class="avatar-container">
                            <div class="avatar-wrapper" @click="openAvatarDialog">
                                <div class="avatar">
                                    <img v-if="userInfo.avatar" :src="userInfo.avatar" alt="Avatar" />
                                    <div v-else class="avatar-placeholder">
                                        <svg class="w-12 h-12" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                                        </svg>
                                    </div>
                                    <div class="avatar-overlay">
                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"/>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div class="avatar-tips">
                                <p class="text-sm text-gray-600 text-center mt-4">
                                    {{ t('user.avatarFormatTip') }}
                                </p>
                                <p class="text-sm text-gray-600 text-center">
                                    {{ t('user.avatarSizeTip') }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 邮箱修改对话框 -->
        <el-dialog 
            v-model="emailDialogVisible" 
            :title="t('user.modifyEmail')" 
            width="500px"
            class="custom-dialog"
        >
            <el-form label-width="120px" class="dialog-form">
                <!-- 新邮箱 -->
                <el-form-item :label="t('user.newEmail')">
                    <el-input 
                        v-model="newEmail" 
                        :placeholder="t('user.enterNewEmail')"
                        class="dialog-input"
                    ></el-input>
                </el-form-item>
                <!-- 验证码 -->
                <el-form-item :label="t('user.verificationCode')">
                    <el-input 
                        v-model="emailCode" 
                        :placeholder="t('user.enterVerificationCode')"
                        class="dialog-input"
                    >
                        <template #append>
                            <el-button @click="sendEmailCode" class="code-button">{{ t('user.sendCode') }}</el-button>
                        </template>
                    </el-input>
                </el-form-item>
            </el-form>
            <!-- 保存按钮 -->
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="emailDialogVisible = false" class="cancel-button">
                        {{ t('user.cancel') }}
                    </el-button>
                    <el-button type="primary" @click="confirmEmailChange" class="confirm-button">
                        {{ t('user.confirm') }}
                    </el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 头像上传对话框 -->
        <el-dialog 
            v-model="avatarDialogVisible" 
            :title="t('user.uploadAvatar')" 
            width="500px"
            class="custom-dialog"
        >
            <!-- 选择文件 -->
            <el-form label-width="120px" class="dialog-form">
                <el-form-item :label="t('user.selectFile')">
                    <el-upload
                        action="#"
                        :auto-upload="false"
                        :show-file-list="false"
                        accept=".jpg,.jpeg,.png"
                        class="avatar-upload"
                    >
                        <el-button class="upload-button">{{ t('user.selectImage') }}</el-button>
                    </el-upload>
                </el-form-item>
                <!-- 提示 -->
                <div class="upload-tips">
                    <div class="tip-item">
                        <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        <span>{{ t('user.supportedFormats') }}</span>
                    </div>
                    <div class="tip-item">
                        <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        <span>{{ t('user.fileSizeLimit') }}</span>
                    </div>
                    <div class="tip-item">
                        <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        <span>{{ t('user.recommendedSize') }}</span>
                    </div>
                </div>
            </el-form>
            <!-- 保存按钮 -->
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="avatarDialogVisible = false" class="cancel-button">
                        {{ t('user.cancel') }}
                    </el-button>
                    <el-button type="primary" class="confirm-button">
                        {{ t('user.confirm') }}
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
    import { ref, onMounted, computed, watch } from 'vue'
    import { useI18n } from 'vue-i18n'
    import { useRouter } from 'vue-router'
    import { ElMessage } from 'element-plus'
    import { useUserStore } from '@/stores/user'
    import { userApi, type UpdateUserInfoRequest, GenderUtils } from '@/api/modules/user'
    const { t } = useI18n()
    const userStore = useUserStore()
    const router = useRouter()

    // 添加保存状态
    const isSaving = ref(false)

    // 当前选中的导航项
    const activeCategory = ref('profile')

    // 对话框显示状态控制
    const emailDialogVisible = ref(false)
    const avatarDialogVisible = ref(false)

    // 表单数据绑定
    const newEmail = ref('')
    const emailCode = ref('')
    const captchaId = ref('')
    const oldPassword = ref('')
    const newPassword = ref('')
    const confirmPassword = ref('')

    // 头像上传相关
    // const activeTab = ref('file')
    // const fileList = ref([])
    // const avatarUrl = ref('')

    // 表单数据绑定
    const userInfo = ref({
        name: '',
        gender: '',
        birthDate: '',
        email: '',
        mobile: '',
        avatar: '',
        password: ''
    })

    // 原始用户信息，用于比较是否有修改
    const originalUserInfo = ref({
        name: '',
        gender: '',
        birthDate: '',
        email: '',
        mobile: '',
        avatar: '',
        password: ''
    })

    // 添加新的变量
    const isSendingCode = ref(false)

    // 监听store中的用户信息变化，自动更新表单数据
    watch(() => userStore.userInfo, (newUserInfo) => {
        if (newUserInfo) {
            
            const infoData = {
                name: newUserInfo.username || '',
                gender: GenderUtils.numberToString((newUserInfo as any).gender || 0), 
                birthDate: (newUserInfo as any).birth || (newUserInfo as any).birthDate || '',
                email: newUserInfo.email || '',
                mobile: (newUserInfo as any).mobile || (newUserInfo as any).phone || '',
                avatar: newUserInfo.avatar || '',
                remark: (newUserInfo as any).remark || '',
                password: ''
            }
            userInfo.value = { ...infoData }
            originalUserInfo.value = { ...infoData }
        }
    }, { immediate: true })

    // 计算属性：判断表单是否有修改，用于控制保存按钮状态
    const isModified = computed(() => {
        return JSON.stringify(userInfo.value) !== JSON.stringify(originalUserInfo.value)
    })

    // 保存用户信息修改
    const saveChanges = async () => {
        // 防止重复提交
        if (isSaving.value) {
            ElMessage.warning(t('user.savingInProgress'))
            return
        }

        try {
            isSaving.value = true

            // 日期格式化函数
            const formatDateForAPI = (date: string | Date): string => {
                if (!date) return ''
                try {
                    const dateObj = new Date(date)
                    // 检查日期是否有效
                    if (isNaN(dateObj.getTime())) {
                        console.warn('无效日期:', date)
                        return ''
                    }
                    const formatted = dateObj.toISOString().split('T')[0]
                    console.log('日期格式化:', date, '->', formatted)
                    return formatted
                } catch (error) {
                    console.error('日期格式化失败:', date, error)
                    return ''
                }
            }

            const updateData: UpdateUserInfoRequest = {
                username: userInfo.value.name || undefined,
                gender: userInfo.value.gender ? GenderUtils.stringToNumber(userInfo.value.gender) : undefined, // 字符串转数字
                birth: userInfo.value.birthDate ? formatDateForAPI(userInfo.value.birthDate) : undefined,
                mobile: userInfo.value.mobile || undefined
            }

            // 移除undefined值，只发送有值的字段
            Object.keys(updateData).forEach(key => {
                const typedKey = key as keyof UpdateUserInfoRequest
                if (updateData[typedKey] === undefined || updateData[typedKey] === '') {
                    delete updateData[typedKey]
                }
            })

            // 如果没有要更新的数据，直接返回
            if (Object.keys(updateData).length === 0) {
                ElMessage.warning(t('user.noDataToUpdate'))
                return
            }

            // 调用保存用户信息的API
            const response = await userApi.updateUserInfo(updateData)
            
            console.log('用户信息更新成功:', response);

            // 更新成功后的处理
            ElMessage.success(t('user.saveSuccess'))
            
            // 重新获取最新的用户信息 - 强制刷新
            const updatedUserInfo = await userStore.refreshUserInfo(true)
            
            // 更新表单数据，确保显示最新的服务器数据
            if (updatedUserInfo) {
                const infoData = {
                    name: updatedUserInfo.username || '',
                    gender: GenderUtils.numberToString((updatedUserInfo as any).gender || 0),
                    birthDate: (updatedUserInfo as any).birth || (updatedUserInfo as any).birthDate || '',
                    email: updatedUserInfo.email || '',
                    mobile: (updatedUserInfo as any).mobile || (updatedUserInfo as any).phone || '',
                    avatar: updatedUserInfo.avatar || '',
                    password: ''
                }
                userInfo.value = { ...infoData }
                originalUserInfo.value = { ...infoData }
            }
                
        } catch (error: any) {
            console.error('保存用户信息失败:', error)
            
            // 统一错误处理
            const errorMessage = error?.response?.data?.msg || error?.message || t('user.saveFailedRetry')
            
            if (error?.message?.includes('401') || error?.message?.includes('未授权')) {
                ElMessage.error(t('user.loginExpired'))
                userStore.logout()
            } else {
                ElMessage.error(errorMessage)
            }
        } finally {
            isSaving.value = false
        }
    }

    // 打开邮箱修改对话框
    const openEmailDialog = () => {
        emailDialogVisible.value = true
    }

    // 打开头像上传对话框
    const openAvatarDialog = () => {
        avatarDialogVisible.value = true
    }

    // 设置当前活动分类
    const setActiveCategory = (category: string) => {
        activeCategory.value = category
        // 保存到localStorage以便刷新后恢复状态
        localStorage.setItem('userProfileActiveCategory', category)
    }

    const goToResetPassword = () => {
        router.push({ name: 'reset-password', query: { source: 'profile' } })
    }

    // 更新密码
    const updatePassword = async () => {
        if (!oldPassword.value || !newPassword.value || !confirmPassword.value) {
            ElMessage.warning(t('user.pleaseFillInCompletePasswordInfo'))
            return
        }

        if (newPassword.value !== confirmPassword.value) {
            ElMessage.error(t('user.passwordsDoNotMatch'))
            return
        }

        if (newPassword.value.length < 8) {
            ElMessage.error(t('user.passwordLengthError'))
            return
        }

        try {
            // 调用修改密码的API
            const response = await userApi.updatePassword({
                oldPassword: oldPassword.value,
                newPassword: newPassword.value,
                confirmNewPassword: confirmPassword.value
            })
            
            ElMessage.success(t('user.passwordUpdateSuccess'))
            
            // 清空表单
            oldPassword.value = ''
            newPassword.value = ''
            confirmPassword.value = ''
        } catch (error: any) {
            console.error('修改密码失败:', error)
            
            // 统一错误处理
            const errorMessage = error?.response?.data?.msg || error?.message || t('user.passwordUpdateFailed')
            ElMessage.error(errorMessage)
        }
    }

    // // 头像上传前的文件验证
    // const beforeAvatarUpload = (file: File) => {
    //     // 检查文件格式：只允许 JPG、PNG、JPEG
    //     const isValidFormat = ['image/jpeg', 'image/jpg', 'image/png'].includes(file.type)
    //     // 检查文件大小：不超过 5MB
    //     const isLt5M = file.size / 1024 / 1024 < 5

    //     if (!isValidFormat) {
    //         ElMessage.error(t('user.uploadFormatError'))
    //         return false
    //     }
    //     if (!isLt5M) {
    //         ElMessage.error(t('user.uploadSizeError'))
    //         return false
    //     }
    //     return true
    // }

    // // 头像上传成功回调
    // const handleAvatarUploadSuccess = (response: any) => {
    //     ElMessage.success(t('user.uploadSuccess'))
    //     userInfo.value.avatar = response.data.url
    //     avatarDialogVisible.value = false
    // }

    // // 头像上传失败回调
    // const handleAvatarUploadError = () => {
    //     ElMessage.error(t('user.uploadFailed'))
    // }

    // // 提交头像上传（预留功能）
    // const submitUpload = () => {
    //     // 头像上传逻辑
    //     ElMessage.success(t('user.uploadSuccess'))
    // }

    // // 通过URL上传头像（预留功能）
    // const uploadAvatarByUrl = async () => {
    //     try {
    //         // 通过URL上传头像
    //         ElMessage.success(t('user.uploadSuccess'))
    //         userInfo.value.avatar = avatarUrl.value
    //         avatarDialogVisible.value = false
    //     } catch (error) {
    //         ElMessage.error(t('user.uploadFailed'))
    //     }
    // }

    // 发送邮箱验证码
    const sendEmailCode = async () => {
        if (!newEmail.value) {
            ElMessage.warning(t('user.enterNewEmail'))
            return
        }
        if (!/\S+@\S+\.\S+/.test(newEmail.value)) {
            ElMessage.warning(t('user.emailInvalid'))
            return
        }
        isSendingCode.value = true
        try {
            const newCaptchaId = await userApi.sendUpdateEmailCode({ email: newEmail.value })
            captchaId.value = newCaptchaId

            ElMessage.success(t('user.sendCodeSuccess'))
        } catch (error) {
            console.error('发送验证码失败:', error)
            // 这里的错误提示可以保持不变
            ElMessage.error(t('user.sendCodeFailed'))
        } finally {
            isSendingCode.value = false
        }
    }

    // 确认邮箱修改
    const confirmEmailChange = async () => {
        if (!newEmail.value.trim()) {
            ElMessage.warning(t('user.promptEnterNewEmail'))
            return
        }

        if (!emailCode.value.trim()) {
            ElMessage.warning(t('user.promptEnterVerificationCode'))
            return
        }

        try {
            // 调用修改邮箱的API
            const response = await userApi.updateEmail({
                newEmail: newEmail.value,
                captchaCode: emailCode.value,
                captchaId: captchaId.value
            })
            
            ElMessage.success(t('user.emailUpdateSuccess'))
            
            // 关闭对话框并清空表单
            emailDialogVisible.value = false
            newEmail.value = ''
            emailCode.value = ''
            
            // 重新获取用户信息以更新显示
            await userStore.refreshUserInfo(true)
        } catch (error: any) {
            console.error('修改邮箱失败:', error)
            
            // 统一错误处理
            const errorMessage = error?.response?.data?.msg || error?.message || t('user.emailUpdateFailedRetry')
            ElMessage.error(errorMessage)
        }
    }

    /**
     * 异步重试工具函数
     * @param fn 要执行的异步函数
     * @param retries 重试次数
     * @param delay 每次重试之间的延迟（毫秒）
     */
    const retryAsync = async <T>(fn: () => Promise<T>, retries = 3, delay = 500, attempt = 1): Promise<T> => {
        try {
            return await fn()
        } catch (error) {
            if (retries <= 1) {
                console.error(`异步操作在 ${attempt} 次尝试后最终失败。`)
                throw error
            }
            console.warn(`异步操作失败 (尝试 ${attempt}/${attempt + retries -1})。将在 ${delay}ms 后重试...`)
            await new Promise(resolve => setTimeout(resolve, delay))
            return retryAsync(fn, retries - 1, delay, attempt + 1)
        }
    }

    // 组件挂载时确保有用户信息
    onMounted(async () => {
        // 从localStorage恢复导航状态
        const savedCategory = localStorage.getItem('userProfileActiveCategory')
        if (savedCategory) {
            activeCategory.value = savedCategory
        }

        try {
            // 强制从服务器获取最新的用户信息，并加入重试逻辑
            const latestUserInfo = await retryAsync(() => userStore.refreshUserInfo(true), 3, 500)
            
            if (latestUserInfo) {
                // 更新表单数据，使用安全的字段访问
                const infoData = {
                    name: latestUserInfo.username || '',
                    gender: GenderUtils.numberToString((latestUserInfo as any).gender || 0), 
                    birthDate: (latestUserInfo as any).birth || (latestUserInfo as any).birthDate || '',
                    email: latestUserInfo.email || '',
                    mobile: (latestUserInfo as any).mobile || (latestUserInfo as any).phone || '',
                    avatar: latestUserInfo.avatar || '',
                    password: ''
                }
                userInfo.value = { ...infoData }
                originalUserInfo.value = { ...infoData }
                
            }
        } catch (error) {
            console.error('获取用户信息失败(重试后):', error)
            ElMessage.error(t('user.fetchUserInfoFailedRetry'))
        }
    })
</script>

<style lang="scss" scoped>
/* 主容器样式 */
.user-profile-container {
    background-color: white;
}

/* 表单区域样式 */
.user-form {
    .form-item {
        margin-bottom: 2rem;
        
        :deep(.el-form-item__label) {
            font-weight: 600;
            color: #374151;
            font-size: 1rem;
        }
    }

    .form-input,
    .form-select,
    .form-date-picker {
        width: 100%;
        max-width: 300px;
        
        :deep(.el-input__wrapper) {
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
            
            &:hover {
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }
            
            &.is-focus {
                box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
                border-color: #52018D;
            }
        }
    }

    .form-input-with-button {
        max-width: 400px;
    }

    .modify-button {
        background-color: #52018D !important;
        border-color: #52018D !important;
        color: white !important;
        border-radius: 0 0.5rem 0.5rem 0;
        transition: all 0.2s ease;

        &:hover {
            background-color: #52018D !important;
            border-color: #52018D !important;
            transform: translateY(-1px);
        }
    }

    .form-item-submit {
        margin-top: 3rem;
        
        .save-button {
            background-color: #52018D;
            border-color: #52018D;
            border-radius: 0.75rem;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
            
            &:not(:disabled):hover {
                background-color: #7433a3;
                border-color: #7433a3;
                transform: translateY(-2px);
                box-shadow: 0 10px 25px rgba(124, 58, 237, 0.3);
            }
            
            &:disabled {
                background-color: #e5e7eb;
                border-color: #e5e7eb;
                color: #9ca3af;
                cursor: not-allowed;
                transform: none;
                box-shadow: none;
            }
        }
    }
}

/* 内容区域样式 */
.content-section {
    width: 100%;
}

/* 右侧区域样式 */
.right-section {
    .space-y-6 > * + * {
        margin-top: 1.5rem;
    }
}

/* 密码表单样式 */
.password-form {
    .form-item {
        margin-bottom: 2rem;
        display: flex;
        align-items: center;
        
        :deep(.el-form-item__label) {
            font-weight: 600;
            color: #374151;
            font-size: 1rem;
            text-align: left;
            padding-right: 12px;
            line-height: 1.5;
            white-space: nowrap;
            flex-shrink: 0;
            width: 140px !important;
        }
        
        :deep(.el-form-item__content) {
            text-align: left;
            flex: 1;
            display: flex;
            align-items: center;
        }
    }

    .form-input {
        width: 100%;
        max-width: 400px;
        
        :deep(.el-input__wrapper) {
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
            
            &:hover {
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }
            
            &.is-focus {
                box-shadow: 0 0 0 3px rgba(82, 1, 141, 0.1);
                border-color: #52018D;
            }
        }
    }

    .form-item-submit {
        margin-top: 3rem;
        margin-bottom: 0;
        
        :deep(.el-form-item__content) {
            text-align: left;
            gap: 1rem;
        }
        
        .password-save-button {
            min-width: 160px;
            background-color: #52018D;
            border-color: #52018D;
            border-radius: 0.75rem;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
            
            &:not(:disabled):hover {
                background-color: #7433a3;
                border-color: #7433a3;
                transform: translateY(-2px);
                box-shadow: 0 10px 25px rgba(82, 1, 141, 0.3);
            }
            
            &:disabled {
                background-color: #e5e7eb;
                border-color: #e5e7eb;
                color: #9ca3af;
                cursor: not-allowed;
                transform: none;
                box-shadow: none;
            }
        }
    }
}

/* 头像区域样式 */
.avatar-section {
    .avatar-container {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .avatar-wrapper {
        position: relative;
        cursor: pointer;
        transition: transform 0.3s ease;
        
        &:hover {
            transform: scale(1.05);
            
            .avatar-overlay {
                opacity: 1;
            }
        }
    }

    .avatar {
        position: relative;
        width: 150px;
        height: 150px;
        border-radius: 50%;
        overflow: hidden;
        background: linear-gradient(135deg, #52018D, #7433a3);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 10px 25px rgba(124, 58, 237, 0.2);
        
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .avatar-placeholder {
            color: white;
            font-size: 2rem;
        }
    }

    .avatar-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
        color: white;
    }

    .avatar-tips {
        margin-top: 1rem;
        max-width: 200px;
        
        p {
            line-height: 1.5;
            margin-bottom: 0.25rem;
        }
    }
}


.radio-icon {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid #ccc;
    position: relative;
    transition: all 0.3s ease;

    &.selected {
        border-color: #52018D;
        background-color: #52018D;

        &::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: white;
            transform: translate(-50%, -50%);
        }
    }
}

.your-plan-badge {
    background-color: #FBAD00;
    color: #333;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
}

.feature-checkmark {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #FBAD00;
    flex-shrink: 0;
}

/* 对话框样式 */
.custom-dialog {
    :deep(.el-overlay-dialog) {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
    }

    :deep(.el-dialog) {
        border-radius: 1rem;
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        margin: 0;
        position: relative;
    }

    :deep(.el-dialog__header) {
        padding: 2rem 2rem 1rem;
        border-bottom: 1px solid #f3f4f6;
        text-align: center;
        
        .el-dialog__title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #111827;
        }
    }

    :deep(.el-dialog__body) {
        padding: 1.5rem 2rem;
    }

    :deep(.el-dialog__footer) {
        padding: 1rem 2rem 2rem;
        border-top: 1px solid #f3f4f6;
        text-align: center;
    }

    .dialog-form {
        .email-change-tip {
            margin-bottom: 1.5rem;
            
            :deep(.el-alert) {
                border-radius: 0.5rem;
                
                .el-alert__content {
                    font-size: 0.875rem;
                    line-height: 1.5;
                }
            }
        }
        
        .el-form-item {
            margin-bottom: 1.5rem;
            
            :deep(.el-form-item__label) {
                font-weight: 600;
                color: #374151;
                text-align: right;
                padding-right: 12px;
            }
            
            :deep(.el-form-item__content) {
                text-align: left;
            }
        }
    }

    .dialog-input {
        :deep(.el-input__wrapper) {
            border-radius: 0.5rem;
            transition: all 0.2s ease;
            
            &:hover {
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            
            &.is-focus {
                box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
                    border-color: #52018D;
            }
        }
    }

    .code-button {
        background-color: #f3f4f6;
        border-color: #e5e7eb;
        color: #6b7280;
        border-radius: 0 0.5rem 0.5rem 0;
        
        &:hover {
            background-color: #e5e7eb;
            color: #374151;
        }
    }

    .dialog-footer {
        display: flex;
        justify-content: center;
        gap: 1rem;
    }

    .cancel-button {
        background-color: #f9fafb;
        border-color: #e5e7eb;
        color: #6b7280;
        border-radius: 0.5rem;
        padding: 0.5rem 1.5rem;
        
        &:hover {
            background-color: #f3f4f6;
            border-color: #d1d5db;
            color: #374151;
        }
    }

    .confirm-button {
        background-color: #52018D;
        border-color: #52018D;
        border-radius: 0.5rem;
        padding: 0.5rem 1.5rem;
        
        &:hover {
            background-color: #7433a3;
            border-color: #7433a3;
        }
    }
}

/* 头像上传样式 */
.avatar-upload {
    .upload-button {
        background-color: #f3f4f6;
        border-color: #e5e7eb;
        color: #374151;
        border-radius: 0.5rem;
        padding: 0.75rem 1.5rem;
        
        &:hover {
            background-color: #e5e7eb;
            border-color: #d1d5db;
        }
    }
}

.upload-tips {
    margin-top: 1.5rem;
    padding: 1rem;
    background-color: #f0f9ff;
    border-radius: 0.5rem;
    border: 1px solid #e0f2fe;
    
    .tip-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
        color: #374151;
        
        &:last-child {
            margin-bottom: 0;
        }
    }
}

/* 响应式调整 */
@media (max-width: 1024px) {
    .flex.gap-8.max-w-6xl {
        flex-direction: column;
        gap: 2rem;
    }
    
    .avatar-section {
        width: 100%;
        order: -1;
        
        .bg-white {
            max-width: 400px;
            margin: 0 auto;
        }
    }
}

@media (max-width: 768px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .text-6xl {
        font-size: 2.5rem;
    }
    
    .py-24 {
        padding-top: 3rem;
        padding-bottom: 3rem;
    }
    
    .bg-white.rounded-lg.shadow-md {
        padding: 1.5rem;
    }
    
    .user-form {
        .form-input,
        .form-select,
        .form-date-picker,
        .form-input-with-button {
            max-width: 100%;
        }
        
        :deep(.el-form-item__label) {
            width: 100px !important;
            font-size: 0.875rem;
        }
    }
    
    .avatar {
        width: 120px !important;
        height: 120px !important;
    }
    
    .custom-dialog {
        :deep(.el-dialog) {
            width: 95% !important;
            margin: 0 auto;
        }
        
        :deep(.el-dialog__header),
        :deep(.el-dialog__body),
        :deep(.el-dialog__footer) {
            padding-left: 1rem;
            padding-right: 1rem;
        }
        
        .dialog-form {
            :deep(.el-form-item__label) {
                width: 100px !important;
                font-size: 0.875rem;
            }
        }
    }
}

@media (max-width: 480px) {
    .text-6xl {
        font-size: 2rem;
    }
    
    .bg-white.rounded-lg.shadow-md {
        padding: 1rem;
    }
    
    .user-form {
        :deep(.el-form-item__label) {
            width: 80px !important;
            font-size: 0.8rem;
        }
    }
    
    .avatar {
        width: 100px !important;
        height: 100px !important;
    }
}
</style>