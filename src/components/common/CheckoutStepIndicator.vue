<template>
    <div class="checkout-steps">
        <div class="step" :class="{ completed: currentStep > 1, active: currentStep === 1 }">
            <div class="step-number">01</div>
            <div class="step-content">
                <div class="step-title">{{ t('cart.shoppingCart') }}</div>
                <div class="step-description">{{ t('cart.organizeItems') }}</div>
            </div>
            <div class="step-progress"></div>
        </div>
        <div class="step" :class="{ completed: currentStep > 2, active: currentStep === 2 }">
            <div class="step-number">02</div>
            <div class="step-content">
                <div class="step-title">{{ t('cart.shippingCheckout') }}</div>
                <div class="step-description">{{ t('cart.examineItems') }}</div>
            </div>
            <div class="step-progress"></div>
        </div>
        <div class="step" :class="{ completed: currentStep >= 3, active: currentStep === 3 }">
            <div class="step-number">03</div>
            <div class="step-content">
                <div class="step-title">{{ t('cart.confirmation') }}</div>
                <div class="step-description">{{ t('cart.examineAndSend') }}</div>
            </div>
            <div class="step-progress"></div>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { useI18n } from 'vue-i18n'

    const { t } = useI18n()

    interface Props {
        currentStep: 1 | 2 | 3
    }

    defineProps<Props>()
</script>

<style scoped lang="scss">
    .checkout-steps {
        display: flex;
        justify-content: space-between;
        margin-bottom: 2rem;
        background: white;
        border-radius: 8px;
        padding: 2rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

        @media (max-width: 768px) {
            padding: 1rem;
            margin-bottom: 1rem;
            flex-direction: column;
            gap: 1.5rem;
        }

        @media (min-width: 769px) and (max-width: 1024px) {
            padding: 1.5rem;
        }

        .step {
            position: relative;
            flex: 1;
            text-align: center;
            padding-bottom: 1.5rem;

            &::after {
                content: '';
                position: absolute;
                top: 2rem;
                right: -50%;
                width: 100%;
                height: 2px;
                background-color: #eee;
                z-index: 1;

                @media (max-width: 768px) {
                    display: none;
                }
            }

            &:last-child::after {
                display: none;
            }

            @media (max-width: 768px) {
                display: flex;
                align-items: center;
                text-align: left;
                padding-bottom: 0;
                gap: 1rem;

                .step-number {
                    margin: 0;
                    flex-shrink: 0;
                }

                .step-content {
                    flex: 1;
                }
            }

            .step-number {
                position: relative;
                width: 3rem;
                height: 3rem;
                line-height: 3rem;
                margin: 0 auto 1rem;
                background-color: #f2f2f2;
                border-radius: 50%;
                font-size: 1.25rem;
                font-weight: 600;
                color: #666;
                z-index: 2;

                @media (max-width: 768px) {
                    width: 2.5rem;
                    height: 2.5rem;
                    line-height: 2.5rem;
                    font-size: 1rem;
                }
            }

            .step-content {
                .step-title {
                    font-size: 1.125rem;
                    font-weight: 600;
                    margin-bottom: 0.5rem;
                    color: #333;

                    @media (max-width: 768px) {
                        font-size: 1rem;
                    }
                }

                .step-description {
                    font-size: 0.875rem;
                    color: #666;

                    @media (max-width: 768px) {
                        font-size: 0.75rem;
                    }
                }
            }

            .step-progress {
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 4px;
                background-color: #eee;

                @media (max-width: 768px) {
                    display: none;
                }
            }

            &.active {
                .step-number {
                    background-color: #ffda44;
                    color: #333;
                }

                .step-title {
                    color: #000;
                }

                .step-progress {
                    background-color: #ffda44;
                }

                &::after {
                    background-color: #ffda44;
                }
            }

            &.completed {
                .step-number {
                    background-color: #52c41a;
                    color: white;
                }

                .step-progress {
                    background-color: #52c41a;
                }

                &::after {
                    background-color: #52c41a;
                }
            }
        }
    }
</style>
