// 支持的语言类型
export type SupportedLocales = 'en-US' | 'zh-CN'

// 语言包模块类型
export type LocaleModule = 'common' | 'footer' | 'header' | 'home' | 'search' | 'login' | 'register'

// 语言设置接口
export interface LocaleSettings {
    locale: SupportedLocales
    fallback: SupportedLocales
}

// 通用翻译类型
export interface CommonMessages {
    loading: string
    error: {
        network: string
        server: string
        unknown: string
        timeout: string
        unauthorized: string
        forbidden: string
        notFound: string
        validation: string
        loadProductsFailed: string
        loadingFailed: string
    }
    success: string
    confirm: string
    cancel: string
    appName: string
    home: string
    back: string
    backToHome: string
    notFound: string
    pageNotFound: string
    redirecting: string
    seconds: string
    retry: string
    attention: string
    close: string
    clear: string
    warning: string
    details: string
    button: {
        submit: string
        save: string
        edit: string
        delete: string
        next: string
        search: string
        more: string
        apply: string
        done: string
        confirm: string
        cancel: string
        refresh: string
    }
    status: {
        failed: string
        processing: string
        completed: string
        pending: string
        empty: string
    }
    message: {
        saved: string
        deleted: string
        updated: string
    }
    form: {
        required: string
        invalid: string
    }
    table: {
        noData: string
        loading: string
    }
    debug?: {
        imageLoadError: string
    }
    navigation: {
        previous: string
        next: string
    }
    pagination: {
        prev: string
        next: string
    }
    goShopping: string
    viewAll: string
    processing: string
    // 单位相关
    types: string
    pieces: string
}

// 页头翻译类型
export interface HeaderMessages {
    about: string
    guide: string
    faqs: string
    commission: string
    services: string
    login: string
    register: string
    searchProducts: string
    searchProductsError: string
    imageSizeError: string
    uploadingImage: string
    uploadSuccess: string
    uploadError: string
    search: {
        placeholder: string
        button: string
    }
    account: string
    cart: string
    language: string
    currency: string
    nav: {
        home: string
        products: string
        about: string
        contact: string
    }
    popularCategories: {
        allCategories: string
        placeholder: string
        grocery: string
        giftFinder: string
        toyShop: string
        home: string
        fashion: string
        giftCard: string
        household: string
        dropshipping: string
        electronics: string
        sports: string
        books: string
        beauty: string
    }
}

// 页脚翻译类型
export interface FooterMessages {
    description: string
    sections: {
        information: {
            title: string
            links: {
                loginSignUp: string
                aboutUs: string
                sellerPolicies: string
                termsConditions: string
            }
        }
        connect: {
            title: string
            links: {
                contactUs: string
                requestQuote: string
                forSellers: string
            }
        }
        newsletter: {
            title: string
            placeholder: string
            button: string
        }
    }
    copyright: string
    paymentMethods: string
}

// 首页翻译类型
export interface HomeMessages {
    banner: {
        title: string
        subtitle: string
        button: string
        contactEmail: string
        description: string
    }
    services: {
        support: {
            title: string
            desc: {
                contactEmail: string
            }
        }
        shipping: {
            title: string
            desc: string
        }
        storage: {
            title: string
            desc: string
        }
    }
    categories: {
        title: string
        subtitle: string
        items: {
            toys: string
            home: string
            fashion: string
            office: string
            pets: string
            appliances: string
            digital: string
            lighting: string
            luggage: string
            tools: string
            auto: string
            kitchen: string
        }
    }
    trending: {
        title: string
        subtitle: string
        button: string
    }
    electronics: {
        title: string
        subtitle: string
        cashback: string
        button: string
    }
    subscribe: {
        title: string
        subtitle: string
        placeholder: string
        button: string
    }
    promotions: {
        ariaLabel: string
        holiday: string
        playtime: string
        playtimeAge: string
        giftSets: string
        giftSetsDesc: string
        wrapIt: string
        wrapItDesc: string
        stylishGifts: string
        wowThem: string
        influencers: {
            emma: string
            jane: string
        }
    }
}

// 搜索页面翻译类型
export interface SearchMessages {
    resultsFor: string
    foundResults: string
    filters: string
    filtersTitle: string
    sort: string
    priceRange: string
    min: string
    max: string
    priceSort: string
    priceLowToHigh: string
    priceHighToLow: string
    salesSort: string
    salesHighToLow: string
    repurchaseRateHighToLow: string
    noResults: string
    noResultsFor: string
    emptyResults: string
    allProducts: string
    mostPopular: string
    bestsellers: string
    bestRating: string
    newest: string
    resetFilters: string
    qualityGuarantee: string
    storeRating: string
    serviceGuarantee: string
    tryDifferentSearch: string
    checkConnection: string
    totalEpScore: string
    pickupRate: string
    // 价格区间验证
    invalidPriceRange: string
    invalidMinPrice: string
    invalidMaxPrice: string
    loadProductsFailed: string
    // 图片搜索
    imageSearch: string
    // 质量保证选项
    quality: {
        [key: string]: string
    }
    // 店铺评分选项
    rating: {
        [key: string]: string
    }
    // 服务保障选项
    service: {
        [key: string]: string
    }
    // 揽收率选项
    pickupRateOptions: {
        [key: string]: string
    }
    product: {
        colors: string
        categories: string
        size: string
        viewAndOrder: string
        addToCart: string
        outOfStock: string
        showing: string
        to: string
        of: string
        results: string
        previous: string
        next: string
        orderBy: string
    }
    pagination: {
        previous: string
        next: string
        showing: string
        to: string
        of: string
        results: string
    }
    // 调试相关信息
    debug?: {
        status: string
        loadingStatus: string
        loading: string
        loaded: string
        error: string
        noError: string
        productsCount: string
        keyword: string
        noKeyword: string
        debugInfo: string
        searchParams: string
        searchType: string
        keywordSearch: string
        imageSearch: string
        imageId: string
        categoryId: string
        noCategory: string
        sort: string
        priceRange: string
        unlimited: string
        productsTotal: string
        currentPage: string
        requestParams: string
        componentMounted: string
        items: string
        none: string
        productsGrid: string
        gridColumns: string
        alternativeDisplay: string
        debugMode: string
        showing: string
        foundGridElements: string
    }
    goShopping: string
}

// 登录页面翻译类型
export interface LoginMessages {
    welcome: string
    username: string
    password: string
    captcha: string
    usernameRequired: string
    passwordRequired: string
    passwordLength: string
    captchaRequired: string
    captchaInvalid: string
    rememberMe: string
    forgotPassword: string
    loginButton: string
    loginSuccess: string
    loginFailed: string
    invalidCredentials: string
}

// 注册页面翻译类型
export interface RegisterMessages {
    welcomeBack: string
    bannerText: string
    welcome: string
    username: string
    email: string
    password: string
    confirmPassword: string
    captcha: string
    agreement: string
    terms: string
    registerButton: string
    hasAccount: string
    login: string
    usernameRequired: string
    usernameLength: string
    emailRequired: string
    emailInvalid: string
    passwordRequired: string
    passwordLength: string
    confirmPasswordRequired: string
    passwordMismatch: string
    captchaRequired: string
    captchaInvalid: string
    registerSuccess: string
    registerFailed: string
    emailVerification: {
        pageTitle: string
        successTitle: string
        successDesc: string
        errorTitle: string
        errorDesc: string
        invalidLink: string
        goLogin: string
        goHome: string
        verifying: string
    }
}

export interface Messages {
    common: CommonMessages
    header: HeaderMessages
    footer: FooterMessages
    home: HomeMessages
    search: SearchMessages
    login: LoginMessages
    register: RegisterMessages
}
