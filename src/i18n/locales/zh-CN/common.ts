import type { CommonMessages } from '../../types'

const common: CommonMessages = {
    loading: '加载中...',
    error: {
        network: '网络错误',
        server: '服务器错误',
        unknown: '未知错误',
        timeout: '请求超时',
        unauthorized: '未授权',
        forbidden: '访问被拒绝',
        notFound: '未找到',
        validation: '验证失败',
        loadProductsFailed: '加载商品失败',
        loadingFailed: '加载失败',
    },
    clear: '清除',
    success: '成功',
    confirm: '确认',
    cancel: '取消',
    appName: 'Naya Source',
    home: '首页',
    back: '返回',
    backToHome: '返回首页',
    notFound: '404',
    pageNotFound: '页面未找到',
    redirecting: '自动跳转中，还剩',
    details: '明细',
    seconds: '秒',
    retry: '重试',
    attention: '注意',
    close: '关闭',
    warning: '警告',
    button: {
        submit: '提交',
        save: '保存',
        edit: '编辑',
        delete: '删除',
        next: '下一步',
        search: '搜索',
        more: '更多',
        apply: '应用',
        done: '完成',
        confirm: '确认',
        cancel: '取消',
        refresh: '刷新',
    },
    status: {
        failed: '失败',
        processing: '处理中',
        completed: '已完成',
        pending: '待处理',
        empty: '暂无数据',
    },
    message: {
        saved: '保存成功',
        deleted: '删除成功',
        updated: '更新成功',
    },
    form: {
        required: '此项为必填',
        invalid: '输入无效',
    },
    table: {
        noData: '暂无数据',
        loading: '加载数据中',
    },
    debug: {
        imageLoadError: '图片加载失败的产品信息',
    },
    navigation: {
        previous: '上一页',
        next: '下一页',
    },
    viewAll: '查看全部',
    processing: '处理中...',
    pagination: {
        prev: '上一页',
        next: '下一页',
    },
    goShopping: '去购物',
    types: '种',
    pieces: '件',
}

export default common
