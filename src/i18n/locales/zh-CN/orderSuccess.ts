export default {
  thankYou: '感谢您的订购！',
  orderConfirmed: '您的订单已确认，我们将尽快发货。',
  orderNumber: '订单号',
  orderDetails: '订单详情',
  orderInfo: '订单信息',
  orderDate: '下单日期',
  paymentMethod: '支付方式',
  shippingAddress: '收货地址',
  deliveryInfo: '配送信息',
  deliveryToWarehouse: '商品将送达我们的仓库',
  consolidatedShipping: '我们会将您的订单进行集运处理',
  trackingAvailable: '订单处理完成后将提供跟踪信息',
  orderSummary: {
    productTypes: '商品种类',
    totalQuantity: '商品总数',
  },
  priceBreakdown: '价格明细',
  merchandiseAmount: '商品金额',
  shippingAmount: '运费',
  serviceFee: '服务费',
  discountAmount: '优惠金额',
  totalAmount: '订单总额',
  continueShopping: '继续购物',
  viewMyOrders: '查看我的订单',
  trackOrder: '跟踪订单',
  nextSteps: '接下来会发生什么？',
  step1: '我们正在向供应商采购您的商品',
  step2: '商品到达仓库后将进行质检和包装',
  step3: '所有商品到齐后将为您安排发货',
  // 错误处理相关
  noOrderData: '未找到订单信息',
  dataParseError: '订单数据格式错误',
  // 兼容性保留
  subtotal: '小计',
  shipping: '运费',
  tax: '税费',
  total: '总计'
}