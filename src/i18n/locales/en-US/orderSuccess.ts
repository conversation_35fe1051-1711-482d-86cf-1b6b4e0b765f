export default {
  thankYou: 'Thank You for Your Order!',
  orderConfirmed: 'Your order has been confirmed and we will ship it as soon as possible.',
  orderNumber: 'Order Number',
  orderDetails: 'Order Details',
  orderInfo: 'Order Information',
  orderDate: 'Order Date',
  paymentMethod: 'Payment Method',
  shippingAddress: 'Shipping Address',
  deliveryInfo: 'Delivery Information',
  deliveryToWarehouse: 'Items will be delivered to our warehouse',
  consolidatedShipping: 'We will consolidate your order for shipping',
  trackingAvailable: 'Tracking information will be provided after processing',
  orderSummary: {
    productTypes: 'Product Types',
    totalQuantity: 'Total Quantity',
  },
  priceBreakdown: 'Price Breakdown',
  merchandiseAmount: 'Merchandise Amount',
  shippingAmount: 'Shipping Amount',
  serviceFee: 'Service Fee',
  discountAmount: 'Discount Amount',
  totalAmount: 'Total Amount',
  continueShopping: 'Continue Shopping',
  viewMyOrders: 'View My Orders',
  trackOrder: 'Track Order',
  nextSteps: 'What happens next?',
  step1: 'We are procuring your items from suppliers',
  step2: 'Items will be inspected and packaged upon arrival',
  step3: 'Your order will be shipped once all items are ready',
  // Error handling
  noOrderData: 'Order information not found',
  dataParseError: 'Order data format error',
  // Compatibility
  subtotal: 'Subtotal',
  shipping: 'Shipping',
  tax: 'Tax',
  total: 'Total'
}