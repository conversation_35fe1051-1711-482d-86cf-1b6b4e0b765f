import type { CommonMessages } from '../../types'

const common: CommonMessages = {
    loading: 'Loading...',
    error: {
        network: 'Network Error',
        server: 'Server Error',
        unknown: 'Unknown Error',
        timeout: 'Request Timeout',
        unauthorized: 'Unauthorized',
        forbidden: 'Access Denied',
        notFound: 'Not Found',
        validation: 'Validation Failed',
        loadProductsFailed: 'Failed to load products',
        loadingFailed: 'Loading Failed',
    },
    success: 'Success',
    confirm: 'Confirm',
    cancel: 'Cancel',
    appName: 'Naya Source',
    home: 'Home',
    back: 'Back',
    backToHome: 'Back to Home',
    notFound: '404',
    pageNotFound: 'Page Not Found',
    redirecting: 'Redirecting in',
    seconds: 'seconds',
    retry: 'Retry',
    attention: 'Attention',
    close: 'Close',
    clear: 'Clear',
    warning: 'Warning',
    details: 'Details',
    button: {
        submit: 'Submit',
        save: 'Save',
        edit: 'Edit',
        delete: 'Delete',
        next: 'Next',
        search: 'Search',
        more: 'More',
        apply: 'Apply',
        done: 'Done',
        confirm: 'Confirm',
        cancel: 'Cancel',
        refresh: 'Refresh',
    },
    status: {
        failed: 'Failed',
        processing: 'Processing',
        completed: 'Completed',
        pending: 'Pending',
        empty: 'No Data',
    },
    message: {
        saved: 'Successfully Saved',
        deleted: 'Successfully Deleted',
        updated: 'Successfully Updated',
    },
    form: {
        required: 'This field is required',
        invalid: 'Invalid input',
    },
    table: {
        noData: 'No Data Available',
        loading: 'Loading Data',
    },
    debug: {
        imageLoadError: 'Product information with image loading error',
    },
    navigation: {
        previous: 'Previous',
        next: 'Next',
    },
    viewAll: 'View All',
    processing: 'Processing...',
    pagination: {
        prev: 'Previous',
        next: 'Next',
    },
    goShopping: 'Go Shopping',
    types: 'types',
    pieces: 'pieces',
}

export default common
