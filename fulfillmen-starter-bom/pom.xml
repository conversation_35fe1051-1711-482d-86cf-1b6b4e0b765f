<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.fulfillmen.starter</groupId>
        <artifactId>fulfillmen-dependencies</artifactId>
        <version>1.2.6-SNAPSHOT</version>
        <relativePath>../fulfillmen-dependencies</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>fulfillmen-starter-bom</artifactId>
    <packaging>pom</packaging>
    <name>Fulfillmen Starter BOM ${project.version}</name>

    <description>Fulfillmen starter BOM 工程的项目清单</description>

    <!-- 包含本工程所有项目 -->
    <dependencyManagement>
        <dependencies>
            <!-- 基础核心代码 -->
            <dependency>
                <groupId>com.fulfillmen.starter</groupId>
                <artifactId>fulfillmen-starter-core</artifactId>
                <version>1.2.6-SNAPSHOT</version>
            </dependency>
            <!-- API 文档代码 -->
            <dependency>
                <groupId>com.fulfillmen.starter</groupId>
                <artifactId>fulfillmen-starter-api-doc</artifactId>
                <version>1.2.6-SNAPSHOT</version>
            </dependency>
            <!-- 认证模块 -->
            <dependency>
                <groupId>com.fulfillmen.starter</groupId>
                <artifactId>fulfillmen-starter-auth-justauth</artifactId>
                <version>1.2.6-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.fulfillmen.starter</groupId>
                <artifactId>fulfillmen-starter-auth-satoken</artifactId>
                <version>1.2.6-SNAPSHOT</version>
            </dependency>
            <!-- 验证码 -->
            <!-- 图片验证码 -->
            <dependency>
                <groupId>com.fulfillmen.starter</groupId>
                <artifactId>fulfillmen-starter-captcha-graphic</artifactId>
                <version>1.2.6-SNAPSHOT</version>
            </dependency>
            <!-- 行为验证码 -->
            <dependency>
                <groupId>com.fulfillmen.starter</groupId>
                <artifactId>fulfillmen-starter-captcha-behavior</artifactId>
                <version>1.2.6-SNAPSHOT</version>
            </dependency>

            <!-- 缓存 -->
            <dependency>
                <groupId>com.fulfillmen.starter</groupId>
                <artifactId>fulfillmen-starter-cache-redisson</artifactId>
                <version>1.2.6-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.fulfillmen.starter</groupId>
                <artifactId>fulfillmen-starter-cache-jetcache</artifactId>
                <version>1.2.6-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.fulfillmen.starter</groupId>
                <artifactId>fulfillmen-starter-cache-springcache</artifactId>
                <version>1.2.6-SNAPSHOT</version>
            </dependency>
            <!-- WebMVC 配置 -->
            <dependency>
                <groupId>com.fulfillmen.starter</groupId>
                <artifactId>fulfillmen-starter-web</artifactId>
                <version>1.2.6-SNAPSHOT</version>
            </dependency>
            <!-- json -->
            <dependency>
                <groupId>com.fulfillmen.starter</groupId>
                <artifactId>fulfillmen-starter-json-jackson</artifactId>
                <version>1.2.6-SNAPSHOT</version>
            </dependency>
            <!-- 数据访问模块 -->
            <dependency>
                <groupId>com.fulfillmen.starter</groupId>
                <artifactId>fulfillmen-starter-data-core</artifactId>
                <version>1.2.6-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.fulfillmen.starter</groupId>
                <artifactId>fulfillmen-starter-data-mp</artifactId>
                <version>1.2.6-SNAPSHOT</version>
            </dependency>
            <!-- 日志模块 -->
            <dependency>
                <groupId>com.fulfillmen.starter</groupId>
                <artifactId>fulfillmen-starter-log-aop</artifactId>
                <version>1.2.6-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.fulfillmen.starter</groupId>
                <artifactId>fulfillmen-starter-log-core</artifactId>
                <version>1.2.6-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.fulfillmen.starter</groupId>
                <artifactId>fulfillmen-starter-log-interceptor</artifactId>
                <version>1.2.6-SNAPSHOT</version>
            </dependency>
            <!-- 安全 -->
            <!-- 加密 -->
            <dependency>
                <groupId>com.fulfillmen.starter</groupId>
                <artifactId>fulfillmen-starter-security-crypto</artifactId>
                <version>1.2.6-SNAPSHOT</version>
            </dependency>
            <!--  限流 -->
            <dependency>
                <groupId>com.fulfillmen.starter</groupId>
                <artifactId>fulfillmen-starter-security-limiter</artifactId>
                <version>1.2.6-SNAPSHOT</version>
            </dependency>
            <!-- 脱敏 -->
            <dependency>
                <groupId>com.fulfillmen.starter</groupId>
                <artifactId>fulfillmen-starter-security-mask</artifactId>
                <version>1.2.6-SNAPSHOT</version>
            </dependency>
            <!-- 密码 -->
            <dependency>
                <groupId>com.fulfillmen.starter</groupId>
                <artifactId>fulfillmen-starter-security-password</artifactId>
                <version>1.2.6-SNAPSHOT</version>
            </dependency>

            <!-- 消息 -->
            <!-- 邮件组件 -->
            <dependency>
                <groupId>com.fulfillmen.starter</groupId>
                <artifactId>fulfillmen-starter-messaging-mail</artifactId>
                <version>1.2.6-SNAPSHOT</version>
            </dependency>

            <!-- websocket 通信 -->
            <dependency>
                <groupId>com.fulfillmen.starter</groupId>
                <artifactId>fulfillmen-starter-messaging-websocket</artifactId>
                <version>1.2.6-SNAPSHOT</version>
            </dependency>

            <!-- 存储模块 -->
            <dependency>
                <groupId>com.fulfillmen.starter</groupId>
                <artifactId>fulfillmen-starter-storage-core</artifactId>
                <version>1.2.6-SNAPSHOT</version>
            </dependency>
            <!-- 本地存储 -->
            <dependency>
                <groupId>com.fulfillmen.starter</groupId>
                <artifactId>fulfillmen-starter-storage-local</artifactId>
                <version>1.2.6-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
    </build>
</project>