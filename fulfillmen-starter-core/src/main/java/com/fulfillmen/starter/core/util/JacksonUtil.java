/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.starter.core.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import com.fulfillmen.starter.core.util.keyDeserialize.TripleKeyDeserializer;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.util.StringUtils;

/**
 * Jackson JSON 工具类 - 高性能、线程安全的 JSON 序列化/反序列化工具
 *
 * <p>
 * 本工具类基于 Jackson 库实现，提供了完整的 JSON 处理功能，包括对象序列化、
 * 反序列化、格式化输出、类型转换等。专门针对企业级应用进行了优化配置，
 * 支持 Java 8 时间类型、自定义序列化器、灵活的配置选项等高级功能。
 * </p>
 *
 * <p>
 * <strong>核心功能：</strong>
 * </p>
 * <ul>
 * <li><strong>对象序列化</strong> - 将Java对象转换为JSON字符串</li>
 * <li><strong>对象反序列化</strong> - 将JSON字符串转换为Java对象</li>
 * <li><strong>泛型支持</strong> - 支持复杂泛型类型的转换</li>
 * <li><strong>集合转换</strong> - 专门优化的集合类型转换</li>
 * <li><strong>时间处理</strong> - 完整支持Java 8时间API</li>
 * <li><strong>格式化输出</strong> - 美化JSON输出格式</li>
 * <li><strong>类型检查</strong> - JSON格式验证和类型判断</li>
 * <li><strong>自定义配置</strong> - 支持各种序列化特性配置</li>
 * </ul>
 *
 * <p>
 * <strong>预配置特性：</strong>
 * </p>
 * <ul>
 * <li><strong>时间格式统一</strong> - LocalDateTime: "yyyy-MM-dd HH:mm:ss"</li>
 * <li><strong>空值处理</strong> - 序列化时忽略null值</li>
 * <li><strong>未知属性容忍</strong> - 反序列化时忽略未知字段</li>
 * <li><strong>浮点数处理</strong> - 使用BigDecimal确保精度</li>
 * <li><strong>注释支持</strong> - 允许JSON中包含注释</li>
 * <li><strong>单引号支持</strong> - 支持单引号和不带引号的字段名</li>
 * <li><strong>自定义Map Key</strong> - 支持复杂对象作为Map键</li>
 * </ul>
 *
 * <pre>
 * <strong>基础使用示例：</strong>
 * 
 * // 1. 基本对象序列化/反序列化
 * User user = new User("张三", 25, "<EMAIL>");
 * String json = JacksonUtil.toJsonString(user);
 * User deserializedUser = JacksonUtil.convertToBean(json, User.class);
 * 
 * // 2. 时间对象处理
 * TimeInfo timeInfo = new TimeInfo();
 * timeInfo.setCreateTime(LocalDateTime.now());
 * timeInfo.setCreateDate(LocalDate.now());
 * String timeJson = JacksonUtil.toJsonString(timeInfo);
 * // 输出: {"createTime":"2024-01-15 14:30:25","createDate":"2024-01-15"}
 * 
 * // 3. 集合类型转换
 * List&lt;User&gt; userList = Arrays.asList(user1, user2, user3);
 * String listJson = JacksonUtil.toJsonString(userList);
 * List&lt;User&gt; deserializedList = JacksonUtil.convertToBean(
 * listJson, List.class, User.class
 * );
 * 
 * // 4. 泛型类型转换
 * ApiResponse&lt;List&lt;User&gt;&gt; response = new ApiResponse&lt;&gt;();
 * response.setData(userList);
 * String responseJson = JacksonUtil.toJsonString(response);
 * ApiResponse&lt;List&lt;User&gt;&gt; deserializedResponse = JacksonUtil.convertToBean(
 * responseJson, new TypeReference&lt;ApiResponse&lt;List&lt;User&gt;&gt;&gt;() {}
 * );
 * 
 * // 5. 格式化输出
 * String prettyJson = JacksonUtil.toJsonStringPretty(user);
 * // 输出格式化的JSON，便于阅读和调试
 * 
 * // 6. JSON格式验证
 * try {
 * boolean isObject = JacksonUtil.validJsonType(jsonString);
 * if (isObject) {
 * // 处理JSON对象
 * } else {
 * // 处理JSON数组
 * }
 * } catch (JsonProcessingException e) {
 * // JSON格式错误
 * }
 * </pre>
 *
 * <p>
 * <strong>高级功能示例：</strong>
 * </p>
 *
 * <pre>
 * // 1. 自定义序列化特性
 * String jsonWithFeature = JacksonUtil.toJsonString(
 * data,
 * SerializationFeature.WRITE_ENUMS_USING_TO_STRING
 * );
 * 
 * // 2. 复杂嵌套对象
 * Map&lt;String, Object&gt; complexData = new HashMap&lt;&gt;();
 * complexData.put("users", userList);
 * complexData.put("metadata", metadataMap);
 * complexData.put("timestamp", LocalDateTime.now());
 * String complexJson = JacksonUtil.toJsonString(complexData);
 * 
 * // 3. 条件序列化
 * // 使用@JsonInclude、@JsonIgnore等注解控制序列化行为
 * 
 * // 4. 自定义ObjectMapper配置
 * ObjectMapper customMapper = JacksonUtil.getObjectMapper();
 * customMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
 * </pre>
 *
 * <p>
 * <strong>企业级最佳实践：</strong>
 * </p>
 *
 * <pre>
 * // 1. API响应序列化
 * &#64;RestController
 * public class UserController {
 * &#64;GetMapping("/users/{id}")
 * public ResponseEntity&lt;String&gt; getUser(&#64;PathVariable Long id) {
 * User user = userService.findById(id);
 * ApiResponse&lt;User&gt; response = ApiResponse.success(user);
 * return ResponseEntity.ok(JacksonUtil.toJsonString(response));
 * }
 * }
 * 
 * // 2. 配置文件处理
 * &#64;Component
 * public class ConfigService {
 * public void saveConfig(Object config) {
 * String json = JacksonUtil.toJsonStringPretty(config);
 * Files.write(configPath, json.getBytes());
 * }
 * 
 * public &lt;T&gt; T loadConfig(Class&lt;T&gt; configClass) {
 * String json = Files.readString(configPath);
 * return JacksonUtil.convertToBean(json, configClass);
 * }
 * }
 * 
 * // 3. 缓存序列化
 * &#64;Service
 * public class CacheService {
 * public void cacheObject(String key, Object data) {
 * String json = JacksonUtil.toJsonString(data);
 * redisTemplate.set(key, json, Duration.ofHours(1));
 * }
 * 
 * public &lt;T&gt; T getCachedObject(String key, Class&lt;T&gt; clazz) {
 * String json = redisTemplate.get(key);
 * return JacksonUtil.convertToBean(json, clazz);
 * }
 * }
 * 
 * // 4. 日志结构化
 * &#64;Component
 * public class AuditLogger {
 * public void logOperation(String operation, Object data) {
 * AuditLog log = new AuditLog(operation, LocalDateTime.now(), data);
 * String logJson = JacksonUtil.toJsonString(log);
 * logger.info("AUDIT: {}", logJson);
 * }
 * }
 * </pre>
 *
 * <p>
 * <strong>性能优化建议：</strong>
 * </p>
 * <ul>
 * <li>大对象序列化时考虑使用流式处理</li>
 * <li>频繁序列化的对象可以考虑缓存JSON结果</li>
 * <li>复杂对象建议使用@JsonIgnore忽略不必要的字段</li>
 * <li>集合类型转换时预估容量，避免扩容开销</li>
 * <li>时间敏感场景可以使用数字时间戳代替字符串格式</li>
 * </ul>
 *
 * <p>
 * <strong>线程安全性：</strong>
 * </p>
 * <ul>
 * <li>内部的ObjectMapper实例是线程安全的</li>
 * <li>所有静态方法都是线程安全的</li>
 * <li>getObjectMapper()返回的是副本，可以安全修改配置</li>
 * <li>支持高并发环境下的使用</li>
 * </ul>
 *
 * <p>
 * <strong>注意事项：</strong>
 * </p>
 * <ul>
 * <li>序列化失败时返回null，注意null值检查</li>
 * <li>时间格式固定为"yyyy-MM-dd HH:mm:ss"，如需其他格式请自定义</li>
 * <li>循环引用的对象会导致序列化失败</li>
 * <li>BigDecimal用于浮点数，确保精度但可能影响性能</li>
 * <li>自定义ObjectMapper配置时注意线程安全</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 * @see com.fasterxml.jackson.databind.ObjectMapper
 * @see com.fasterxml.jackson.core.type.TypeReference
 * @see com.fasterxml.jackson.databind.SerializationFeature
 * @see com.fasterxml.jackson.databind.DeserializationFeature
 * @see com.fulfillmen.starter.core.util.keyDeserialize.TripleKeyDeserializer
 */
@Slf4j
public final class JacksonUtil {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    static {
        // 统一日期格式yyyy-MM-dd HH:mm:ss
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DateTimeFormatter
            .ofPattern("yyyy-MM-dd HH:mm:ss")));
        javaTimeModule.addSerializer(LocalDate.class, new LocalDateSerializer(DateTimeFormatter
            .ofPattern("yyyy-MM-dd")));
        javaTimeModule.addSerializer(LocalTime.class, new LocalTimeSerializer(DateTimeFormatter.ofPattern("HH:mm:ss")));
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(DateTimeFormatter
            .ofPattern("yyyy-MM-dd HH:mm:ss")));
        javaTimeModule.addDeserializer(LocalDate.class, new LocalDateDeserializer(DateTimeFormatter
            .ofPattern("yyyy-MM-dd")));
        javaTimeModule.addDeserializer(LocalTime.class, new LocalTimeDeserializer(DateTimeFormatter
            .ofPattern("HH:mm:ss")));

        // 增加map key解析类型
        SimpleModule simpleTypeModule = new SimpleModule();
        simpleTypeModule.addKeyDeserializer(Triple.class, new TripleKeyDeserializer());

        OBJECT_MAPPER
            // 允许json字符串中带注释
            .configure(JsonParser.Feature.ALLOW_COMMENTS, true)
            // 允许json字段名不被引号包括起来
            .configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true)
            // 允许json字段名使用单引号包括起来
            .configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true)
            // .configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true)
            // 将json中的浮点数解析成BigDecimal对象，禁用后会解析成Double对象
            .configure(DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS, true)
            // jackson默认开启遇到未知属性需要抛异常，因此如要和fastjson保持一致则需要关闭该特性
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            // 建议关闭，排序会影响序列化性能（fastjson在反序列化时支持按照field顺序读取解析，因此排序后的json串有利于提高fastjson的解析性能，但jackson并没有该特性）
            // .configure(MapperFeature.SORT_PROPERTIES_ALPHABETICALLY, true)
            .configure(MapperFeature.PROPAGATE_TRANSIENT_MARKER, true)
            .registerModule(javaTimeModule)
            .registerModule(simpleTypeModule)
            .setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    /**
     * 提供外部使用的 ObjectMapper 实例。
     * 
     * <pre>
     * 注意：此方法返回的是副本实例，外部可以安全地修改配置而不影响工具类内部行为。
     * 内部的共享实例配置已预设为最佳实践配置。
     * </pre>
     * 
     * @return 配置好的 ObjectMapper 副本实例
     */
    public static ObjectMapper getObjectMapper() {
        return OBJECT_MAPPER.copy();
    }

    /**
     * Object转json字符串
     * 
     * <pre>
     * String 类型默认不转换，直接返回
     * </pre>
     *
     * @param obj obj
     * @param <T> 返回类型
     * @return Json字符串 ，如果 obj 为 null 则返回 null
     */
    public static String toJsonString(Object obj) {
        if (Objects.isNull(obj)) {
            return null;
        }
        try {
            if (obj instanceof String) {
                return (String)obj;
            }
            return OBJECT_MAPPER.writeValueAsString(obj);
        } catch (Exception e) {
            String className = obj != null ? obj.getClass().getSimpleName() : "null";
            if (className.isEmpty()) {
                className = obj.getClass().getName(); // 对于匿名类使用完整类名
            }
            log.warn("JSON序列化异常, obj: {}", className, e);
            return null;
        }
    }

    /**
     * Object转json字符串
     * 
     * <pre>
     * String 类型默认不转换，直接返回
     * </pre>
     *
     * @param obj     object
     * @param feature 反序列化特性 {@link DeserializationFeature}
     * @return Json字符串 ，如果 obj 为 null 则返回 null
     */
    public static String toJsonString(Object obj, DeserializationFeature feature) {
        if (Objects.isNull(obj)) {
            return null;
        }
        try {
            if (obj instanceof String) {
                return (String)obj;
            }
            // 使用副本避免修改共享实例，确保线程安全
            ObjectMapper mapper = OBJECT_MAPPER.copy().enable(feature);
            return mapper.writeValueAsString(obj);
        } catch (Exception e) {
            String className = obj != null ? obj.getClass().getSimpleName() : "null";
            if (className.isEmpty()) {
                className = obj.getClass().getName(); // 对于匿名类使用完整类名
            }
            log.warn("JSON序列化异常(DeserializationFeature), obj: {}", className, e);
            return null;
        }
    }

    /**
     * Object转json字符串
     * 
     * <pre>
     * String 类型默认不转换，直接返回
     * </pre>
     *
     * @param obj     object
     * @param feature 序列化特性 {@link SerializationFeature}
     * @return Json字符串 ，如果 obj 为 null 则返回 null
     */
    public static String toJsonString(Object obj, SerializationFeature feature) {
        if (Objects.isNull(obj)) {
            return null;
        }
        try {
            if (obj instanceof String) {
                return (String)obj;
            }
            // 使用副本避免修改共享实例，确保线程安全
            ObjectMapper mapper = OBJECT_MAPPER.copy().enable(feature);
            return mapper.writeValueAsString(obj);
        } catch (Exception e) {
            String className = obj != null ? obj.getClass().getSimpleName() : "null";
            if (className.isEmpty()) {
                className = obj.getClass().getName(); // 对于匿名类使用完整类名
            }
            log.warn("JSON序列化异常(SerializationFeature), obj: {}", className, e);
            return null;
        }
    }

    /**
     * Object转json字符串
     * 
     * <pre>
     * String 类型默认不转换，直接返回
     * </pre>
     *
     * @param obj     object
     * @param feature Mapper特性 {@link MapperFeature}
     * @return Json字符串 ，如果 obj 为 null 则返回 null
     */
    public static String toJsonString(Object obj, MapperFeature feature) {
        if (Objects.isNull(obj)) {
            return null;
        }
        try {
            if (obj instanceof String) {
                return (String)obj;
            }
            // 使用副本避免修改共享实例，确保线程安全
            ObjectMapper mapper = OBJECT_MAPPER.copy().enable(feature);
            return mapper.writeValueAsString(obj);
        } catch (Exception e) {
            String className = obj != null ? obj.getClass().getSimpleName() : "null";
            if (className.isEmpty()) {
                className = obj.getClass().getName(); // 对于匿名类使用完整类名
            }
            log.warn("JSON序列化异常(MapperFeature), obj: {}", className, e);
            return null;
        }
    }

    /**
     * Object转json字符串并格式化美化
     * 
     * <pre>
     * String 类型默认不转换，直接返回
     * </pre>
     *
     * @param obj object
     * @return Json字符串并格式化
     */
    public static String toJsonStringPretty(Object obj) {
        if (Objects.isNull(obj)) {
            return null;
        }
        try {
            if (obj instanceof String) {
                return (String)obj;
            }
            return OBJECT_MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(obj);
        } catch (Exception e) {
            String className = obj != null ? obj.getClass().getSimpleName() : "null";
            if (className.isEmpty()) {
                className = obj.getClass().getName(); // 对于匿名类使用完整类名
            }
            log.warn("JSON格式化序列化异常, obj: {}", className, e);
            return null;
        }
    }

    /**
     * string转Bean
     *
     * @param str   json字符串
     * @param clazz 被转对象class
     * @param <T>   返回类型
     * @return Bean
     */
    @SuppressWarnings("unchecked")
    public static <T> T convertToBean(String str, Class<T> clazz) {
        if (!StringUtils.hasText(str) || clazz == null) {
            return null;
        }
        try {
            if (String.class.isAssignableFrom(clazz)) {
                return (T)str;
            }
            return OBJECT_MAPPER.readValue(str, clazz);
        } catch (IOException e) {
            log.warn("JSON反序列化异常, str: {}, clazz: {}", str, clazz.getSimpleName(), e);
            return null;
        }
    }

    /**
     * string转object 此方法可以，转换泛型对象的包装类 例如 , ResponseUtils<T>
     * 
     * <pre>
     * example :
     * <code>
     * ResponseUtils<String> responseUtil = JacksonUtil.convertToBean(result, new TypeReference<ResponseUtils<String>>() {});
     * List<User> userListObj = JacksonUtil.convertToBean(userListStr, new TypeReference<List<User>>() {});
     * </code>
     * </pre>
     *
     * @param str           json字符串
     * @param typeReference 被转对象引用类型
     * @param <T>           返回类型
     * @return Bean
     */
    @SuppressWarnings("unchecked")
    public static <T> T convertToBean(String str, TypeReference<T> typeReference) {
        if (!StringUtils.hasText(str) || typeReference == null) {
            return null;
        }
        try {
            // 修复：检查实际的目标类型，而不是TypeReference类型
            if (typeReference.getType().equals(String.class)) {
                return (T)str;
            }
            return OBJECT_MAPPER.readValue(str, typeReference);
        } catch (IOException e) {
            log.warn("JSON转换异常, str: {}, typeReference: {}", str, typeReference.getType(), e);
            return null;
        }
    }

    /**
     * string转object 用于转为集合对象
     * 
     * <pre>
     * 此方法转换 可转换 集合对象
     * example: <code> final List<User> list = JacksonUtil.convertToBean(userListStr, List.class, User.class);</code>
     * </pre>
     *
     * @param str             json字符串
     * @param collectionClass 集合类型 例如: List.class
     * @param elementClasses  集合中的对象类型 例如 User.class
     * @param <T>             返回类型 List<User>
     * @return JavaBean List<User>
     */
    public static <T> T convertToBean(String str,
                                      Class<? extends Collection> collectionClass,
                                      Class<?> elementClasses) {
        // 添加参数验证
        if (!StringUtils.hasText(str) || collectionClass == null || elementClasses == null) {
            return null;
        }

        JavaType javaType = OBJECT_MAPPER.getTypeFactory().constructParametricType(collectionClass, elementClasses);
        try {
            return OBJECT_MAPPER.readValue(str, javaType);
        } catch (IOException e) {
            // 统一异常处理级别和格式
            log.warn("JSON集合转换异常, str: {}, collectionClass: {}, elementClass: {}", str, collectionClass
                .getSimpleName(), elementClasses.getSimpleName(), e);
            return null;
        }
    }

    /**
     * 检查JSON字符串的根节点类型
     *
     * <pre>
     * 返回值说明：
     * - true: JSON对象 (如: {"key": "value"})
     * - false: JSON数组 (如: [1, 2, 3])
     * - 抛异常: JSON格式不正确
     * </pre>
     *
     * @param json JSON字符串
     * @return true表示JSON对象，false表示JSON数组
     * @throws JsonProcessingException 当JSON格式不正确时抛出
     */
    public static boolean validJsonType(String json) throws JsonProcessingException {
        if (!StringUtils.hasText(json)) {
            throw new JsonProcessingException("JSON字符串不能为空") {
            };
        }
        try {
            var jsonNode = OBJECT_MAPPER.readTree(json);
            return !jsonNode.isArray();
        } catch (JsonProcessingException e) {
            log.error("Json格式异常。json: {}", json, e);
            throw e;
        }
    }

    /**
     * 判断类型是否是Java包装类型
     * 
     * <pre>
     * true 是包装类型（如Integer、Boolean等）
     * false 不是包装类型
     * </pre>
     *
     * @param clz Class
     * @return true 是包装类型 false 不是包装类型
     */
    public static boolean isWrapClass(Class<?> clz) {
        return clz == Boolean.class || clz == Character.class || clz == Byte.class || clz == Short.class || clz == Integer.class || clz == Long.class || clz == Float.class || clz == Double.class || clz == Void.class;
    }

}
