 /*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.starter.core.util;

import static org.junit.jupiter.api.Assertions.*;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.BeforeEach;

/**
 * JacksonUtil 测试类
 *
 * <AUTHOR>
 * @date 2025/01/07
 * @description JacksonUtil 的单元测试
 * @since 1.0.0
 */
@DisplayName("JacksonUtil 测试")
class JacksonUtilTest {

    // 测试用的实体类
    static class TestUser {
        private String name;
        private Integer age;
        private LocalDateTime createTime;
        private BigDecimal amount;

        public TestUser() {}

        public TestUser(String name, Integer age, LocalDateTime createTime, BigDecimal amount) {
            this.name = name;
            this.age = age;
            this.createTime = createTime;
            this.amount = amount;
        }

        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public Integer getAge() { return age; }
        public void setAge(Integer age) { this.age = age; }
        public LocalDateTime getCreateTime() { return createTime; }
        public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }
        public BigDecimal getAmount() { return amount; }
        public void setAmount(BigDecimal amount) { this.amount = amount; }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            TestUser testUser = (TestUser) obj;
            return java.util.Objects.equals(name, testUser.name) &&
                   java.util.Objects.equals(age, testUser.age) &&
                   java.util.Objects.equals(createTime, testUser.createTime) &&
                   java.util.Objects.equals(amount, testUser.amount);
        }
    }

    private TestUser testUser;
    private List<TestUser> testUserList;

    @BeforeEach
    void setUp() {
        testUser = new TestUser("张三", 25, 
            LocalDateTime.of(2025, 1, 7, 10, 30, 0), 
            new BigDecimal("1000.50"));
        
        testUserList = new ArrayList<>();
        testUserList.add(testUser);
        testUserList.add(new TestUser("李四", 30, 
            LocalDateTime.of(2025, 1, 8, 9, 15, 30), 
            new BigDecimal("2000.75")));
    }

    @Nested
    @DisplayName("getObjectMapper 方法测试")
    class GetObjectMapperTest {

        @Test
        @DisplayName("返回的是副本实例，不影响内部配置")
        void testGetObjectMapperReturnsCopy() {
            ObjectMapper mapper1 = JacksonUtil.getObjectMapper();
            ObjectMapper mapper2 = JacksonUtil.getObjectMapper();
            
            // 验证返回的是不同实例
            assertNotSame(mapper1, mapper2);
            
            // 修改其中一个实例的配置
            mapper1.configure(SerializationFeature.INDENT_OUTPUT, true);
            
            // 验证另一个实例配置未被影响
            assertFalse(mapper2.isEnabled(SerializationFeature.INDENT_OUTPUT));
            
            // 验证工具类内部方法仍正常工作（内部配置未被破坏）
            String json = JacksonUtil.toJsonString(testUser);
            assertNotNull(json);
            assertFalse(json.contains("\n")); // 验证内部实例没有开启格式化
        }

        @Test
        @DisplayName("返回的ObjectMapper配置正确")
        void testObjectMapperConfiguration() {
            ObjectMapper mapper = JacksonUtil.getObjectMapper();
            
            // 验证关键配置
            assertFalse(mapper.isEnabled(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES));
            assertTrue(mapper.isEnabled(DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS));
        }
    }

    @Nested
    @DisplayName("toJsonString 基础方法测试")
    class ToJsonStringBasicTest {

        @Test
        @DisplayName("正常对象序列化")
        void testToJsonStringWithValidObject() {
            String json = JacksonUtil.toJsonString(testUser);
            
            assertNotNull(json);
            assertTrue(json.contains("\"name\":\"张三\""));
            assertTrue(json.contains("\"age\":25"));
            assertTrue(json.contains("\"createTime\":\"2025-01-07 10:30:00\""));
            assertTrue(json.contains("\"amount\":1000.50"));
        }

        @Test
        @DisplayName("String类型直接返回")
        void testToJsonStringWithString() {
            String input = "Hello World";
            String result = JacksonUtil.toJsonString(input);
            
            assertEquals(input, result);
        }

        @Test
        @DisplayName("null值处理")
        void testToJsonStringWithNull() {
            String result = JacksonUtil.toJsonString(null);
            assertNull(result);
        }

        @Test
        @DisplayName("复杂对象序列化")
        void testToJsonStringWithComplexObject() {
            String json = JacksonUtil.toJsonString(testUserList);
            
            assertNotNull(json);
            assertTrue(json.startsWith("["));
            assertTrue(json.endsWith("]"));
            assertTrue(json.contains("张三"));
            assertTrue(json.contains("李四"));
        }
    }

    @Nested
    @DisplayName("toJsonString 特性配置测试")
    class ToJsonStringWithFeaturesTest {

        @Test
        @DisplayName("DeserializationFeature配置测试")
        void testToJsonStringWithDeserializationFeature() {
            String json = JacksonUtil.toJsonString(testUser, DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);
            
            assertNotNull(json);
            assertTrue(json.contains("张三"));
        }

        @Test
        @DisplayName("SerializationFeature配置测试")
        void testToJsonStringWithSerializationFeature() {
            String json = JacksonUtil.toJsonString(testUser, SerializationFeature.INDENT_OUTPUT);
            
            assertNotNull(json);
            assertTrue(json.contains("\n")); // 验证格式化输出
        }

        @Test
        @DisplayName("MapperFeature配置测试")
        void testToJsonStringWithMapperFeature() {
            String json = JacksonUtil.toJsonString(testUser, MapperFeature.SORT_PROPERTIES_ALPHABETICALLY);
            
            assertNotNull(json);
            assertTrue(json.contains("张三"));
        }

        @Test
        @DisplayName("特性配置方法的线程安全性")
        void testFeatureMethodsThreadSafety() throws InterruptedException {
            int threadCount = 10;
            CountDownLatch latch = new CountDownLatch(threadCount);
            ExecutorService executor = Executors.newFixedThreadPool(threadCount);
            AtomicInteger successCount = new AtomicInteger(0);

            for (int i = 0; i < threadCount; i++) {
                executor.submit(() -> {
                    try {
                        // 并发调用带特性配置的方法
                        String json1 = JacksonUtil.toJsonString(testUser, SerializationFeature.INDENT_OUTPUT);
                        String json2 = JacksonUtil.toJsonString(testUser, DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);
                        String json3 = JacksonUtil.toJsonString(testUser, MapperFeature.SORT_PROPERTIES_ALPHABETICALLY);
                        
                        if (json1 != null && json2 != null && json3 != null) {
                            successCount.incrementAndGet();
                        }
                    } catch (Exception e) {
                        // 记录异常但不停止测试
                        e.printStackTrace();
                    } finally {
                        latch.countDown();
                    }
                });
            }

            latch.await();
            executor.shutdown();
            
            // 验证所有线程都成功执行
            assertEquals(threadCount, successCount.get());
        }
    }

    @Nested
    @DisplayName("toJsonStringPretty 方法测试")
    class ToJsonStringPrettyTest {

        @Test
        @DisplayName("格式化输出测试")
        void testToJsonStringPretty() {
            String json = JacksonUtil.toJsonStringPretty(testUser);
            
            assertNotNull(json);
            assertTrue(json.contains("\n"));
            assertTrue(json.contains("  "));
            assertTrue(json.contains("张三"));
        }

        @Test
        @DisplayName("String类型直接返回")
        void testToJsonStringPrettyWithString() {
            String input = "Hello World";
            String result = JacksonUtil.toJsonStringPretty(input);
            
            assertEquals(input, result);
        }

        @Test
        @DisplayName("null值处理")
        void testToJsonStringPrettyWithNull() {
            String result = JacksonUtil.toJsonStringPretty(null);
            assertNull(result);
        }
    }

    @Nested
    @DisplayName("convertToBean 方法测试")
    class ConvertToBeanTest {

        @Test
        @DisplayName("正常JSON转Bean")
        void testConvertToBeanWithValidJson() {
            String json = "{\"name\":\"张三\",\"age\":25,\"createTime\":\"2025-01-07 10:30:00\",\"amount\":1000.50}";
            
            TestUser result = JacksonUtil.convertToBean(json, TestUser.class);
            
            assertNotNull(result);
            assertEquals("张三", result.getName());
            assertEquals(25, result.getAge());
            assertEquals(LocalDateTime.of(2025, 1, 7, 10, 30, 0), result.getCreateTime());
            assertEquals(new BigDecimal("1000.50"), result.getAmount());
        }

        @Test
        @DisplayName("String类型转换")
        void testConvertToBeanWithStringType() {
            String input = "Hello World";
            String result = JacksonUtil.convertToBean(input, String.class);
            
            assertEquals(input, result);
        }

        @Test
        @DisplayName("空字符串处理")
        void testConvertToBeanWithEmptyString() {
            TestUser result = JacksonUtil.convertToBean("", TestUser.class);
            assertNull(result);
        }

        @Test
        @DisplayName("null值处理")
        void testConvertToBeanWithNull() {
            TestUser result = JacksonUtil.convertToBean(null, TestUser.class);
            assertNull(result);
        }

        @Test
        @DisplayName("无效JSON处理")
        void testConvertToBeanWithInvalidJson() {
            TestUser result = JacksonUtil.convertToBean("{invalid json}", TestUser.class);
            assertNull(result);
        }
    }

    @Nested
    @DisplayName("convertToBean TypeReference 方法测试")
    class ConvertToBeanTypeReferenceTest {

        @Test
        @DisplayName("泛型List转换")
        void testConvertToBeanWithTypeReference() {
            String json = "[{\"name\":\"张三\",\"age\":25,\"createTime\":\"2025-01-07 10:30:00\",\"amount\":1000.50}," +
                         "{\"name\":\"李四\",\"age\":30,\"createTime\":\"2025-01-08 09:15:30\",\"amount\":2000.75}]";
            
            List<TestUser> result = JacksonUtil.convertToBean(json, new TypeReference<List<TestUser>>() {});
            
            assertNotNull(result);
            assertEquals(2, result.size());
            assertEquals("张三", result.get(0).getName());
            assertEquals("李四", result.get(1).getName());
        }

        @Test
        @DisplayName("Map转换")
        void testConvertToBeanWithMapTypeReference() {
            String json = "{\"key1\":\"value1\",\"key2\":\"value2\"}";
            
            Map<String, String> result = JacksonUtil.convertToBean(json, new TypeReference<Map<String, String>>() {});
            
            assertNotNull(result);
            assertEquals(2, result.size());
            assertEquals("value1", result.get("key1"));
            assertEquals("value2", result.get("key2"));
        }

        @Test
        @DisplayName("String类型TypeReference - 验证修复")
        void testConvertToBeanWithStringTypeReference() {
            String input = "Hello World";
            String result = JacksonUtil.convertToBean(input, new TypeReference<String>() {});
            
            assertEquals(input, result);
        }

        @Test
        @DisplayName("null值处理")
        void testConvertToBeanTypeReferenceWithNull() {
            List<TestUser> result = JacksonUtil.convertToBean(null, new TypeReference<List<TestUser>>() {});
            assertNull(result);
        }
    }

    @Nested
    @DisplayName("convertToBean 集合方法测试")
    class ConvertToBeanCollectionTest {

        @Test
        @DisplayName("List集合转换")
        void testConvertToBeanWithCollection() {
            String json = "[{\"name\":\"张三\",\"age\":25,\"createTime\":\"2025-01-07 10:30:00\",\"amount\":1000.50}]";
            
            List<TestUser> result = JacksonUtil.convertToBean(json, List.class, TestUser.class);
            
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals("张三", result.get(0).getName());
        }

        @Test
        @DisplayName("参数验证 - 空字符串")
        void testConvertToBeanCollectionWithEmptyString() {
            List<TestUser> result = JacksonUtil.convertToBean("", List.class, TestUser.class);
            assertNull(result);
        }

        @Test
        @DisplayName("参数验证 - null集合类型")
        void testConvertToBeanCollectionWithNullCollectionClass() {
            String json = "[{\"name\":\"张三\"}]";
            List<TestUser> result = JacksonUtil.convertToBean(json, null, TestUser.class);
            assertNull(result);
        }

        @Test
        @DisplayName("参数验证 - null元素类型")
        void testConvertToBeanCollectionWithNullElementClass() {
            String json = "[{\"name\":\"张三\"}]";
            List<TestUser> result = JacksonUtil.convertToBean(json, List.class, null);
            assertNull(result);
        }
    }

    @Nested
    @DisplayName("validJsonType 方法测试")
    class ValidJsonTypeTest {

        @Test
        @DisplayName("JSON对象类型验证")
        void testValidJsonTypeWithObject() throws JsonProcessingException {
            String jsonObject = "{\"key\":\"value\"}";
            assertTrue(JacksonUtil.validJsonType(jsonObject));
        }

        @Test
        @DisplayName("JSON数组类型验证")
        void testValidJsonTypeWithArray() throws JsonProcessingException {
            String jsonArray = "[1,2,3]";
            assertFalse(JacksonUtil.validJsonType(jsonArray));
        }

        @Test
        @DisplayName("空字符串异常")
        void testValidJsonTypeWithEmptyString() {
            assertThrows(JsonProcessingException.class, () -> {
                JacksonUtil.validJsonType("");
            });
        }

        @Test
        @DisplayName("null值异常")
        void testValidJsonTypeWithNull() {
            assertThrows(JsonProcessingException.class, () -> {
                JacksonUtil.validJsonType(null);
            });
        }

        @Test
        @DisplayName("无效JSON异常")
        void testValidJsonTypeWithInvalidJson() {
            assertThrows(JsonProcessingException.class, () -> {
                JacksonUtil.validJsonType("{invalid json}");
            });
        }

        @Test
        @DisplayName("复杂JSON对象")
        void testValidJsonTypeWithComplexObject() throws JsonProcessingException {
            String complexJson = "{\"user\":{\"name\":\"张三\",\"age\":25},\"items\":[1,2,3]}";
            assertTrue(JacksonUtil.validJsonType(complexJson));
        }
    }

    @Nested
    @DisplayName("isWrapClass 方法测试")
    class IsWrapClassTest {

        @Test
        @DisplayName("包装类型验证")
        void testIsWrapClassWithWrapperTypes() {
            assertTrue(JacksonUtil.isWrapClass(Boolean.class));
            assertTrue(JacksonUtil.isWrapClass(Character.class));
            assertTrue(JacksonUtil.isWrapClass(Byte.class));
            assertTrue(JacksonUtil.isWrapClass(Short.class));
            assertTrue(JacksonUtil.isWrapClass(Integer.class));
            assertTrue(JacksonUtil.isWrapClass(Long.class));
            assertTrue(JacksonUtil.isWrapClass(Float.class));
            assertTrue(JacksonUtil.isWrapClass(Double.class));
            assertTrue(JacksonUtil.isWrapClass(Void.class));
        }

        @Test
        @DisplayName("非包装类型验证")
        void testIsWrapClassWithNonWrapperTypes() {
            assertFalse(JacksonUtil.isWrapClass(String.class));
            assertFalse(JacksonUtil.isWrapClass(Object.class));
            assertFalse(JacksonUtil.isWrapClass(TestUser.class));
            assertFalse(JacksonUtil.isWrapClass(List.class));
            assertFalse(JacksonUtil.isWrapClass(int.class)); // 原始类型
            assertFalse(JacksonUtil.isWrapClass(boolean.class)); // 原始类型
        }

        @Test
        @DisplayName("null值处理")
        void testIsWrapClassWithNull() {
            assertFalse(JacksonUtil.isWrapClass(null));
        }
    }

    @Nested
    @DisplayName("时间类型序列化测试")
    class DateTimeSerializationTest {

        @Test
        @DisplayName("LocalDateTime序列化格式")
        void testLocalDateTimeSerialization() {
            LocalDateTime dateTime = LocalDateTime.of(2025, 1, 7, 10, 30, 45);
            String json = JacksonUtil.toJsonString(dateTime);
            
            assertEquals("\"2025-01-07 10:30:45\"", json);
        }

        @Test
        @DisplayName("LocalDate序列化格式")
        void testLocalDateSerialization() {
            LocalDate date = LocalDate.of(2025, 1, 7);
            String json = JacksonUtil.toJsonString(date);
            
            assertEquals("\"2025-01-07\"", json);
        }

        @Test
        @DisplayName("LocalTime序列化格式")
        void testLocalTimeSerialization() {
            LocalTime time = LocalTime.of(10, 30, 45);
            String json = JacksonUtil.toJsonString(time);
            
            assertEquals("\"10:30:45\"", json);
        }

        @Test
        @DisplayName("时间类型反序列化")
        void testDateTimeDeserialization() {
            String json = "\"2025-01-07 10:30:45\"";
            LocalDateTime result = JacksonUtil.convertToBean(json, LocalDateTime.class);
            
            assertEquals(LocalDateTime.of(2025, 1, 7, 10, 30, 45), result);
        }
    }

    @Nested
    @DisplayName("BigDecimal处理测试")
    class BigDecimalTest {

        @Test
        @DisplayName("BigDecimal序列化")
        void testBigDecimalSerialization() {
            BigDecimal amount = new BigDecimal("1000.567");
            String json = JacksonUtil.toJsonString(amount);
            
            assertEquals("1000.567", json);
        }

        @Test
        @DisplayName("浮点数转BigDecimal")
        void testFloatToBigDecimal() {
            String json = "{\"amount\":1000.567}";
            TestUser user = JacksonUtil.convertToBean(json, TestUser.class);
            
            assertNotNull(user.getAmount());
            assertTrue(user.getAmount() instanceof BigDecimal);
        }
    }

    @Nested
    @DisplayName("特殊数据类型测试")
    class SpecialDataTypesTest {

        enum TestEnum {
            VALUE1, VALUE2, VALUE3
        }

        static class EnumTestClass {
            private TestEnum enumValue;
            public TestEnum getEnumValue() { return enumValue; }
            public void setEnumValue(TestEnum enumValue) { this.enumValue = enumValue; }
        }

        @Test
        @DisplayName("枚举类型序列化反序列化")
        void testEnumSerialization() {
            EnumTestClass obj = new EnumTestClass();
            obj.setEnumValue(TestEnum.VALUE2);
            
            String json = JacksonUtil.toJsonString(obj);
            assertNotNull(json);
            assertTrue(json.contains("\"VALUE2\""));
            
            EnumTestClass result = JacksonUtil.convertToBean(json, EnumTestClass.class);
            assertNotNull(result);
            assertEquals(TestEnum.VALUE2, result.getEnumValue());
        }

        @Test
        @DisplayName("数组类型处理")
        void testArraySerialization() {
            int[] intArray = {1, 2, 3, 4, 5};
            String json = JacksonUtil.toJsonString(intArray);
            
            assertNotNull(json);
            assertEquals("[1,2,3,4,5]", json);
            
            int[] result = JacksonUtil.convertToBean(json, int[].class);
            assertArrayEquals(intArray, result);
        }

        @Test
        @DisplayName("特殊字符处理")
        void testSpecialCharacters() {
            TestUser user = new TestUser("测试\"引号\"\n换行\t制表符", 25, 
                LocalDateTime.of(2025, 1, 7, 10, 30, 0), 
                new BigDecimal("1000.50"));
            
            String json = JacksonUtil.toJsonString(user);
            assertNotNull(json);
            assertTrue(json.contains("\\\""));  // 引号被转义
            assertTrue(json.contains("\\n"));   // 换行符被转义
            assertTrue(json.contains("\\t"));   // 制表符被转义
            
            TestUser result = JacksonUtil.convertToBean(json, TestUser.class);
            assertNotNull(result);
            assertEquals("测试\"引号\"\n换行\t制表符", result.getName());
        }
    }

    @Nested
    @DisplayName("配置验证测试")
    class ConfigurationTest {

        @Test
        @DisplayName("验证NON_NULL配置生效")
        void testNonNullConfiguration() {
            TestUser user = new TestUser();
            user.setName("张三");
            // age、createTime、amount 为 null
            
            String json = JacksonUtil.toJsonString(user);
            assertNotNull(json);
            
            // 验证null字段不被序列化
            assertFalse(json.contains("\"age\":null"));
            assertFalse(json.contains("\"createTime\":null"));
            assertFalse(json.contains("\"amount\":null"));
            assertTrue(json.contains("\"name\":\"张三\""));
        }

        @Test
        @DisplayName("验证FAIL_ON_UNKNOWN_PROPERTIES配置")
        void testUnknownPropertiesHandling() {
            String jsonWithUnknownField = "{\"name\":\"张三\",\"age\":25,\"unknownField\":\"test\"}";
            
            // 应该能正常反序列化，忽略未知字段
            TestUser result = JacksonUtil.convertToBean(jsonWithUnknownField, TestUser.class);
            assertNotNull(result);
            assertEquals("张三", result.getName());
            assertEquals(25, result.getAge());
        }

        @Test
        @DisplayName("验证BigDecimal配置")
        void testBigDecimalConfiguration() {
            String jsonWithFloat = "{\"name\":\"张三\",\"amount\":123.456}";
            
            TestUser result = JacksonUtil.convertToBean(jsonWithFloat, TestUser.class);
            assertNotNull(result);
            assertNotNull(result.getAmount());
            assertTrue(result.getAmount() instanceof BigDecimal);
            assertEquals(new BigDecimal("123.456"), result.getAmount());
        }
    }

    @Nested
    @DisplayName("边界和压力测试")
    class BoundaryAndStressTest {

        @Test
        @DisplayName("深层嵌套对象")
        void testDeepNestedObject() {
            // 创建深层嵌套结构
            Map<String, Object> level1 = new java.util.HashMap<>();
            Map<String, Object> level2 = new java.util.HashMap<>();
            Map<String, Object> level3 = new java.util.HashMap<>();
            
            level3.put("value", "deep_value");
            level2.put("level3", level3);
            level1.put("level2", level2);
            
            String json = JacksonUtil.toJsonString(level1);
            assertNotNull(json);
            assertTrue(json.contains("deep_value"));
            
            @SuppressWarnings("unchecked")
            Map<String, Object> result = JacksonUtil.convertToBean(json, Map.class);
            assertNotNull(result);
        }

        @Test
        @DisplayName("大数据量处理")
        void testLargeDataProcessing() {
            // 创建包含1000个元素的列表
            List<TestUser> largeList = new ArrayList<>();
            for (int i = 0; i < 1000; i++) {
                largeList.add(new TestUser("User" + i, i, 
                    LocalDateTime.now(), new BigDecimal(i * 100)));
            }
            
            String json = JacksonUtil.toJsonString(largeList);
            assertNotNull(json);
            assertTrue(json.length() > 10000); // 确保生成了大量数据
            
            List<TestUser> result = JacksonUtil.convertToBean(json, 
                new TypeReference<List<TestUser>>() {});
            assertNotNull(result);
            assertEquals(1000, result.size());
            assertEquals("User999", result.get(999).getName());
        }

        @Test
        @DisplayName("空集合和空对象处理")
        void testEmptyCollectionsAndObjects() {
            List<TestUser> emptyList = new ArrayList<>();
            String json = JacksonUtil.toJsonString(emptyList);
            assertEquals("[]", json);
            
            Map<String, String> emptyMap = new java.util.HashMap<>();
            String mapJson = JacksonUtil.toJsonString(emptyMap);
            assertEquals("{}", mapJson);
        }
    }

    @Nested
    @DisplayName("错误处理和日志测试")
    class ErrorHandlingTest {

        @Test
        @DisplayName("序列化异常处理")
        void testSerializationErrorHandling() {
            // 创建一个会导致序列化失败的对象
            Object problematicObject = new Object() {
                @SuppressWarnings("unused")
                public String getValue() {
                    throw new RuntimeException("Intentional error");
                }
            };
            
            String result = JacksonUtil.toJsonString(problematicObject);
            // 异常被捕获，返回null
            assertNull(result);
        }

        @Test
        @DisplayName("反序列化异常处理")
        void testDeserializationErrorHandling() {
            String invalidJson = "{\"age\":\"not_a_number\"}";
            TestUser result = JacksonUtil.convertToBean(invalidJson, TestUser.class);
            
            // 异常被捕获，返回null
            assertNull(result);
        }

        @Test
        @DisplayName("各种JSON格式错误")
        void testVariousJsonErrors() {
            String[] invalidJsons = {
                "{", // 不完整的JSON
                "{key:value}", // 无引号的key
                "{\"key\":}", // 缺少value
                "[1,2,]", // 多余的逗号
                "undefined", // 无效的值
                "{\"key\": 1 2}", // 语法错误
            };
            
            for (String invalidJson : invalidJsons) {
                TestUser result = JacksonUtil.convertToBean(invalidJson, TestUser.class);
                assertNull(result, "Should return null for invalid JSON: " + invalidJson);
            }
        }
    }
}