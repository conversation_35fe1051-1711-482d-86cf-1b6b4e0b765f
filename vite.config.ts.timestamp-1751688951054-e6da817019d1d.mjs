// vite.config.ts
import { fileURLToPath, URL } from "node:url";
import vue from "file:///Users/<USER>/work/fulfillmen/fulfillmen-shop-vue/node_modules/.pnpm/@vitejs+plugin-vue@4.6.2_vite@5.4.19_@types+node@22.15.18_sass-embedded@1.88.0_terser@5_da407d5887bd6b2c2ac3ecdc2cbde609/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import { defineConfig, loadEnv } from "file:///Users/<USER>/work/fulfillmen/fulfillmen-shop-vue/node_modules/.pnpm/vite@5.4.19_@types+node@22.15.18_sass-embedded@1.88.0_terser@5.39.1/node_modules/vite/dist/node/index.js";
import { createHtmlPlugin } from "file:///Users/<USER>/work/fulfillmen/fulfillmen-shop-vue/node_modules/.pnpm/vite-plugin-html@3.2.2_vite@5.4.19_@types+node@22.15.18_sass-embedded@1.88.0_terser@5.39.1_/node_modules/vite-plugin-html/dist/index.mjs";
import vueDevTools from "file:///Users/<USER>/work/fulfillmen/fulfillmen-shop-vue/node_modules/.pnpm/vite-plugin-vue-devtools@7.7.6_rollup@4.40.2_vite@5.4.19_@types+node@22.15.18_sass-embe_19a92cf1b4a840e77705d9d617320753/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";
var __vite_injected_original_import_meta_url = "file:///Users/<USER>/work/fulfillmen/fulfillmen-shop-vue/vite.config.ts";
var vite_config_default = defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd());
  const isJavaIntegration = mode === "java";
  console.log("\u5F53\u524D\u6784\u5EFA\u6A21\u5F0F:", mode);
  console.log("API\u76EE\u6807\u5730\u5740:", env.VITE_API_TARGET || "http://localhost:8080");
  const config = {
    plugins: [vue(), vueDevTools(), createHtmlPlugin({})],
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", __vite_injected_original_import_meta_url))
      },
      extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue"]
    },
    server: {
      host: "0.0.0.0",
      port: 5173,
      allowedHosts: env.VITE_ALLOWED_HOSTS ? env.VITE_ALLOWED_HOSTS.split(",") : [".sealoshzh.site", ".1688hub.com"],
      proxy: {
        "/api": {
          target: env.VITE_API_TARGET || "http://localhost:8080",
          changeOrigin: true,
          // 根据后端API的设计选择是否重写路径
          // 情况1: 如果后端API不期望收到/api前缀，取消下面注释
          //   rewrite: path => path.replace(/^\/api/, ''),
          // 情况2: 如果后端API期望收到/api前缀，保持注释状态
          configure: (proxy, options) => {
            console.log("API\u4EE3\u7406\u76EE\u6807\u5730\u5740:", options.target);
          }
        }
      }
    },
    build: {
      sourcemap: true
    }
  };
  if (isJavaIntegration) {
    console.log("\u4F7F\u7528Java\u540E\u7AEF\u96C6\u6210\u6A21\u5F0F\u8FDB\u884C\u6784\u5EFA...");
    config.build = {
      outDir: "../fulfillmen-shop-bootstrap/src/main/resources/static",
      emptyOutDir: true,
      assetsDir: "assets",
      sourcemap: true,
      rollupOptions: {
        output: {
          // JS文件输出格式
          chunkFileNames: "assets/js/[name]-[hash].js",
          entryFileNames: "assets/js/[name]-[hash].js",
          // 其他静态资源输出格式
          assetFileNames: "assets/[ext]/[name]-[hash].[ext]"
        }
      }
    };
  } else {
    console.log("\u4F7F\u7528\u9ED8\u8BA4\u6A21\u5F0F\u8FDB\u884C\u6784\u5EFA...");
    config.build = {
      sourcemap: true
    };
  }
  return config;
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
