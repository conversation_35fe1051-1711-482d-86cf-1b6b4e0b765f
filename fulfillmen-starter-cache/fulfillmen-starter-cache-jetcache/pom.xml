<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fulfillmen.starter</groupId>
        <artifactId>fulfillmen-starter-cache</artifactId>
        <version>1.2.6-SNAPSHOT</version>
<!--        <relativePath>../pom.xml</relativePath>-->
    </parent>
    <artifactId>fulfillmen-starter-cache-jetcache</artifactId>
    <name>Fulfillmen Starter 缓存模块 - JetCache</name>
    <packaging>jar</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- JetCache（一个基于 Java 的缓存系统封装，提供统一的 API 和注解来简化缓存的使用。提供了比 SpringCache 更加强大的注解，可以原生的支持 TTL、两级缓存、分布式自动刷新，还提供了 Cache 接口用于手工缓存操作） -->
        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-autoconfigure</artifactId>
        </dependency>

        <!-- JetCache 注解 -->
        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-anno</artifactId>
        </dependency>

        <!-- JetCache Redisson 适配 -->
        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-redisson</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>redisson</artifactId>
                    <groupId>org.redisson</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>

        <!-- 缓存模块 - Redisson -->
        <dependency>
            <groupId>com.fulfillmen.starter</groupId>
            <artifactId>fulfillmen-starter-cache-redisson</artifactId>
        </dependency>

        <!-- Jackson 支持 - 不传递依赖 -->
        <dependency>
            <groupId>com.fulfillmen.starter</groupId>
            <artifactId>fulfillmen-starter-json-jackson</artifactId>
            <optional>true</optional>
        </dependency>

    </dependencies>
</project>
