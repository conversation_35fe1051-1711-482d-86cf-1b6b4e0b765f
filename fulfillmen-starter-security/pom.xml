<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <parent>
        <groupId>com.fulfillmen.starter</groupId>
        <artifactId>fulfillmen-starter-parent</artifactId>
        <version>1.2.6-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>fulfillmen-starter-security</artifactId>
    <name>Fulfillmen Starter 安全模块 ${project.version}</name>
    <description>
        安全模块
    </description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <modules>
        <module>fulfillmen-starter-security-crypto</module>
        <module>fulfillmen-starter-security-limiter</module>
        <module>fulfillmen-starter-security-mask</module>
        <module>fulfillmen-starter-security-password</module>
    </modules>

    <dependencies>
        <!-- 核心模块 -->
        <dependency>
            <groupId>com.fulfillmen.starter</groupId>
            <artifactId>fulfillmen-starter-core</artifactId>
        </dependency>
    </dependencies>

</project>
