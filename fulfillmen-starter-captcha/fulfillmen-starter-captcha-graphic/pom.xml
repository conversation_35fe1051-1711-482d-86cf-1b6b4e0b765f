<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fulfillmen.starter</groupId>
        <artifactId>fulfillmen-starter-captcha</artifactId>
        <version>1.2.6-SNAPSHOT</version>
    </parent>
    <artifactId>fulfillmen-starter-captcha-graphic</artifactId>
    <packaging>jar</packaging>
    <name>Fulfillmen Starter 验证码模块 - 图形验证码</name>
    <description>Fulfillmen Starter 验证码模块 - 图形验证码</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- Easy Captcha（Java 图形验证码，支持 gif、中文、算术等类型，可用于 Java Web、JavaSE 等项目） -->
        <dependency>
            <groupId>com.github.whvcse</groupId>
            <artifactId>easy-captcha</artifactId>
        </dependency>

        <!-- JS 引擎（一个纯编译的 JavaScript 引擎） -->
        <dependency>
            <groupId>org.openjdk.nashorn</groupId>
            <artifactId>nashorn-core</artifactId>
        </dependency>
    </dependencies>
</project>
