/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 采购订单表-统一管理跨供应商采购
 *
 * <AUTHOR>
 * @date 2025/6/25 15:11
 * @description: todo
 * @since 1.0.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "tz_order_purchase")
public class TzOrderPurchase implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 商品总金额
     */
    @TableField(value = "goods_amount")
    private BigDecimal goodsAmount;

    /**
     * 总运费
     */
    @TableField(value = "total_freight")
    private BigDecimal totalFreight;

    /**
     * 采购订单编号
     */
    @TableField(value = "purchase_order_no")
    private String purchaseOrderNo;

    /**
     * 采购商ID
     */
    @TableField(value = "buyer_id")
    private Long buyerId;

    /**
     * 订单状态: 0待支付/1部分支付/2已支付/3部分履约/4已履约/5部分完成/6已完成/7已取消
     */
    @TableField(value = "order_status")
    private TzOrderPurchaseStatusEnum orderStatus;

    /**
     * 下单日期
     */
    @TableField(value = "order_date")
    private LocalDateTime orderDate;

    /**
     * 支付完成日期
     */
    @TableField(value = "payment_date")
    private LocalDateTime paymentDate;

    /**
     * 订单完成日期
     */
    @TableField(value = "completion_date")
    private LocalDateTime completionDate;

    /**
     * 服务费，整单的服务费价格。通过 tenant_info.service_fee 获取比例。
     */
    @TableField(value = "service_fee")
    private BigDecimal serviceFee;

    /**
     * 记录当前交易汇率。
     */
    @TableField(value = "exchange_rate")
    private BigDecimal exchangeRate;

    /**
     * 总折扣
     */
    @TableField(value = "total_discount")
    private BigDecimal totalDiscount;

    /**
     * 订单总金额
     */
    @TableField(value = "total_amount")
    private BigDecimal totalAmount;

    /**
     * 实付金额
     */
    @TableField(value = "finsh_pay_amount")
    private BigDecimal finshPayAmount;

    /**
     * 总税费
     */
    @TableField(value = "total_tax")
    private BigDecimal totalTax;

    /**
     * 订单净金额
     */
    @TableField(value = "net_amount")
    private BigDecimal netAmount;

    /**
     * 收货地址详情
     */
    @TableField(value = "delivery_address")
    private String deliveryAddress;

    /**
     * 邮政编码
     */
    @TableField(value = "postal_code")
    private String postalCode;

    /**
     * 国家代码
     */
    @TableField(value = "country_code")
    private String countryCode;

    /**
     * 省份
     */
    @TableField(value = "province")
    private String province;

    /**
     * 城市
     */
    @TableField(value = "city")
    private String city;

    /**
     * 区县
     */
    @TableField(value = "district")
    private String district;

    /**
     * 收货人
     */
    @TableField(value = "consignee_name")
    private String consigneeName;

    /**
     * 收货人电话
     */
    @TableField(value = "consignee_phone")
    private String consigneePhone;

    /**
     * 供应商数量
     */
    @TableField(value = "supplier_count")
    private Integer supplierCount;

    /**
     * 订单行数
     */
    @TableField(value = "line_item_count")
    private Integer lineItemCount;

    /**
     * 商品总数量
     */
    @TableField(value = "total_quantity")
    private Integer totalQuantity;

    /**
     * 采购备注
     */
    @TableField(value = "purchase_notes")
    private String purchaseNotes;

    /**
     * 租户ID
     */
    @TableField(value = "tenant_id")
    private Long tenantId;

    /**
     * 是否删除
     */
    @TableField(value = "is_deleted")
    private Long isDeleted;

    /**
     * 数据版本
     */
    @Version
    @TableField(value = "revision")
    private Integer revision;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified")
    private LocalDateTime gmtModified;
}
