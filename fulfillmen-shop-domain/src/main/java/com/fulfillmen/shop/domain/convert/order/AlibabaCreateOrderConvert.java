/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.convert.order;

import com.fulfillmen.shop.domain.dto.order.CreateOrderRespDTO;
import com.fulfillmen.shop.domain.dto.order.CreateOrderRespDTO.AlibabaCreateOrderRespDTO;
import com.fulfillmen.shop.domain.dto.order.CreateOrderRespDTO.AlibabaFailedOffer;
import com.fulfillmen.support.alibaba.api.response.order.OrderCreateResponse.FailedOffer;
import com.fulfillmen.support.alibaba.api.response.order.OrderCreateResponse.Order;
import com.fulfillmen.support.alibaba.api.response.order.OrderCreateResponse.OrderCreateResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/7/1 14:55
 * @description: todo
 * @since 1.0.0
 */
@Mapper
public interface AlibabaCreateOrderConvert {

    AlibabaCreateOrderConvert INSTANCE = Mappers.getMapper(AlibabaCreateOrderConvert.class);

    /**
     * 转换订单
     *
     * @param order 订单
     * @return CreateOrderRespDTO.AlibabaCreateOrderRespDTO
     */
    AlibabaCreateOrderRespDTO convertAlibabaCreateOrderRespDTO(Order order);

    /**
     * 转换失败商品
     *
     * @param failedOffer 失败商品
     * @return CreateOrderRespDTO.AlibabaFailedOffer
     */
    AlibabaFailedOffer convertAlibabaFailedOffer(FailedOffer failedOffer);

    /**
     * 转换订单创建结果
     *
     * @param orderCreateResult 订单创建结果
     * @return CreateOrderRespDTO.AlibabaCreateOrderRespDTO
     */
    @Mapping(target = "isMultipleOrder", expression = "java(orderCreateResult.getOrderList() != null && !orderCreateResult.getOrderList().isEmpty())")
    CreateOrderRespDTO convertAlibabaCreateOrderRespDTO(OrderCreateResult orderCreateResult);
}
