/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzProductSpuSingleItemEnum;
import com.fulfillmen.shop.domain.entity.json.AttrJson;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 订单项表-具体商品采购明细
 *
 * <AUTHOR>
 * @date 2025/6/25 15:11
 * @description: todo
 * @since 1.0.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "tz_order_item", autoResultMap = true)
public class TzOrderItem implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 采购订单ID
     */
    @TableField(value = "purchase_order_id")
    private Long purchaseOrderId;

    /**
     * 供应商订单ID
     */
    @TableField(value = "supplier_order_id")
    private Long supplierOrderId;

    /**
     * 行号
     */
    @TableField(value = "line_number")
    private Integer lineNumber;

    /**
     * 商品SPU ID
     */
    @TableField(value = "product_spu_id")
    private Long productSpuId;

    /**
     * 商品SKU ID
     */
    @TableField(value = "product_sku_id")
    private Long productSkuId;

    /**
     * 平台商品ID
     */
    @TableField(value = "platform_product_id")
    private String platformProductId;

    /**
     * 平台SKU ID
     */
    @TableField(value = "platform_sku_id")
    private String platformSkuId;

    /**
     * 平台 specId
     */
    @TableField(value = "platform_spec_id")
    private String platformSpecId;

    /**
     * 商品标题
     */
    @TableField(value = "product_title")
    private String productTitle;

    /**
     * 商品英文标题
     */
    @TableField(value = "product_title_en")
    private String productTitleEn;

    /**
     * SKU规格属性
     */
    @TableField(value = "sku_specs", typeHandler = JacksonTypeHandler.class)
    private List<AttrJson> skuSpecs;

    /**
     * 商品图片
     */
    @TableField(value = "product_image_url")
    private String productImageUrl;

    /**
     * 单价
     */
    @TableField(value = "price")
    private BigDecimal price;

    /**
     * 订购数量
     */
    @TableField(value = "quantity")
    private Integer quantity;

    /**
     * 行总金额
     */
    @TableField(value = "line_total_amount")
    private BigDecimal lineTotalAmount;

    /**
     * 计量单位
     */
    @TableField(value = "unit")
    private String unit;

    /**
     * 计量单位(英文)
     */
    @TableField(value = "unit_en")
    private String unitEn;

    /**
     * 状态：0
     */
    @TableField(value = "`status`")
    private TzOrderItemStatusEnum status;

    /**
     * 当 status =1 ，下单的错误码
     */
    @TableField(value = "error_code")
    private Integer errorCode;

    /**
     * 当 status =1 ，下单的错误描述
     */
    @TableField(value = "error_message")
    private String errorMessage;

    /**
     * 外部平台订单行ID(字段无用，后续处理)
     */
    @TableField(value = "external_line_item_id")
    private String externalLineItemId;

    /**
     * 是否单品，0 否，1 是。 如果是单品的，创建订单的时候只需要传 offerId ，不需要 specId
     */
    @TableField(value = "is_single_item")
    private TzProductSpuSingleItemEnum isSignleItem;

    /**
     * 租户ID
     */
    @TableField(value = "tenant_id")
    private Long tenantId;

    /**
     * 是否删除
     */
    @TableField(value = "is_deleted")
    private Long isDeleted;

    /**
     * 数据版本
     */
    @Version
    @TableField(value = "revision")
    private Integer revision;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified")
    private LocalDateTime gmtModified;
}
