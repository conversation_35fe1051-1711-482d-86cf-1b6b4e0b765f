/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 采购订单状态枚举
 *
 * <pre>
 * 采购订单完整状态流转：
 *
 * 0. 临时保存 (Temporarily Save Orders)
 * - 询价需要代付款商品，数据同步到 WMS 或管理后台
 * - 账户余额不足代付款商品
 *
 * 1. 待支付 (Payment Pending)
 * - 订单已确认，等待客户支付
 *
 * 2. 支付完成 (Payment Completed)
 * - 客户支付成功，余额已扣减
 *
 * 3. 待采购 (Purchase Pending)
 * - 支付完成后，等待平台财务进行采购
 *
 * 4. 供应商已发货 (Supplier Shipped)
 * - 平台完成采购，供应商已发货至仓库
 *
 * 5. 仓库待收货 (Warehouse Pending Received)
 * - 货物在途中，等待仓库接收
 *
 * 6. 仓库已收货 (Warehouse Received)
 * - WMS 仓库确认收到货物
 *
 * 7. 已入库 (In Stock)
 * - 货物质检完成并上架，准备发给客户
 *
 * 8. 已取消 (Order Cancelled)
 * - 订单在完成前被取消
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/6/27
 * @description 采购订单状态枚举
 * @since 1.0.0
 */
@Getter
public enum TzOrderPurchaseStatusEnum {

    /**
     * 临时保存
     *
     * <pre>
     * 订单临时保存状态，适用于以下情况：
     * - 询价商品需要代付款，订单保存等待进一步处理
     * - 客户余额不足，订单保存等待充值
     * - 数据会同步到 WMS 或管理后台进行处理
     * </pre>
     */
    TEMPORARILY_SAVED(0, "临时保存", "Temporarily Save Orders"),

    /**
     * 待支付
     *
     * <pre>
     * 订单已确认商品和价格，等待客户完成支付
     * 可选支付方式：余额支付、Stripe支付、Alipay 等
     * </pre>
     */
    PAYMENT_PENDING(1, "待支付", "Payment Pending"),

    /**
     * 待审核
     *
     * <pre>
     * 客户支付成功，余额已扣减或第三方支付完成
     * 订单进入内部处理流程
     * </pre>
     */
    PAYMENT_COMPLETED(2, "支付完成", "Payment Completed"),

    /**
     * 待采购
     *
     * <pre>
     * 用户支付完成后，等待采购员进行下单处理
     * </pre>
     */
    PURCHASE_PENDING(2, "待采购", "Purchase Pending"),

    /**
     * 供应商已发货
     *
     * <pre>
     * 平台完成采购，供应商已将货物发往 WMS 仓库
     * 可能存在多个供应商分别发货的情况
     * </pre>
     */
    SUPPLIER_SHIPPED(4, "供应商已发货", "Supplier Shipped"),

    /**
     * 仓库待收货
     *
     * <pre>
     * 货物在运输途中，等待 WMS 仓库接收
     * 所有供应商都已发货，但仓库尚未完全接收
     * </pre>
     */
    WAREHOUSE_PENDING_RECEIVED(5, "仓库待收货", "Warehouse Pending Received"),

    /**
     * 仓库已收货
     *
     * <pre>
     * WMS 仓库确认收到所有货物
     * 货物进入质检和上架流程
     * </pre>
     */
    WAREHOUSE_RECEIVED(6, "仓库已收货", "Warehouse Received"),

    /**
     * 已入库
     *
     * <pre>
     * 货物质检完成并成功上架
     * 可以开始准备发货给最终客户
     * 这是采购订单的最终完成状态
     * </pre>
     */
    IN_STOCK(7, "已入库", "In Stock"),

    /**
     * 已取消
     *
     * <pre>
     * 订单在完成前被取消
     * 可能发生在支付前、采购前或发货前
     * 需要根据取消时点处理退款
     * </pre>
     */
    ORDER_CANCELLED(8, "已取消", "Order Cancelled");

    @EnumValue
    @JsonValue
    private final Integer value;

    /**
     * 中文描述
     */
    private final String description;

    /**
     * 英文描述
     */
    private final String englishDescription;

    TzOrderPurchaseStatusEnum(Integer value, String description, String englishDescription) {
        this.value = value;
        this.description = description;
        this.englishDescription = englishDescription;
    }

    @JsonCreator
    public static TzOrderPurchaseStatusEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (TzOrderPurchaseStatusEnum status : TzOrderPurchaseStatusEnum.values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据中文描述获取枚举
     */
    public static TzOrderPurchaseStatusEnum getByDescription(String description) {
        if (description == null) {
            return null;
        }
        for (TzOrderPurchaseStatusEnum status : TzOrderPurchaseStatusEnum.values()) {
            if (status.getDescription().equals(description)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据英文描述获取枚举
     */
    public static TzOrderPurchaseStatusEnum getByEnglishDescription(String englishDescription) {
        if (englishDescription == null) {
            return null;
        }
        for (TzOrderPurchaseStatusEnum status : TzOrderPurchaseStatusEnum.values()) {
            if (status.getEnglishDescription().equals(englishDescription)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否为可支付状态
     */
    public boolean isPayable() {
        return this == TEMPORARILY_SAVED || this == PAYMENT_PENDING;
    }

    /**
     * 判断是否为可取消状态
     */
    public boolean isCancellable() {
        return this != IN_STOCK && this != ORDER_CANCELLED;
    }

    /**
     * 判断是否为已完成状态
     */
    public boolean isCompleted() {
        return this == IN_STOCK;
    }

    /**
     * 判断是否为进行中状态
     */
    public boolean isInProgress() {
        return this == PAYMENT_COMPLETED || this == PURCHASE_PENDING ||
            this == SUPPLIER_SHIPPED || this == WAREHOUSE_PENDING_RECEIVED ||
            this == WAREHOUSE_RECEIVED;
    }
}
