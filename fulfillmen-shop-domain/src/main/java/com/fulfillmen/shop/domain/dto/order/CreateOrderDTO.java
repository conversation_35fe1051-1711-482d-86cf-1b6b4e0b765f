/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.dto.order;

import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 创建订单
 *
 * <AUTHOR>
 * @date 2025/6/30 14:54
 * @description: 创建订单传输对象
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateOrderDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 采购订单
     */
    private TzOrderPurchase purchaseOrder;

    /**
     * 供应商订单
     *
     * <pre>
     * 按店铺或供应商拆分出子订单
     * </pre>
     */
    private List<TzOrderSupplier> supplierOrders;

    /**
     * 订单项
     */
    private List<TzOrderItem> orderItems;

    /**
     * 是否来自购物车 0: 否 , 1: 是
     */
    private Integer isShoppingCart;

    /**
     * 用户 ID，TzUser.id
     */
    private Long buyerId;
    /**
     * 购物车ID 多个ID用逗号分隔，需要删除的购物车 Id 列表。如果 null 则不删除。
     */
    private String shoppingCartIds;

}
