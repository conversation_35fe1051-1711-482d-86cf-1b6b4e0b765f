/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.dto.order;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 创建订单响应结果
 *
 * <AUTHOR>
 * @date 2025/7/1 14:47
 * @description: todo
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateOrderRespDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单总金额（单位分） 注：一次创建多个订单时，该字段为空 示例值：100
     */
    private Long totalSuccessAmount;

    /**
     * 订单ID 注：一次创建多个订单时，该字段为空 示例值：*********
     */
    private String orderId;

    /**
     * 运费，单位：分 注：一次创建多个订单时，该字段为空
     */
    private Long postFee;

    /**
     * 账期信息对象 注：非账期支付订单返回空
     */
    private AlibabaAccountPeriod accountPeriod;

    /**
     * 失败商品信息列表 记录下单失败的商品详情
     */
    private List<AlibabaFailedOffer> failedOfferList;

    /**
     * 订单列表 注：一次创建多个订单时返回
     */
    private List<AlibabaCreateOrderRespDTO> orderList;

    /**
     * 是否产生了多个订单id,注：同一供应商，创建订单平台可能分配多个订单 ID。 默认 false 否，true 是
     * <pre>
     * orderList 不为空时，表示产生了多个订单
     * </pre>
     */
    private Boolean isMultipleOrder;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AlibabaAccountPeriod implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 账期类型 1：一个月指定日期结算一次 3：两个月指定日期结算一次 6：三个月指定日期结算一次 5：按收货时间和账期日期结算 示例值：1
         */
        private Integer tapType;

        /**
         * 账期结算日期 - 按月结算类型此值代表具体某日 - 按收货时间结算时此值代表结算时间周期 示例值：12
         */
        private Integer tapDate;

        /**
         * 逾期次数 示例值：0
         */
        private Integer tapOverdue;
    }

    /**
     * 1688下单失败商品DTO
     *
     * <AUTHOR>
     * @date 2025/7/1 14:49
     * @description: todo
     * @since 1.0.0
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AlibabaFailedOffer implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;
        /**
         * 下单失败的商品ID 示例值：************
         */
        private String offerId;

        /**
         * 下单失败的商品规格ID 示例值：b26be072650b185beaf205cbae88530d
         */
        private String specId;

        /**
         * 下单失败的错误编码
         */
        private String errorCode;

        /**
         * 下单失败的错误描述
         */
        private String errorMessage;
    }

    /**
     * 1688创建订单响应DTO
     *
     * <AUTHOR>
     * @date 2025/7/1 14:49
     * @description: todo
     * @since 1.0.0
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AlibabaCreateOrderRespDTO implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;
        /**
         * 运费（单位：分）
         */
        private Long postFee;

        /**
         * 订单实付款金额（单位：分）
         */
        private Long orderAmount;

        /**
         * 描述信息
         */
        private String message;

        /**
         * 返回码
         */
        private String resultCode;

        /**
         * 是否成功
         */
        private Boolean success;

        /**
         * 订单号
         */
        private String orderId;

    }
}
