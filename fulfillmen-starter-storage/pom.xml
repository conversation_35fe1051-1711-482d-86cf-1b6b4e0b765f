<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fulfillmen.starter</groupId>
        <artifactId>fulfillmen-starter-parent</artifactId>
        <version>1.2.6-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>fulfillmen-starter-storage</artifactId>
    <name>Fulfillmen Starter 存储模块 ${project.version}</name>
    <modules>
        <module>fulfillmen-starter-storage-core</module>
        <module>fulfillmen-starter-storage-local</module>
        <module>fulfillmen-starter-storage-oss</module>
    </modules>
    <packaging>pom</packaging>

    <description>
        文件存储模块
    </description>



    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- 核心模块 -->
        <dependency>
            <groupId>com.fulfillmen.starter</groupId>
            <artifactId>fulfillmen-starter-core</artifactId>
        </dependency>
    </dependencies>

    <build>
    <plugins>
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-resources-plugin</artifactId>
            <version>3.3.1</version>
            <configuration>
                <nonFilteredFileExtensions>
                    <nonFilteredFileExtension>png</nonFilteredFileExtension>
                    <nonFilteredFileExtension>jpg</nonFilteredFileExtension>
                    <nonFilteredFileExtension>pdf</nonFilteredFileExtension>
                </nonFilteredFileExtensions>
            </configuration>
        </plugin>
    </plugins>
</build>
</project>
