<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fulfillmen.starter</groupId>
        <artifactId>fulfillmen-starter-storage</artifactId>
        <version>1.2.6-SNAPSHOT</version>
    </parent>

    <artifactId>fulfillmen-starter-storage-oss</artifactId>
    <packaging>jar</packaging>
    <name>Fulfillmen Starter 存储模块 - OSS Storage ${project.version}</name>
    <description>Fulfillmen Starter 存储模块 - OSS Storage</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!--存储 - 核心模块-->
        <dependency>
            <groupId>com.fulfillmen.starter</groupId>
            <artifactId>fulfillmen-starter-storage-core</artifactId>
        </dependency>
        <!--  S3 SDK for Java 2.x  -->
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>s3</artifactId>
            <exclusions>
                <!-- 基于 Netty 的 HTTP 客户端移除 -->
                <exclusion>
                    <groupId>software.amazon.awssdk</groupId>
                    <artifactId>netty-nio-client</artifactId>
                </exclusion>
                <!-- 基于 CRT 的 HTTP 客户端移除 -->
                <exclusion>
                    <groupId>software.amazon.awssdk</groupId>
                    <artifactId>aws-crt-client</artifactId>
                </exclusion>
                <!-- 基于 Apache 的 HTTP 客户端移除 -->
                <exclusion>
                    <groupId>software.amazon.awssdk</groupId>
                    <artifactId>apache-client</artifactId>
                </exclusion>
                <!-- 配置基于 URL 连接的 HTTP 客户端移除 -->
                <exclusion>
                    <groupId>software.amazon.awssdk</groupId>
                    <artifactId>url-connection-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 使用AWS基于 CRT 的 S3 客户端 -->
        <dependency>
            <groupId>software.amazon.awssdk.crt</groupId>
            <artifactId>aws-crt</artifactId>
        </dependency>

        <!-- 基于 AWS CRT 的 S3 客户端的性能增强的 S3 传输管理器 -->
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>s3-transfer-manager</artifactId>
        </dependency>

        <!-- AWS SDK HTTP客户端实现 - Netty -->
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>netty-nio-client</artifactId>
<!--            <version>${aws.sdk.version}</version>-->
        </dependency>

        <!-- TestContainers 测试依赖 -->
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>testcontainers</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
